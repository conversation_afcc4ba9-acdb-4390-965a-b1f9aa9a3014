{"name": "otto-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --experimental-https", "test": "jest", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/pro-regular-svg-icons": "^6.7.2", "@fortawesome/pro-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@next/third-parties": "^15.1.7", "@nrk/nativebridge": "^1.1.9", "@react-oauth/google": "^0.12.1", "@sentry/nextjs": "^8.48.0", "@uiw/react-json-view": "^2.0.0-alpha.30", "@vgs/collect-js": "^0.6.3", "@vgs/collect-js-react": "^1.2.0", "@vis.gl/react-google-maps": "^1.4.3", "dayjs": "^1.11.13", "i18n-iso-countries": "^7.14.0", "jotai": "^2.12.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "mic-recorder-to-mp3": "^2.2.2", "next": "^14.2.23", "primereact": "^10.9.6", "quill": "^2.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "react-markdown": "^9.0.3", "react-select": "^5.10.1", "react-tooltip": "^5.28.0", "react-use-websocket": "^4.13.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "sharp": "^0.33.5", "swr": "^2.3.3", "turndown": "^7.2.0"}, "devDependencies": {"@types/google.accounts": "^0.0.15", "@types/google.maps": "^3.58.1", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/node": "^20.16.11", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@types/turndown": "^5.0.5", "autoprefixer": "^10.4.20", "cheerio": "^1.0.0", "clsx": "^2.1.1", "csv-stringify": "^6.5.2", "eslint": "^8.57.1", "eslint-config-next": "^14.2.14", "jest": "^29.7.0", "node-fetch": "^3.3.2", "postcss": "^8.4.49", "sass": "^1.79.4", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "ts-jest": "^29.2.5", "typescript": "^5.6.2"}}