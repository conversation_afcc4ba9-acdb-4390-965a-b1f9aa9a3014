import { NextResponse, type NextRequest } from "next/server";
import {
  COOKIE_ACCESS_TOKEN_NAME,
  COOKIE_OTC_NAME,
  COOKIE_RETURN_PATH_NAME,
  ROUTES,
} from "./common/constants";
import { getParentDomain } from "./common/utils/env";

export function middleware(request: NextRequest) {
  const { pathname, search, searchParams } = request.nextUrl;
  const otc = searchParams.get(COOKIE_OTC_NAME);
  const isAuthenticated = request.cookies.has(COOKIE_ACCESS_TOKEN_NAME);
  const hasReturnPath = request.cookies.has(COOKIE_RETURN_PATH_NAME);
  const loginRoutes = [ROUTES.LOGIN, ROUTES.LOGIN_DENIED];
  let response: NextResponse;

  if (isAuthenticated && hasReturnPath) {
    response = NextResponse.redirect(
      new URL(
        decodeURIComponent(
          request.cookies.get(COOKIE_RETURN_PATH_NAME)?.value || ROUTES.HOME
        ),
        request.url
      )
    );
    response.cookies.delete(COOKIE_RETURN_PATH_NAME);
  } else if (loginRoutes.includes(pathname) && isAuthenticated) {
    response = NextResponse.redirect(new URL(ROUTES.HOME, request.url));
  } else if (!loginRoutes.includes(pathname) && !isAuthenticated) {
    const returnPath = `${pathname}${search}`;
    response = NextResponse.redirect(new URL(ROUTES.LOGIN, request.url));

    if (pathname === ROUTES.HOME) {
      response.cookies.delete(COOKIE_RETURN_PATH_NAME);
    } else {
      response.cookies.set(
        COOKIE_RETURN_PATH_NAME,
        encodeURIComponent(returnPath),
        {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
          maxAge: 60 * 15, // 15 minutes
        }
      );
    }
  } else {
    response = NextResponse.next();
  }

  if (otc) {
    response.cookies.set(COOKIE_OTC_NAME, otc, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      domain: getParentDomain(),
    });
  }

  return response;
}

// The matcher values need to be constants so they can be statically analyzed at build-time.
// Dynamic values such as variables will be ignored.
export const config = {
  matcher: [
    "/",
    "/future-trips",
    "/login/:path*",
    "/admin-settings/:path*",
    "/login-denied",
    "/preferences",
    "/itineraries",
    "/trips/:slug*",
    "/onboarding",
    "/admin/:path*",
    "/user-preferences",
    "/user-travel-policy",
  ],
};
