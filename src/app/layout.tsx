import { ReactNode, Suspense } from "react";
import type { Metadata } from "next";
import Script from "next/script";
import { GoogleTagManager } from "@next/third-parties/google";
import dayjs from "dayjs";
import advancedFormat from "dayjs/plugin/advancedFormat";
import relativeTime from "dayjs/plugin/relativeTime";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import duration from "dayjs/plugin/duration";
import clsx from "clsx";

import { isDevelopment } from "@/common/utils/env";
import { Providers } from "@/common/components/providers";
import SmartAppBanner from "@/common/components/smart-app-banner";

import { inter } from "@/common/fonts";
import "react-tooltip/dist/react-tooltip.css";
import "../common/styles/globals.scss";

export const metadata: Metadata = {
  title: "Otto",
};

dayjs.extend(advancedFormat);
dayjs.extend(relativeTime);
dayjs.extend(timezone);
dayjs.extend(utc);
dayjs.extend(duration);

const RootLayout = ({
  children,
}: Readonly<{
  children: ReactNode;
}>) => {
  return (
    <html lang="en">
      <Script id="dark-mode-init">
        {`
          document.documentElement.classList.toggle(
          "dark",
          localStorage["dark-mode"] === "true" ||
            (!("dark-mode" in localStorage) &&
              window.matchMedia("(prefers-color-scheme: dark)").matches)
        );`}
      </Script>
      {!isDevelopment() && (
        <>
          {/* UserCentrics - All other 3rd party scripts requiring user consent should be added AFTER these scripts */}
          <Script
            src="https://web.cmp.usercentrics.eu/modules/autoblocker.js"
            strategy="beforeInteractive"
          />
          <Script
            id="usercentrics-cmp"
            src="https://web.cmp.usercentrics.eu/ui/loader.js"
            data-settings-id="7yRowFtqAnkxPO"
            async
            strategy="beforeInteractive"
          />
        </>
      )}
      {!!process.env.NEXT_PUBLIC_GTM_ID && (
        <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID} />
      )}
      <body
        className={clsx(inter.className, "dark:bg-gray-800 dark:text-white")}
        style={{ height: "100dvh" }}
      >
        <main className="h-full">
          <Providers
            googleClientId={process.env.GOOGLE_CLIENT_ID}
            googleMapsAPIKey={process.env.GOOGLE_MAPS_API_KEY}
          >
            <Suspense>
              {children}
              <SmartAppBanner />
            </Suspense>
          </Providers>
        </main>
      </body>
    </html>
  );
};

export default RootLayout;
