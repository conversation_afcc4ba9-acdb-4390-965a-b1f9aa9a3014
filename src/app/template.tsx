"use client";

import { ReactN<PERSON>, useCallback, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import useJ<PERSON><PERSON> from "@/features/js-bridge/hooks/use-bridge";
import { use<PERSON>tom, useSet<PERSON>tom } from "jotai";
import cookies from "js-cookie";
import { COOKIE_ACCESS_TOKEN_NAME, ROUTES } from "@/common/constants";
import {
  isNativeBridgeAtom,
  nativeWSDebugAtom,
} from "@/common/store/nativebridge";
import { showLoginLoaderAtom } from "@/features/user/store/login";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";
import { useSessionHistory, useUserProfile } from "@/features/user/hooks/api";
import { Toast, ToastMessage } from "primereact/toast";
import useToast from "@/common/hooks/use-toast";
import usePushNotificationsPopup from "@/common/hooks/use-push-notifications-popup";
import { toastsQue<PERSON><PERSON><PERSON> } from "@/common/store/toast";
import arrayHasElements from "@/common/utils/array-has-elements";
import { useMonitoring } from "@/common/hooks/use-monitoring";
import {
  calendarIntegrationModalAtom,
  chatHistoryAtom,
  showSkeletonAtom,
  suggestedCapabilityAtom,
} from "@/features/chat/store/chat";
import { travelContextAtom } from "@/features/chat/store/debugging";
import { tripDetailsAtom } from "@/features/chat/store/current-trip";
import useOnNavigation from "@/common/hooks/use-on-navigation";
import { useTripsList } from "@/common/hooks/api";
import "../common/utils/fontawesome";
import {
  unbookedFlightAtom,
  unbookedHotelAtom,
} from "@/features/itineraries/store/unbooked";

export default function MainTemplate({ children }: { children: ReactNode }) {
  const router = useRouter();

  const [isNativeBridge, setIsNativebridge] = useAtom(isNativeBridgeAtom);
  const [nativeWSDebug, setNativeWSDebug] = useAtom(nativeWSDebugAtom);
  const setMessagesList = useSetAtom(chatHistoryAtom);
  const setShowSkeleton = useSetAtom(showSkeletonAtom);
  const setTravelContext = useSetAtom(travelContextAtom);
  const setTripDetails = useSetAtom(tripDetailsAtom);

  const isAuthenticated = !!cookies.get(COOKIE_ACCESS_TOKEN_NAME);
  const userProfileInit = useRef<boolean>(false);
  const tripsListInit = useRef<boolean>(false);
  const sessionHistoryInit = useRef<boolean>(false);
  const { mutate: mutateSessionHistory } = useSessionHistory();
  const showLoginLoader = useSetAtom(showLoginLoaderAtom);
  const { isLoading: onboardingRedirectLoading, onboardingRedirect } =
    useOnboardingRedirect();
  const { ref: toastRef, showErrorToast } = useToast();
  const isBridgeInitialized = useRef(false);
  const initialiseJSBridge = useJSBridge();
  const { mutateUserProfile } = useUserProfile();
  const { error: tripsListError, mutate: mutateTripsList } = useTripsList();
  const [queue, setQueue] = useAtom(toastsQueueAtom);
  const { openPushNotificationsPopup } = usePushNotificationsPopup();
  const setSelectedFlight = useSetAtom(unbookedFlightAtom);
  const setSelectedHotel = useSetAtom(unbookedHotelAtom);
  const setSuggestedCapability = useSetAtom(suggestedCapabilityAtom);
  const setCalendarIntegrationModal = useSetAtom(calendarIntegrationModalAtom);

  useMonitoring();

  useEffect(() => {
    if (!isBridgeInitialized.current && !onboardingRedirectLoading) {
      initialiseJSBridge({
        onInit: () => !isNativeBridge && setIsNativebridge(true),
        // onNewTripNotification: () => router.push(ROUTES.FUTURE_TRIPS),
        onPostLogin: () => {
          onboardingRedirect();
          mutateUserProfile();
          !tripsListError &&
            mutateTripsList().catch((error) =>
              showErrorToast(error, "Trips list request failed.")
            );
        },
        onLoginAccessDenied: () => router.push(ROUTES.LOGIN_DENIED),
        onShowLoader: () => showLoginLoader(true),
        onHideLoader: () => showLoginLoader(false),
        onWSDebug: () => !nativeWSDebug && setNativeWSDebug(true),
        onDisplayPushNotifications: openPushNotificationsPopup,
      });
      isBridgeInitialized.current = true;
    }
  }, [
    initialiseJSBridge,
    isNativeBridge,
    onboardingRedirectLoading,
    mutateTripsList,
    mutateUserProfile,
    nativeWSDebug,
    onboardingRedirect,
    router,
    setIsNativebridge,
    setNativeWSDebug,
    showErrorToast,
    showLoginLoader,
    tripsListError,
    openPushNotificationsPopup,
  ]);

  useEffect(() => {
    if (isAuthenticated) {
      if (!userProfileInit.current) {
        userProfileInit.current = true;
        mutateUserProfile();
      }
      if (!tripsListInit.current) {
        tripsListInit.current = true;
        mutateTripsList();
      }
      if (!sessionHistoryInit.current) {
        sessionHistoryInit.current = true;
        mutateSessionHistory();
      }
    }
  });

  useEffect(() => {
    if (!!toastRef.current?.getElement() && arrayHasElements(queue)) {
      queue.forEach((message) => toastRef.current?.show(message));
      setQueue([]);
    }
  }, [toastRef, queue, setQueue]);

  useEffect(() => {
    if (!!toastRef.current?.getElement()) {
      const localStorageToastsQueue = window.localStorage.getItem("toasts");
      if (localStorageToastsQueue) {
        (JSON.parse(localStorageToastsQueue) as ToastMessage[]).map((toast) =>
          toastRef.current?.show(toast)
        );
        window.localStorage.removeItem("toasts");
      }
    }
  }, [toastRef]);

  const onNavigationCallback = useCallback(
    (url: string) => {
      // Preserve chat history when returning from calendar auth
      if (url.includes(ROUTES.ONBOARDING) || url.includes(ROUTES.PREFERENCES)) {
        return;
      }
      setCalendarIntegrationModal(false);
      setMessagesList([]);
      setShowSkeleton(null);
      setTravelContext(null);
      setTripDetails({ isLoading: true });
      setSelectedFlight(null);
      setSelectedHotel(null);
      setSuggestedCapability(null);
    },
    [
      setCalendarIntegrationModal,
      setMessagesList,
      setSelectedFlight,
      setSelectedHotel,
      setShowSkeleton,
      setSuggestedCapability,
      setTravelContext,
      setTripDetails,
    ]
  );
  useOnNavigation(onNavigationCallback);

  return (
    <>
      <Toast ref={toastRef} />
      {children}
    </>
  );
}
