import { ReactNode } from "react";
import clsx from "clsx";
import { vastagoGroteskMedium } from "@/common/fonts";

const RootLayout = ({
  children,
}: Readonly<{
  children: ReactNode;
}>) => {
  return (
    <div
      className={clsx(
        "bg-gradient-login from-primary-900 size-full to-gray-999 overflow-y-auto",
        vastagoGroteskMedium.className
      )}
    >
      <div className="flex flex-col h-full items-center pt-8 pb-2 px-6 xs:pt-24 xs:px-12 lg:max-w-240 lg:mx-auto">
        {children}
      </div>
    </div>
  );
};

export default RootLayout;
