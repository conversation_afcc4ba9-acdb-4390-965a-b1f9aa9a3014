"use client";

import { useEffect, useRef } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import Link from "next/link";
import { useAtomValue } from "jotai";
import clsx from "clsx";
import { Button } from "primereact/button";
import { inter } from "@/common/fonts";
import colors from "@/common/constants/colors";
import { useLogin } from "@/features/user/hooks/api";
import useToast from "@/common/hooks/use-toast";
import { useStoreToggle } from "@/common/hooks/toggles";
import { showLoginLoaderAtom } from "@/features/user/store/login";
import { isNativeBridgeAtom } from "@/common/store/nativebridge";
import { showSidebarAtom } from "@/common/store/sidebar";
import Logo from "@/common/components/logo";
import LoadingSpinner from "@/common/components/loading-spinner";
import MicrosoftLogo from "@/common/components/icons/microsoft-logo";
import Image from "next/image";
import appleLogo from "../../../../public/images/apple-logo.svg";
import { isProduction } from "@/common/utils";
import { ROUTES } from "@/common/constants";
import { OneTimePassword } from "@/features/login/components/one-time-password";
import { useIsTryingMode } from "@/common/hooks/use-trying-mode";
import { GoogleLogin } from "@/features/login/components/google-login";
import { DomHandler } from "primereact/utils";

const Login = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const code = searchParams.get("code");
  const isApplePrivateEmail = searchParams.get("is_private_email") === "true";
  const tryingMode = useIsTryingMode();

  const hasTriggeredLogin = useRef(false);

  const {
    triggerLogin,
    error: loginError,
    triggerMicrosoftLogin,
    triggerAppleLogin,
  } = useLogin(tryingMode);

  const showLoginLoader = useAtomValue(showLoginLoaderAtom);
  const isNativeBridge = useAtomValue(isNativeBridgeAtom);

  const { turnOff: closeSidebar } = useStoreToggle(showSidebarAtom);
  const { showToast } = useToast();

  const signInButtonClassnames = clsx(
    "bg-black text-white border border-neutral-600 flex font-normal gap-x-3 h-auto",
    "outline-0 p-4.5 rounded-full text-base w-fit h-full max-h-[3.125rem] items-center",
    "dark:bg-black dark:text-white",
    inter.className
  );
  const isNativeAndroid = isNativeBridge && DomHandler.isAndroid();

  useEffect(() => {
    if (code && !hasTriggeredLogin.current) {
      const currentUrl = window.location.origin + pathname;
      const reqBody = JSON.stringify({
        code,
        redirect_uri: currentUrl,
      });
      triggerLogin(reqBody);
      hasTriggeredLogin.current = true;
    }
  }, [code, pathname, triggerLogin]);

  useEffect(() => {
    if (isApplePrivateEmail) {
      showToast({
        detail:
          "We notice that you’re using Apple’s private email feature. Unfortunately that means we can’t validate that you have access to Otto’s Beta. If you think you should have access, please try again using the email address you used when you were invited to the Beta.",
        severity: "warn",
        sticky: true,
      });
    }
  }, [isApplePrivateEmail, showToast]);

  useEffect(() => {
    return () => closeSidebar();
  }, [closeSidebar]);

  return !loginError && (!!code || showLoginLoader) ? (
    <LoadingSpinner
      fillActive={colors.primary[500]}
      fill={colors.primary[900]}
      className="absolute h-4 left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-10 lg:w-16"
    />
  ) : (
    <>
      <Link href="https://www.ottotheagent.com/">
        <Logo className="animate-top-fade-in basis-8 grow-0 shrink-0" />
      </Link>
      <h1
        className="animate-top-fade-in-md animation-delay-[250ms] font-medium mt-3 tall:mt-7 shadow-[#0006] /
        text-2.5xl md:text-4xl lg:text-6xl text-center text-shadow text-white xs:font-normal xs:mt-8"
      >
        Hi, I&#39;m Otto, your AI agent for business travel.
      </h1>
      <p
        className="animate-top-fade-in-lg animation-delay-[500ms] mt-3 text-center text-gray-350 text-xl /
      tall:mt-8 md:px-10 lg:px-20"
      >
        Once we get to know each other, I proactively plan, book,{" "}
        <span className="md:text-nowrap">
          and ensure you have the perfect trip.
        </span>
      </p>
      <div
        className="animate-top-fade-in-xl animation-delay-[750ms] flex flex-col items-center justify-center /
      mt-3 tall:mt-8 gap-2 w-fit"
      >
        <GoogleLogin className={signInButtonClassnames} />
        <Button
          onClick={() => triggerMicrosoftLogin()}
          className={signInButtonClassnames}
        >
          <MicrosoftLogo />
          Sign in with Microsoft
        </Button>

        {!isNativeAndroid && (
          <Button
            onClick={() => triggerAppleLogin()}
            className={clsx(signInButtonClassnames, "gap-0 pl-2.5")}
          >
            <Image src={appleLogo} alt="Apple logo" height={40} />
            Sign in with Apple
          </Button>
        )}

        {(!isProduction() || !isNativeBridge) && <OneTimePassword />}
      </div>
      <div className="flex-1 flex items-end pb-2 tall:pb-6">
        <div
          className="animate-top-fade-in-2xl animation-delay-[1000ms] border-[#36b5f333] /
          border-t flex font-normal gap-x-4 pt-5 [&>*]:font-normal [&>*]:p-0 /
        [&>*]:text-neutral-475 [&>*]:text-sm xs:pt-3"
        >
          <p className="text-sm">© 2025 Otto</p>
          <Link className="hover:text-neutral-300" href={ROUTES.SUPPORT}>
            Support
          </Link>
          <Link className="hover:text-neutral-300" href={ROUTES.PRIVACY}>
            Privacy
          </Link>
          <Link className="hover:text-neutral-300" href={ROUTES.TERMS}>
            Terms
          </Link>
        </div>
      </div>
    </>
  );
};

export default Login;
