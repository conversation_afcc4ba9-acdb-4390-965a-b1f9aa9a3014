"use client";

import Logo from "@/common/components/logo";
import { ROUTES } from "@/common/constants";
import { useRedeemOtc } from "@/features/user/hooks/api";
import Link from "next/link";
import { Button } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { useState } from "react";

export default function LoginDenied() {
  const [code, setCode] = useState("");

  const { redeemOtc, isLoading } = useRedeemOtc();

  const onUseCode = () => {
    if (!code || isLoading) return;
    redeemOtc({ otc: code });
  };

  return (
    <div className="flex flex-col items-center justify-center gap-6">
      <Link href={ROUTES.LOGIN}>
        <Logo
          className="basis-8 grow-0 shrink-0 h-7 md:h-12 md:mb-1.5"
          height={50}
          width={160}
        />
      </Link>
      <h1
        className="text-3xl md:text-4xl leading-[2.875rem] md:leading-[4.5rem] text-center /
      text-white [text-shadow:_0px_0px_8px_rgba(0,0,0,0.4)]"
      >
        Activate your account
      </h1>
      <p className="text-center text-neutral-400 text-xl md:px-10 lg:px-20 mt-2 mb-6 md:m-0">
        Welcome to Otto! To get started, please enter your invitation code.
      </p>
      <div className="flex flex-col items-center gap-4 mt-1.5 max-w-60 w-full">
        <InputText
          className="w-full"
          placeholder="Invite code"
          value={code}
          onChange={(e) => setCode(e.target.value)}
          disabled={isLoading}
        />
        <Button
          label="Get started"
          severity="secondary"
          outlined
          className="w-full"
          onClick={onUseCode}
          loading={isLoading}
        />
        <div className="flex flex-col items-center gap-3 mt-1.5">
          <Link href="https://ottotheagent.com/">
            <Button
              label="Join our waitlist"
              link
              className="text-xl leading-none"
            />
          </Link>
          <Link href={ROUTES.LOGIN}>
            <Button
              label="Try another account"
              link
              className="text-xl leading-none"
            />
          </Link>
        </div>
      </div>
    </div>
  );
}
