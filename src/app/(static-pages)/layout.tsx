import { ReactNode } from "react";
import WithBottomShadow from "@/common/components/with-bottom-shadow";
import Logo from "@/common/components/logo";
import { Button } from "primereact/button";
import Link from "next/link";
import { ROUTES } from "@/common/constants";

const RootLayout = ({
  children,
}: Readonly<{
  children: ReactNode;
}>) => {
  return (
    <>
      <WithBottomShadow>
        <div className="container px-4 py-6 xl:max-w-350 flex items-center justify-between gap-4">
          <Link href={ROUTES.LOGIN}>
            <Logo />
          </Link>

          <Link href={ROUTES.LOGIN}>
            <Button label="Back" outlined />
          </Link>
        </div>
      </WithBottomShadow>
      <div className="container p-4 pb-20 xl:max-w-350">{children}</div>
    </>
  );
};

export default RootLayout;
