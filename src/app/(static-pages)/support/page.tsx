import appConfig from "@/../app.config";
import Paragraph from "@/features/static-pages/components/paragraph";
import Section from "@/features/static-pages/components/section";
import Title from "@/features/static-pages/components/title";

export default function SupportPage() {
  const { firstResponse, contact, workingHours } = appConfig ?? {};
  const { supportEmail } = contact ?? {};

  return (
    <>
      <Title>Support</Title>

      <Section title="Customer Support">
        <Paragraph>
          If you&#39;re a customer looking for support, please contact us via
          email at: <br />
          <a
            className="text-blue-500 underline"
            href={`mailto:${supportEmail}`}
          >
            {supportEmail}
          </a>
        </Paragraph>
      </Section>

      <Section title="Hours">
        <Paragraph>M-F: {workingHours}</Paragraph>
      </Section>

      <Section title="First-Response Service Level Agreement (SLA)">
        <Paragraph>{firstResponse}</Paragraph>
      </Section>
    </>
  );
}
