"use client";

import { useEffect } from "react";
import LoadingSpinner from "@/common/components/loading-spinner";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";

export default function Home() {
  const { onboardingRedirect } = useOnboardingRedirect();

  useEffect(() => {
    onboardingRedirect();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <main className="flex min-h-dvh items-center justify-center p-6">
      <LoadingSpinner className="w-16 lg:w-24 h-5" />
    </main>
  );
}
