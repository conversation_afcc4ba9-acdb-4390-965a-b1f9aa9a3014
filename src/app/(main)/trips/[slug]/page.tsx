"use client";

import { useEffect } from "react";
import { use<PERSON><PERSON>, useAtomValue, useSet<PERSON><PERSON> } from "jotai";
import DynamicRouteProps from "@/common/types/dynamic-route-props";
import { TutorialSteps } from "@/features/tutorial/types/tutorial-types";
import { ApiPaths } from "@/common/constants";
import screens from "@/common/constants/screens";
import { useGetThreadHistory } from "@/features/admin/hooks/api";
import { useStoreToggle } from "@/common/hooks/toggles";
import { useGetTripDetails, useTripsList } from "@/common/hooks/api";
import { useSessionHistory } from "@/features/user/hooks/api";
import useInitMessage from "@/features/chat/hooks/use-init-message";
import { tutorialStepAtom } from "@/features/tutorial/store/tutorial-store";
import { currentTripAtom } from "@/features/chat/store/current-trip";
import { showSidebar<PERSON>tom } from "@/common/store/sidebar";
import Itinerary from "@/features/chat/components/right-sidebar/itinerary";
import TripsHeader from "@/features/chat/components/trips-header";
import Sidebar from "@/common/components/sidebar/main";
import Chat from "@/features/chat/components/chat";
import RawOutputPopup from "@/features/chat/components/raw-output-popup";
import UpdatedFlightPopup from "@/features/chat/components/right-sidebar/itinerary/updated-flight-popup";
import useThrottle from "@/common/hooks/use-throttle";
import { isClient } from "@/common/utils";
import { isUserSelectedAtom } from "@/features/admin/store/foreign-user";
import { useIsTryingMode } from "@/common/hooks/use-trying-mode";
import { StringParam, useQueryParam } from "@/common/hooks/use-query-param";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import {
  chatHistoryReceivedAtom,
  openingMessageAtom,
} from "@/features/chat/store/chat";

export default function Trip({ params }: DynamicRouteProps) {
  const [message, setMessage] = useQueryParam("message", StringParam);
  const { turnOff: closeSidebar, value: isSidebarOpen } =
    useStoreToggle(showSidebarAtom);
  const setCurrentTrip = useSetAtom(currentTripAtom);
  const tryingModeEnabled = useIsTryingMode();
  const tripId = parseInt(params.slug, 10);
  const isUserSelected = useAtomValue(isUserSelectedAtom);
  const chatHistoryReceived = useAtomValue(chatHistoryReceivedAtom);

  const { data: trips } = useTripsList();
  const isOwnTrip = [...(trips?.booked ?? []), ...(trips?.planned ?? [])].some(
    (trip) => trip.id === tripId
  );
  const { getThreadHistory } = useGetThreadHistory(
    `${ApiPaths.ADMIN_GET_TRIP_HISTORY}?trip_id=${tripId}`
  );
  const { getTripDetails } = useGetTripDetails();
  const throttleTripDetails = useThrottle(getTripDetails, 0);
  const [openingMessage] = useAtom(openingMessageAtom);
  const getInitMessage = useInitMessage(tripId, openingMessage);
  const initMessage = getInitMessage();

  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);

  const { isLoading: sessionHistoryLoading, isTutorialComplete } =
    useSessionHistory();
  const [tutorialStep, setTutorialStep] = useAtom(tutorialStepAtom);
  const isDesktop = isClient() && window.innerWidth >= screens["lg"];

  useEffect(() => {
    if (message && chatHistoryReceived) {
      send({
        text: message,
        type: OutgoingMessageTypes.PROMPT,
      });
      setMessage(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [message, chatHistoryReceived]);

  useEffect(() => {
    throttleTripDetails(tripId);
    if (!isOwnTrip && isUserSelected) {
      getThreadHistory();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tripId, throttleTripDetails]);

  useEffect(() => {
    setCurrentTrip(tripId);
  }, [setCurrentTrip, tripId]);

  useEffect(() => {
    if (
      !sessionHistoryLoading &&
      isTutorialComplete === false &&
      !tutorialStep
    ) {
      setTimeout(
        () =>
          setTutorialStep(
            isDesktop ? TutorialSteps.TRIPS : TutorialSteps.SIDEBAR
          ),
        300
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTutorialComplete, sessionHistoryLoading, tutorialStep]);

  return (
    <>
      <div className="flex h-full relative w-full lg:flex-auto">
        <Sidebar isOpen={isSidebarOpen} onClose={closeSidebar} />
        <Chat
          header={<TripsHeader />}
          initialMessage={initMessage}
          tryingModeEnabled={tryingModeEnabled}
        />
      </div>
      <Itinerary />
      <RawOutputPopup />
      <UpdatedFlightPopup />
    </>
  );
}
