"use client";

import { <PERSON><PERSON> } from "primereact/button";
import { UserRole } from "@/features/user/types/user";
import { useUserProfile } from "@/features/user/hooks/api";
import { useCreateTrip, useTripsList } from "@/common/hooks/api";
import { useStoreToggle } from "@/common/hooks/toggles";
import { userSelectDebugAtom } from "@/features/chat/store/debugging";
import { showSidebarAtom } from "@/common/store/sidebar";
import Sidebar from "@/common/components/sidebar/main";
import BackDrop from "@/common/components/backdrop";
import IconClose from "@/common/components/icons/close";
import Header from "@/common/components/header";
import UserSelect from "@/features/admin/components/user-select";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";
import { useAtomValue } from "jotai";

export default function TripsPage() {
  const userSelectDebug = useAtomValue(userSelectDebugAtom);
  const { turnOff: closeSidebar, value: isSidebarOpen } =
    useStoreToggle(showSidebarAtom);
  const { data: trips } = useTripsList();
  const { createTrip } = useCreateTrip();
  const { role } = useUserProfile();
  const { onboardingRedirect } = useOnboardingRedirect();

  return (
    <>
      <Sidebar isOpen={isSidebarOpen} onClose={closeSidebar} />
      <div className="flex-auto h-full">
        <Header className="justify-between lg:justify-end">
          {role === UserRole.ADMIN && userSelectDebug && (
            <UserSelect onClear={onboardingRedirect} />
          )}
        </Header>
        {!!trips && [...trips?.booked, ...trips?.planned].length === 0 && (
          <div className="flex items-center justify-center mt-52">
            <Button onClick={(e) => createTrip()}>
              <IconClose fill="white" className="rotate-45 w-3" />
              Create trip
            </Button>
          </div>
        )}
      </div>
      {isSidebarOpen && (
        <BackDrop className="lg:hidden" onClick={closeSidebar} />
      )}
    </>
  );
}
