"use client";

import LoadingSpinner from "@/common/components/loading-spinner";
import useToast from "@/common/hooks/use-toast";
import usePaymentInformation from "@/features/user/api/use-payment-information";
import { ROUTES } from "@/common/constants/router.constants";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import PaymentDetailsForm from "@/features/user/components/payment-settings/payment-details-form";
import { useRouter } from "next/navigation";

export default function PaymentSettingsPage() {
  const { showToast } = useToast();
  const { data: initialData, isValidating, mutate } = usePaymentInformation();
  const router = useRouter();
  useViewportSize({
    onDetect: (isMobile) => {
      if (!isMobile) {
        router.replace(ROUTES.PAYMENT_DETAILS);
      }
    },
  });

  return (
    <>
      <h2 className="hidden font-medium leading-5.5 max-w-150 mx-auto my-4 text-xl md:block">
        Payment details
      </h2>
      {isValidating ? (
        <LoadingSpinner className="mx-auto my-4" />
      ) : (
        <PaymentDetailsForm
          initialData={initialData}
          onSaved={() => {
            showToast({
              severity: "success",
              detail: "Payment details successfully updated!",
            });
            mutate();
          }}
        />
      )}
    </>
  );
}
