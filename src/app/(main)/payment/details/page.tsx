"use client";

import useToast from "@/common/hooks/use-toast";
import usePaymentInformation from "@/features/user/api/use-payment-information";
import LoadingSpinner from "@/common/components/loading-spinner";
import PaymentDetailsForm from "@/features/user/components/payment-settings/payment-details-form";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle, faPlus } from "@fortawesome/pro-regular-svg-icons";
import { Button } from "primereact/button";
import { useCallback, useState } from "react";

export default function PaymentDetailsPage() {
  const { showToast } = useToast();
  const { data: initialData, isValidating, mutate } = usePaymentInformation();
  const [showPaymentForm, setShowPaymentForm] = useState<boolean>(false);
  // Show the form if there is no company payment information, or there is company payment info and the user has already set up their payment information.
  const shouldShowPaymentForm =
    showPaymentForm ||
    !initialData?.has_company_payment_information ||
    !!initialData?.payment_information;

  const addPaymentHandler = () => setShowPaymentForm(true);

  const onSaved: VoidFunction = useCallback(() => {
    showToast({
      severity: "success",
      detail: "Payment details successfully updated!",
    });
    mutate();
  }, [mutate, showToast]);

  return (
    <>
      <h2 className="hidden font-medium leading-5.5 max-w-150 mx-auto my-6 text-xl md:block">
        Payment details
      </h2>
      <div className="max-w-150 mx-auto h-full md:h-max">
        {initialData?.has_company_payment_information && (
          <div className="bg-lime-100 flex gap-x-4 items-start mb-6 p-3 rounded-[10px] dark:bg-lime-900">
            <FontAwesomeIcon className="size-4 py-1" icon={faInfoCircle} />
            {"You're already set up to pay with your company card."}
          </div>
        )}
        {shouldShowPaymentForm && isValidating && (
          <LoadingSpinner className="mx-auto my-4" />
        )}
        {shouldShowPaymentForm && !isValidating && (
          <PaymentDetailsForm initialData={initialData} onSaved={onSaved} />
        )}
        {!shouldShowPaymentForm && (
          <Button
            className="font-medium p-0 text-blue-500 text-base"
            link
            onClick={addPaymentHandler}
          >
            <FontAwesomeIcon icon={faPlus} />
            add personal card
          </Button>
        )}
      </div>
    </>
  );
}
