"use client";

import LoadingSpinner from "@/common/components/loading-spinner";
import arrayHasElements from "@/common/utils/array-has-elements";
import useAirlineCredits from "@/features/user/api/use-airline-credits";
import AirlineCredit from "@/features/user/components/payment-settings/airline-credit";

export default function AirlineCreditsPage() {
  const { data, isLoading } = useAirlineCredits();
  const { airline_credits } = data ?? {};

  return isLoading ? (
    <LoadingSpinner className="mx-auto mt-10" />
  ) : (
    <>
      <h2 className="hidden font-medium leading-5.5 max-w-100 mx-auto my-4 text-xl md:block">
        Airline credits
      </h2>
      <div className="flex flex-col gap-y-4 max-w-100 mt-4 mx-auto">
        {arrayHasElements(airline_credits) ? (
          airline_credits.map((item, index) => (
            <AirlineCredit {...item} key={index} />
          ))
        ) : (
          <h2>You do not have any airline credits.</h2>
        )}
      </div>
    </>
  );
}
