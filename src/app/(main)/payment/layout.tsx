"use client";

import Header from "@/common/components/header";
import { SettingsHeader } from "@/common/components/settings-header";
import Sidebar from "@/common/components/sidebar/main";
import { ROUTES } from "@/common/constants";
import { useStoreToggle } from "@/common/hooks/toggles";
import { useCurrentRoute } from "@/common/hooks/use-current-route";
import { showSidebarAtom } from "@/common/store/sidebar";
import { useRedirectToLastTrip } from "@/features/chat/hooks/redirects";
import PaymentSettingsNavigation from "@/features/user/components/payment-settings/payment-settings-navigation";
import { paymentRoutesHeaders } from "@/features/user/constants/payment";
import clsx from "clsx";
import { useRouter } from "next/navigation";
import { ReactNode } from "react";

type PaymentLayoutProps = {
  children?: ReactNode;
};

export default function PaymentLayout({ children }: PaymentLayoutProps) {
  const currentRoute = useCurrentRoute();
  const isPaymentRoute = currentRoute === ROUTES.PAYMENT;
  const router = useRouter();
  const redirectToLastTrip = useRedirectToLastTrip();
  const { turnOff: onSidebarClose, value: isSidebarOpen } =
    useStoreToggle(showSidebarAtom);

  return (
    <div className="flex relative size-full">
      <Sidebar
        className="max-md:hidden max-md:[&+.backdrop]:hidden"
        isOpen={isSidebarOpen}
        onClose={onSidebarClose}
      />
      <div className="size-full flex flex-col">
        <SettingsHeader
          className="md:hidden"
          onBack={
            !isPaymentRoute ? () => router.push(ROUTES.PAYMENT) : undefined
          }
          onClose={redirectToLastTrip}
          title={paymentRoutesHeaders[currentRoute as string]}
        />
        <Header wrapperClassName="hidden md:block" title="Payments" />
        <div className="flex flex-1 overflow-y-auto">
          <div
            className={clsx(
              "md:pt-8 md:px-5 w-full md:basis-[240px] md:border-neutral-250",
              "lg:basis-[280px] dark:md:border-neutral-800 md:border-r md:flex-none md:h-full",
              {
                "max-md:hidden": !isPaymentRoute,
              }
            )}
          >
            <PaymentSettingsNavigation />
          </div>
          <div
            className={clsx("p-4 w-full", {
              "max-md:hidden": isPaymentRoute,
            })}
          >
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}
