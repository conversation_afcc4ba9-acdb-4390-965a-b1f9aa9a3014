"use client";

import Header from "@/common/components/header";
import { SettingsHeader } from "@/common/components/settings-header";
import { useStoreToggle } from "@/common/hooks/toggles";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { showSidebarAtom } from "@/common/store/sidebar";
import Sidebar from "@/common/components/sidebar/main";
import { useRedirectToLastTrip } from "@/features/chat/hooks/redirects";
import { InputSwitch, InputSwitchChangeEvent } from "primereact/inputswitch";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useAtom, useAtomValue } from "jotai";
import { darkModeAtom } from "@/common/store/dark-mode";
import { DarkModeOptions } from "@/common/constants/theme";
import clsx from "clsx";
import { useUserProfile } from "@/features/user/hooks/api";
import { useCalendarConnect } from "@/features/user/api/use-calendar-connect";
import { showCalendarMessageAtom } from "@/features/user/store/settings";
import { useState } from "react";
import { CalendarDisconnectDialog } from "@/features/user/components/calendar-disconnect-dialog";
import { CalendarSwitchDialog } from "@/features/user/components/calendar-switch-dialog";
import { useClient } from "@/common/hooks/use-client";

export default function SettingsPage() {
  const isClient = useClient();
  const { isMobile, isDesktop } = useViewportSize();
  const { turnOff: onSidebarClose, value: isSidebarOpen } =
    useStoreToggle(showSidebarAtom);

  const [darkMode, setDarkMode] = useAtom(darkModeAtom);
  const showCalendarMessage = useAtomValue(showCalendarMessageAtom);

  const [calendarToDisconnect, setCalendarToDisconnect] = useState<
    "google" | "microsoft" | null
  >(null);
  const [calendarToSwitch, setCalendarToSwitch] = useState<
    "google" | "microsoft" | null
  >(null);

  const redirectToLastTrip = useRedirectToLastTrip();
  const { microsoftCalendarEnabled, googleCalendarEnabled } = useUserProfile();
  const { connectGoogleCalendar, connectMicrosoftCalendar, isLoading } =
    useCalendarConnect();

  if (!isClient) {
    return null;
  }

  const onSwitchGoogleCalendar = (event: InputSwitchChangeEvent) => {
    const shouldEnable = event.value;
    if (shouldEnable && microsoftCalendarEnabled) {
      setCalendarToSwitch("google");
    } else if (shouldEnable) {
      connectGoogleCalendar({ enable: true });
    } else {
      setCalendarToDisconnect("google");
    }
  };

  const onSwitchMicrosoftCalendar = (event: InputSwitchChangeEvent) => {
    const shouldEnable = event.value;
    if (shouldEnable && googleCalendarEnabled) {
      setCalendarToSwitch("microsoft");
    } else if (shouldEnable) {
      connectMicrosoftCalendar({ enable: true });
    } else {
      setCalendarToDisconnect("microsoft");
    }
  };

  const confirmCalendarDisconnect = () => {
    switch (calendarToDisconnect) {
      case "google":
        connectGoogleCalendar({ enable: false });
        break;
      case "microsoft":
        connectMicrosoftCalendar({ enable: false });
        break;
      default:
        break;
    }
    setCalendarToDisconnect(null);
  };

  const confirmCalendarSwitch = () => {
    switch (calendarToSwitch) {
      case "google":
        connectGoogleCalendar({ enable: true });
        break;
      case "microsoft":
        connectMicrosoftCalendar({ enable: true });
        break;
      default:
        break;
    }
    setCalendarToSwitch(null);
  };

  return (
    <div className="flex size-full">
      {isDesktop && <Sidebar isOpen={isSidebarOpen} onClose={onSidebarClose} />}
      <div className="flex flex-col size-full">
        {isMobile ? (
          <SettingsHeader title="Settings" onClose={redirectToLastTrip} />
        ) : (
          <Header title="Settings" />
        )}
        <div className="flex flex-col gap-8 p-4 md:p-8 max-w-164 mx-auto w-full">
          <div className="flex flex-col gap-2 md:gap-3">
            <h2 className="font-medium leading-5.5 md:text-xl">Calendar</h2>
            <div className="bg-gray-100 dark:bg-neutral-700 rounded-md p-4 flex flex-col gap-4">
              <p className="text-sm">
                Connect your calendar so Otto can learn better about your
                preferences and past and future trips.
              </p>
              {showCalendarMessage && (
                <div className="flex gap-2 p-2 rounded bg-amber-250 dark:text-black">
                  <FontAwesomeIcon
                    icon={["far", "circle-exclamation"]}
                    width={14}
                    className="mt-px"
                  />
                  <p className="text-xs leading-4.5">
                    For now you can select and connect only one calendar at a
                    time.
                  </p>
                </div>
              )}
              <div className="flex gap-4 font-medium justify-between items-center">
                Microsoft calendar
                <InputSwitch
                  checked={microsoftCalendarEnabled}
                  onChange={onSwitchMicrosoftCalendar}
                  disabled={isLoading}
                />
              </div>
              <div className="flex gap-4 font-medium justify-between items-center">
                Google calendar
                <InputSwitch
                  checked={googleCalendarEnabled}
                  onChange={onSwitchGoogleCalendar}
                  disabled={isLoading}
                />
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-2 md:gap-3">
            <h2 className="font-medium leading-5.5 md:text-xl">Theme</h2>
            <div className="bg-gray-100 dark:bg-neutral-700 rounded-md p-4 flex flex-col gap-4 font-medium">
              <button
                className="flex items-center gap-2 w-full"
                onClick={() => setDarkMode(DarkModeOptions.DEVICE_DEFAULT)}
              >
                <FontAwesomeIcon
                  icon="check"
                  className={clsx("text-primary-500", {
                    "opacity-0": darkMode !== DarkModeOptions.DEVICE_DEFAULT,
                  })}
                />
                System
              </button>
              <button
                className="flex items-center gap-2 w-full"
                onClick={() => setDarkMode(DarkModeOptions.LIGHT)}
              >
                <FontAwesomeIcon
                  icon="check"
                  className={clsx("text-primary-500", {
                    "opacity-0": darkMode !== DarkModeOptions.LIGHT,
                  })}
                />
                Light
              </button>
              <button
                className="flex items-center gap-2 w-full"
                onClick={() => setDarkMode(DarkModeOptions.DARK)}
              >
                <FontAwesomeIcon
                  icon="check"
                  className={clsx("text-primary-500", {
                    "opacity-0": darkMode !== DarkModeOptions.DARK,
                  })}
                />
                Dark
              </button>
            </div>
          </div>
        </div>
      </div>
      <CalendarDisconnectDialog
        visible={!!calendarToDisconnect}
        onHide={() => setCalendarToDisconnect(null)}
        onConfirm={confirmCalendarDisconnect}
      />
      <CalendarSwitchDialog
        visible={!!calendarToSwitch}
        onHide={() => setCalendarToSwitch(null)}
        onConfirm={confirmCalendarSwitch}
      />
    </div>
  );
}
