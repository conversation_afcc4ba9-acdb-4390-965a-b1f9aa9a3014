"use client";

import Header from "@/common/components/header";
import Sidebar from "@/common/components/sidebar/main";
import { useStoreToggle } from "@/common/hooks/toggles";
import { showSidebarAtom } from "@/common/store/sidebar";
import UserSelect from "@/features/admin/components/user-select";
import FlightDetailsSidebar from "@/features/chat/components/right-sidebar/flight-details/flight-details-sidebar";
import HotelDetails from "@/features/chat/components/right-sidebar/hotel-details";
import Itinerary from "@/features/chat/components/right-sidebar/itinerary";
import { userSelectDebugAtom } from "@/features/chat/store/debugging";
import ItinerariesTable from "@/features/itineraries/components/itineraries-table/itineraries-table";
import { useUserProfile } from "@/features/user/hooks/api";
import { UserRole } from "@/features/user/types/user";
import { useAtomValue } from "jotai";

export default function ItinerariesPage() {
  const { turnOff: onSidebarClose, value: isSidebarOpen } =
    useStoreToggle(showSidebar<PERSON>tom);
  const userSelectDebug = useAtomValue(userSelectDebugAtom);

  const { role } = useUserProfile();

  return (
    <>
      <Sidebar isOpen={isSidebarOpen} onClose={onSidebarClose} />
      <div className="flex-1">
        <Header>
          <h1 className="w-full">Your itineraries</h1>
          {role === UserRole.ADMIN && userSelectDebug && <UserSelect />}
        </Header>
        <div>
          <ItinerariesTable />
          <HotelDetails />
          <FlightDetailsSidebar />
        </div>
      </div>
      <Itinerary />
    </>
  );
}
