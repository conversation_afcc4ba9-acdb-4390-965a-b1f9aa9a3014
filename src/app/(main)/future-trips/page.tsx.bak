"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useAtomValue } from "jotai";
import { UserRole } from "@/features/user/types/user";
import { ApiPaths } from "@/common/constants";
import { futureTripsInitMessage } from "@/features/chat/constants/messages";
import { USER_SELECT_DEBUG_KEY } from "@/features/chat/constants/debugging";
import { useLocalStorageToggle, useStoreToggle } from "@/common/hooks/toggles";
import { useGetThreadHistory } from "@/features/admin/hooks/api";
import { useUserProfile } from "@/features/user/hooks/api";
import { userSelectDebugAtom } from "@/features/chat/store/debugging";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import { showSidebarAtom } from "@/common/store/sidebar";
import Chat from "@/features/chat/components/chat";
import Header from "@/common/components/header";
import Sidebar from "@/common/components/sidebar/main";
import OnboardingSidebar from "@/common/components/sidebar/onboarding";
import UserSelect from "@/features/admin/components/user-select";
import useWebSocket from "@/features/chat/hooks/use-web-socket";

export default function FutureTripsPage() {
  const searchParams = useSearchParams();
  const isOnboarding = !!searchParams.get("onboarding");
  const { role } = useUserProfile();
  const { sendInitMessage } = useWebSocket(
    process.env.NEXT_PUBLIC_WS_URL as string
  );
  const { error: threadHistoryError, getThreadHistory: getFutureTripsHistory } =
    useGetThreadHistory(ApiPaths.ADMIN_GET_FUTURE_TRIPS_HISTORY);
  const { value: userSelectDebug } = useLocalStorageToggle(
    userSelectDebugAtom,
    USER_SELECT_DEBUG_KEY
  );
  const { turnOff: onSidebarClose, value: isSidebarOpen } =
    useStoreToggle(showSidebarAtom);

  const foreignUser = useAtomValue(foreignUserAtom);
  const initMessage = !foreignUser
    ? { ...futureTripsInitMessage, isOnboarding }
    : undefined;

  useEffect(() => {
    !threadHistoryError &&
      !!foreignUser &&
      getFutureTripsHistory(`?user_id=${foreignUser.id}`);
  }, [foreignUser, getFutureTripsHistory, threadHistoryError]);

  const header = (
    <Header>
      <h1 className="w-full">Creating your trips</h1>
      {role === UserRole.ADMIN && userSelectDebug && (
        <UserSelect
          onClear={() =>
            sendInitMessage({ ...futureTripsInitMessage, isOnboarding: false })
          }
        />
      )}
    </Header>
  );

  return (
    <div className="flex h-full relative w-full lg:flex-auto">
      {isOnboarding ? (
        <OnboardingSidebar onClose={onSidebarClose} isOpen={isSidebarOpen} />
      ) : (
        <Sidebar isOpen={isSidebarOpen} onClose={onSidebarClose} />
      )}

      <Chat
        className={"flex-auto"}
        header={header}
        initialMessage={initMessage}
      />
    </div>
  );
}
