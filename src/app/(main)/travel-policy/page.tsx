"use client";

import { useEffect } from "react";
import { useAtomValue } from "jotai";
import RightSidebars from "@/features/chat/types/right-sidebars";
import screens from "@/common/constants/screens";
import { ApiPaths } from "@/common/constants";
import { travelPolicyInitMessage } from "@/features/chat/constants/messages";
import { useGetThreadHistory } from "@/features/admin/hooks/api";
import { useStoreToggle } from "@/common/hooks/toggles";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { showSidebarAtom } from "@/common/store/sidebar";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import Chat from "@/features/chat/components/chat";
import Sidebar from "@/common/components/sidebar/main";
import TravelPolicyChatHeader from "@/features/travel-policy/components/travel-policy-chat-header";
import TravelPolicySidebar from "@/features/travel-policy/components/travel-policy-sidebar";
import { isClient } from "@/common/utils";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { useUserProfile } from "@/features/user/hooks/api";
import { TravelPolicyDisplay } from "@/features/travel-policy/components/travel-policy-display";
import Header from "@/common/components/header";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faInfoCircle,
  faQuestionCircle,
} from "@fortawesome/pro-regular-svg-icons";
import LoadingSpinner from "@/common/components/loading-spinner";

export default function TravelPolicyPage() {
  const isLargeScreen = isClient() && window.innerWidth >= screens["3xl"];
  const { isDesktop } = useViewportSize();
  const { turnOff: onSidebarClose, value: isSidebarOpen } =
    useStoreToggle(showSidebarAtom);
  const { rightSidebar, switchTo } = useRightSidebarSwitch();
  const {
    error: threadHistoryError,
    getThreadHistory: getTravelPolicyHistory,
  } = useGetThreadHistory(ApiPaths.ADMIN_GET_TRAVEL_POLICY_HISTORY);

  const foreignUser = useAtomValue(foreignUserAtom);
  const initMessage = !foreignUser ? travelPolicyInitMessage : undefined;

  useEffect(() => {
    !threadHistoryError &&
      !!foreignUser &&
      getTravelPolicyHistory(`?user_id=${foreignUser.id}`);
  }, [foreignUser, getTravelPolicyHistory, threadHistoryError]);

  useEffect(() => {
    isLargeScreen && switchTo(RightSidebars.TRAVEL_POLICY);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLargeScreen]);

  const { profile, travelPolicy } = useUserProfile();

  if (!profile) {
    return (
      <LoadingSpinner className="h-5 mt-[50dvh] mx-auto -translate-y-1/2 w-16 lg:w-24" />
    );
  }

  const isReadonly = !!profile?.organization_id;

  return (
    <div className="flex h-full relative w-full lg:flex-auto">
      <Sidebar isOpen={isSidebarOpen} onClose={onSidebarClose} />
      {isReadonly ? (
        <div className="w-full">
          <Header wrapperClassName="h-max">
            <h1 className="flex gap-x-1 items-center w-full overflow-hidden text-nowrap text-ellipsis">
              Travel policy
            </h1>
          </Header>
          <div className="flex-col max-w-164 mx-auto p-4 w-full md:p-8">
            {!!travelPolicy ? (
              <>
                <div className="bg-amber-250 flex gap-x-4 items-start mb-6 p-3 rounded-[10px] dark:bg-amber-600">
                  <FontAwesomeIcon
                    className="size-4 py-1"
                    icon={faInfoCircle}
                  />
                  Your travel policy is being controlled by your organization.
                </div>
                <TravelPolicyDisplay travelPolicy={travelPolicy} />
              </>
            ) : (
              <div className="bg-gray-200 flex gap-x-4 items-start mb-6 p-3 rounded-[10px] dark:bg-gray-700">
                <FontAwesomeIcon
                  className="size-4 py-1"
                  icon={faQuestionCircle}
                />
                Your travel policy is being controlled by your organization. No company travel policy found.
              </div>
            )}
          </div>
        </div>
      ) : (
        <>
          <Chat
            className="flex-auto"
            emptyMessage={threadHistoryError?.detail}
            header={<TravelPolicyChatHeader />}
            initialMessage={initMessage}
          />
          <TravelPolicySidebar
            data={travelPolicy}
            isOpen={rightSidebar === RightSidebars.TRAVEL_POLICY}
            onClose={() => switchTo(null)}
          />
        </>
      )}
    </div>
  );
}
