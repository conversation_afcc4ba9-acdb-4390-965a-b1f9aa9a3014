"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAtomValue } from "jotai";
import { ROUTES } from "@/common/constants";
import { isUserSelectedAtom } from "@/features/admin/store/foreign-user";
import Sidebar from "@/common/components/sidebar/main";
import Header from "@/common/components/header";
import { useStoreToggle } from "@/common/hooks/toggles";
import { showSidebarAtom } from "@/common/store/sidebar";
import { PreferencesList } from "@/features/user/components/preferences-list";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";
import { useSelectedUserSettings } from "@/features/admin/hooks/api";

export default function UserPreferencesPage() {
  const router = useRouter();
  const isUserSelected = useAtomValue(isUserSelectedAtom);
  const { turnOff: onSidebarClose, value: isSidebarOpen } =
    useStoreToggle(showSidebar<PERSON>tom);

  const { onboardingRedirect } = useOnboardingRedirect();

  const { preferences } = useSelectedUserSettings();

  // Redirect to preferences page if this page is accessed manually
  useEffect(() => {
    if (!isUserSelected) {
      router.push(ROUTES.PREFERENCES);
    }
  }, [isUserSelected, router]);

  return (
    <div className="flex size-full relative">
      <Sidebar isOpen={isSidebarOpen} onClose={onSidebarClose} />
      <div className="flex flex-col h-full flex-1 min-w-0">
        <Header
          title="Travel preferences"
          showUserSelect
          onClearUserSelect={onboardingRedirect}
        />
        <div className="p-6 flex-1 overflow-y-auto">
          {preferences && <PreferencesList preferences={preferences} />}
        </div>
      </div>
    </div>
  );
}
