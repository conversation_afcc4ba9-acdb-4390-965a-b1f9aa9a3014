"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheck } from "@fortawesome/pro-regular-svg-icons";

import { ROUTES } from "@/common/constants";
import { useClient } from "@/common/hooks/use-client";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";
import Header from "@/common/components/header";
import { SettingsHeader } from "@/common/components/settings-header";
import { LeftNav } from "@/features/company-admin/components/left-nav";
import PaymentDetailsForm from "@/features/user/components/payment-settings/payment-details-form";
import LoadingSpinner from "@/common/components/loading-spinner";
import usePaymentInformation from "@/features/user/api/use-payment-information";

export default function AdminSettingsPaymentMethodsPage() {
  const router = useRouter();
  const { isMobile, isDesktop } = useViewportSize();
  const isClient = useClient();
  const { onboardingRedirect } = useOnboardingRedirect();
  const [isSaved, setIsSaved] = useState<boolean>(false);
  const {
    data: initialData,
    isValidating,
    mutate,
  } = usePaymentInformation(true);

  if (!isClient) return null;

  return (
    <div className="flex flex-col size-full overflow-y-auto">
      {isMobile && (
        <>
          <SettingsHeader
            title="Payment methods"
            onBack={() => router.push(ROUTES.ADMIN_SETTINGS)}
            onClose={onboardingRedirect}
          />
        </>
      )}
      {isDesktop && (
        <>
          <Header>Payment methods</Header>
        </>
      )}
      <div className="flex overflow-y-auto flex-1">
        <div className="hidden p-6 pt-8 border-r border-neutral-250 md:block dark:border-neutral-800">
          <LeftNav />
        </div>
        <div className="p-6 pt-7 flex-1 h-fit max-w-150 mx-auto">
          <h2 className="hidden font-medium leading-5.5 mb-6 text-xl md:block">
            Payment details
          </h2>
          {isSaved && (
            <div className="bg-lime-100 flex gap-x-4 items-start mb-6 p-3 rounded-[10px] dark:bg-lime-900">
              <FontAwesomeIcon className="size-4 py-1" icon={faCheck} />
              Your company card has been added and is now available for
              employees to use at checkout.
            </div>
          )}
          {isValidating ? (
            <LoadingSpinner className="mx-auto my-4" />
          ) : (
            <PaymentDetailsForm
              initialData={initialData}
              isCompanyCard={true}
              onSaved={() => {
                setIsSaved(true);
                mutate();
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
}
