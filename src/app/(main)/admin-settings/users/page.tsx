"use client";

import Header from "@/common/components/header";
import { SettingsHeader } from "@/common/components/settings-header";
import { ROUTES } from "@/common/constants";
import { useClient } from "@/common/hooks/use-client";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";
import { LeftNav } from "@/features/company-admin/components/left-nav";
import { useRouter } from "next/navigation";

export default function AdminSettingsUsersPage() {
  const router = useRouter();
  const { isMobile, isDesktop } = useViewportSize();
  const isClient = useClient();
  const { onboardingRedirect } = useOnboardingRedirect();

  if (!isClient) return null;

  return (
    <div className="flex flex-col size-full overflow-y-auto">
      {isMobile && (
        <>
          <SettingsHeader
            title="Users"
            onBack={() => router.push(ROUTES.ADMIN_SETTINGS)}
            onClose={onboardingRedirect}
          />
        </>
      )}
      {isDesktop && (
        <>
          <Header title="Users" />
          <div className="flex overflow-y-auto flex-1">
            <div className="p-6 pt-8 border-r border-neutral-250 dark:border-neutral-800">
              <LeftNav />
            </div>
          </div>
        </>
      )}
    </div>
  );
}
