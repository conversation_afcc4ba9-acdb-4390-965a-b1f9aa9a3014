"use client";

import Chat from "@/features/chat/components/chat";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import {
  OutgoingMessage,
  OutgoingMessageTypes,
} from "@/features/chat/types/messages";
import RightSidebars from "@/features/chat/types/right-sidebars";
import { LeftNav } from "@/features/company-admin/components/left-nav";
import TravelPolicyChatHeader from "@/features/travel-policy/components/travel-policy-chat-header";
import TravelPolicySidebar from "@/features/travel-policy/components/travel-policy-sidebar";
import { useUserProfile } from "@/features/user/hooks/api";
import { useClient } from "@/common/hooks/use-client";

export default function AdminSettingsTravelPolicyPage() {
  const isClient = useClient();
  const { rightSidebar, switchTo } = useRightSidebarSwitch();
  const { travelPolicy } = useUserProfile();

  const initMessage: OutgoingMessage = {
    type: OutgoingMessageTypes.TRAVEL_POLICY_INIT,
  };

  if (!isClient) {
    return null;
  }

  return (
    <>
      <div className="flex-1 flex flex-col overflow-y-auto h-full">
        <TravelPolicyChatHeader />
        <div className="flex overflow-y-auto h-full">
          <div className="hidden p-6 pt-8 border-r border-neutral-250 dark:border-neutral-800 md:block">
            <LeftNav />
          </div>
          <Chat initialMessage={initMessage} />
        </div>
      </div>
      <TravelPolicySidebar
        data={travelPolicy}
        isOpen={rightSidebar === RightSidebars.TRAVEL_POLICY}
        onClose={() => switchTo(null)}
      />
    </>
  );
}
