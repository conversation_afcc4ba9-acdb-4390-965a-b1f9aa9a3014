"use client";

import { SettingsHeader } from "@/common/components/settings-header";
import { ROUTES } from "@/common/constants/router.constants";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";
import { useRouter } from "next/navigation";
import { LeftNav } from "@/features/company-admin/components/left-nav";

export default function AdminSettingsPage() {
  const router = useRouter();
  useViewportSize({
    onDetect: (isMobile) => {
      if (!isMobile) {
        router.replace(ROUTES.ADMIN_SETTINGS_DOMAINS);
      }
    },
  });
  const { onboardingRedirect } = useOnboardingRedirect();

  return (
    <div>
      <SettingsHeader title="Admin settings" onClose={onboardingRedirect} />
      <LeftNav />
    </div>
  );
}
