"use client";

import Sidebar from "@/common/components/sidebar/main";
import { ROUTES } from "@/common/constants";
import { useStoreToggle } from "@/common/hooks/toggles";
import { useClient } from "@/common/hooks/use-client";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { showSidebarAtom } from "@/common/store/sidebar";
import { useUserProfile } from "@/features/user/hooks/api";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isMobile } = useViewportSize();
  const { turnOff: onSidebarClose, value: isSidebarOpen } =
    useStoreToggle(showSidebarAtom);
  const isClient = useClient();
  const { isCompanyAdmin, profile } = useUserProfile();
  const router = useRouter();

  useEffect(() => {
    if (!isCompanyAdmin && !!profile) {
      router.push(ROUTES.HOME);
    }
  }, [isCompanyAdmin, profile, router]);

  if (isMobile || !isClient) {
    return children;
  }

  return (
    <div className="flex h-full relative w-full lg:flex-auto">
      <Sidebar isOpen={isSidebarOpen} onClose={onSidebarClose} />
      {children}
    </div>
  );
}
