"use client";

import Divider from "@/common/components/divider";
import Header from "@/common/components/header";
import { SettingsHeader } from "@/common/components/settings-header";
import { ROUTES } from "@/common/constants";
import regex from "@/common/constants/regex";
import { useClient } from "@/common/hooks/use-client";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";
import { LeftNav } from "@/features/company-admin/components/left-nav";
import { useOrganization } from "@/features/company-admin/api/use-organization";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Button } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { KeyboardEvent, useState } from "react";

const MIN_DOMAIN_LENGTH = 3;

export default function AdminSettingsDomainsPage() {
  const router = useRouter();
  const { isMobile, isDesktop } = useViewportSize();
  const isClient = useClient();
  const { onboardingRedirect } = useOnboardingRedirect();

  const [isAddingDomain, setIsAddingDomain] = useState(false);
  const [domain, setDomain] = useState("");

  const {
    organization,
    addDomain,
    isAddDomainLoading,
    deleteDomain,
    isDeleteDomainLoading,
  } = useOrganization();

  if (!isClient) return null;

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === "Enter" && domain.length >= MIN_DOMAIN_LENGTH) {
      handleSubmit();
    }
  };

  const handleSubmit = () => {
    if (!domain.trim()) {
      return;
    }
    addDomain({ domain });
    setDomain("");
    setIsAddingDomain(false);
  };

  const DomainsTitle = (
    <h2 className="font-medium leading-5.5 md:w-52 shrink-0">
      Validated domains
    </h2>
  );
  const Domains = (
    <div className="space-y-4">
      {organization?.validated_domains.map((domain, index) => (
        <div className="flex items-center justify-between" key={index}>
          <span className="font-medium leading-5.5">{domain}</span>
          <Button
            severity="danger"
            text
            className="p-0.5"
            onClick={() => deleteDomain(domain)}
            disabled={isDeleteDomainLoading}
          >
            <FontAwesomeIcon icon={["far", "trash"]} width={12} />
            Delete
          </Button>
        </div>
      ))}
    </div>
  );
  const DomainsMessage = (
    <p className="text-neutral-500 leading-5.5">
      Employees with these domains will auto-join Otto. No manual approval is
      required.
    </p>
  );
  const AddDomainButton = !isAddingDomain && (
    <Button
      link
      label="+ add domain"
      className="p-0 text-base leading-5.5 w-fit"
      onClick={() => setIsAddingDomain(true)}
      loading={isAddDomainLoading}
    />
  );
  const AddDomainInput = isAddingDomain && (
    <div className="flex items-center gap-2">
      <InputText
        value={domain}
        onChange={(e) => setDomain(e.target.value)}
        placeholder="Enter domain"
        autoFocus
        className="w-full"
        keyfilter={regex.domain}
        minLength={3}
        onKeyDown={handleKeyDown}
      />
      <Button
        label="Add"
        onClick={handleSubmit}
        disabled={domain.length < MIN_DOMAIN_LENGTH}
      />
    </div>
  );

  const Settings = (
    <div className="px-4 pt-6 md:pt-18 pb-2 w-full max-w-[37.5rem] mx-auto overflow-y-auto">
      <div className="flex flex-col md:flex-row gap-6 md:gap-0">
        <h2 className="font-medium leading-5.5 md:w-52 shrink-0">
          Organization
        </h2>
        <div className="flex items-center gap-1">
          {organization?.image && (
            <Image
              src={organization.image}
              alt="Company logo"
              width={24}
              height={24}
            />
          )}
          <div className="text-xl md:text-base leading-5.5 font-medium">
            {organization?.name}
          </div>
        </div>
      </div>

      <Divider className="py-4 md:py-6" />

      {isMobile && (
        <div className="flex flex-col gap-6">
          <div className="flex items-center justify-between">
            {DomainsTitle}
            {AddDomainButton}
          </div>
          {DomainsMessage}
          {Domains}
          {AddDomainInput}
        </div>
      )}
      {isDesktop && (
        <div className="flex">
          {DomainsTitle}
          <div className="flex flex-col gap-4">
            {Domains}
            {DomainsMessage}
            {AddDomainButton}
            {AddDomainInput}
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="flex flex-col size-full overflow-y-auto">
      {isMobile && (
        <>
          <SettingsHeader
            title="Domains"
            onBack={() => router.push(ROUTES.ADMIN_SETTINGS)}
            onClose={onboardingRedirect}
          />
          {Settings}
        </>
      )}
      {isDesktop && (
        <>
          <Header title="Domains" />
          <div className="flex overflow-y-auto flex-1">
            <div className="p-6 pt-8 border-r border-neutral-250 dark:border-neutral-800">
              <LeftNav />
            </div>
            {Settings}
          </div>
        </>
      )}
    </div>
  );
}
