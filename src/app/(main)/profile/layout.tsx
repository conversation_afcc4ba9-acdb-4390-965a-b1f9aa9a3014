"use client";

import Sidebar from "@/common/components/sidebar/main";
import { useStoreToggle } from "@/common/hooks/toggles";
import { useClient } from "@/common/hooks/use-client";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { showSidebarAtom } from "@/common/store/sidebar";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isMobile } = useViewportSize();
  const { turnOff: onSidebarClose, value: isSidebarOpen } =
    useStoreToggle(showSidebarAtom);
  const isClient = useClient();

  if (isMobile || !isClient) {
    return children;
  }

  return (
    <div className="flex h-full relative w-full lg:flex-auto">
      <Sidebar isOpen={isSidebarOpen} onClose={onSidebarClose} />
      {children}
    </div>
  );
}
