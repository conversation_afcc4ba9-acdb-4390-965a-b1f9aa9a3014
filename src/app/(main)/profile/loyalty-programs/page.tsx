"use client";

import { useRouter } from "next/navigation";

import { ROUTES } from "@/common/constants";
import { useClient } from "@/common/hooks/use-client";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { SettingsHeader } from "@/common/components/settings-header";
import Header from "@/common/components/header";
import { LeftNav } from "@/features/user/components/left-nav";
import LoyaltyPrograms from "@/features/user/components/loyalty-programs/loyalty-programs";

export default function LoyaltyProgramsPage() {
  const router = useRouter();
  const { isMobile, isDesktop } = useViewportSize();
  const isClient = useClient();

  if (!isClient) return null;

  return (
    <div className="flex flex-col size-full overflow-y-auto">
      {isMobile && (
        <>
          <SettingsHeader
            title="Loyalty numbers"
            onBack={() => router.push(ROUTES.PROFILE)}
            onClose={() => router.push(ROUTES.HOME)}
          />
          <div className="p-4 space-y-6 overflow-y-auto mx-auto">
            <LoyaltyPrograms />
          </div>
        </>
      )}
      {isDesktop && (
        <>
          <Header>User profile</Header>
          <div className="flex overflow-y-auto flex-1">
            <div className="p-6 pt-8 border-r border-neutral-250 dark:border-neutral-800">
              <LeftNav />
            </div>
            <div className="flex-1 h-fit max-w-[34.375rem] mx-auto p-6 pt-7">
              <h2 className="font-medium leading-5.5 mb-6 text-xl">
                Loyalty numbers
              </h2>
              <LoyaltyPrograms />
            </div>
          </div>
        </>
      )}
    </div>
  );
}
