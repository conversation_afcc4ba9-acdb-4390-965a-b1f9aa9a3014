"use client";

import { ROUTES } from "@/common/constants/router.constants";
import { useRouter } from "next/navigation";
import { SettingsHeader } from "@/common/components/settings-header";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { LeftNav } from "@/features/user/components/left-nav";

export default function ProfilePage() {
  const router = useRouter();
  useViewportSize({
    onDetect: (isMobile) => {
      if (!isMobile) {
        router.replace(ROUTES.PERSONAL_INFO);
      }
    },
  });

  const goBack = () => {
    router.push(ROUTES.HOME);
  };

  return (
    <div>
      <SettingsHeader title="User profile" onClose={goBack} />
      <LeftNav />
    </div>
  );
}
