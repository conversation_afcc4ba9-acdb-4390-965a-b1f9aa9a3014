"use client";

import Header from "@/common/components/header";
import { SettingsHeader } from "@/common/components/settings-header";
import { useClient } from "@/common/hooks/use-client";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { useRouter } from "next/navigation";

import { LeftNav } from "@/features/user/components/left-nav";
import PersonalDetailsForm from "@/features/user/components/personal-details-form";
import { ROUTES } from "@/common/constants";

export default function PersonalInfoPage() {
  const router = useRouter();
  const { isMobile, isDesktop } = useViewportSize();
  const isClient = useClient();

  if (!isClient) return null;

  return (
    <div className="flex flex-col size-full overflow-y-auto">
      {isMobile && (
        <>
          <SettingsHeader
            title="Personal information"
            onBack={() => router.push(ROUTES.PROFILE)}
            onClose={() => router.push(ROUTES.HOME)}
          />
          <div className="p-4 space-y-6 overflow-y-auto mx-auto">
            <PersonalDetailsForm />
          </div>
        </>
      )}
      {isDesktop && (
        <>
          <Header>User profile</Header>
          <div className="flex overflow-y-auto flex-1">
            <div className="p-6 pt-8 border-r border-neutral-250 dark:border-neutral-800">
              <LeftNav />
            </div>
            <div className="p-6 pt-7 flex-1 flex justify-center h-fit">
              <PersonalDetailsForm title="Personal information" />
            </div>
          </div>
        </>
      )}
    </div>
  );
}
