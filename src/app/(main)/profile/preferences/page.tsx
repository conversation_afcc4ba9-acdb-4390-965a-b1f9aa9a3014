"use client";

import { useEffect } from "react";
import { useAtomValue } from "jotai";
import RightSidebars from "@/features/chat/types/right-sidebars";
import { ApiPaths, ROUTES } from "@/common/constants";
import { preferencesInitMessage } from "@/features/chat/constants/messages";
import { useGetThreadHistory } from "@/features/admin/hooks/api";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import { threadTimestampAtom } from "@/features/chat/store/current-trip";
import { useUserProfile } from "@/features/user/hooks/api";
import Chat from "@/features/chat/components/chat";
import PreferencesSidebar from "@/common/components/sidebar/preferences";
import PreferencesChatHeader from "@/features/user/components/preferences-chat-header";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { LeftNav } from "@/features/user/components/left-nav";
import { useCalendarAuth } from "@/common/hooks/use-calendar-auth";
import { useClient } from "@/common/hooks/use-client";
import LoadingSpinner from "@/common/components/loading-spinner";
import { useTripsList } from "@/common/hooks/api";

export default function PreferencesPage() {
  const isClient = useClient();
  const { isDesktop, isMobile } = useViewportSize();

  const { rightSidebar, switchTo } = useRightSidebarSwitch();
  const { error: threadHistoryError, getThreadHistory: getPreferencesHistory } =
    useGetThreadHistory(ApiPaths.ADMIN_GET_PREFERENCES_HISTORY);
  const { preferences } = useUserProfile();
  const threadTimestamp = useAtomValue(threadTimestampAtom);

  const foreignUser = useAtomValue(foreignUserAtom);
  const initMessage = !foreignUser ? preferencesInitMessage : undefined;

  const { isLoading } = useTripsList();

  const { isLoading: isCalendarAuthLoading } = useCalendarAuth({ redirectPath: ROUTES.PREFERENCES });

  useEffect(() => {
    !threadHistoryError &&
      !!foreignUser &&
      getPreferencesHistory(`?user_id=${foreignUser.id}`);
  }, [foreignUser, getPreferencesHistory, threadHistoryError]);

  useEffect(() => {
    if (preferences?.updated_at && threadTimestamp?.lastTimestamp) {
      const preferencesUpdatedAt = new Date(preferences.updated_at).getTime();
      const lastMessageTimestamp = new Date(
        threadTimestamp.lastTimestamp
      ).getTime();

      if (preferencesUpdatedAt > lastMessageTimestamp) {
        switchTo(RightSidebars.PREFERENCES);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [preferences, threadTimestamp]);

  if (!isClient || isCalendarAuthLoading || isLoading) {
    return (
      <div className="flex min-h-dvh items-center justify-center p-6">
        <LoadingSpinner className="w-16 lg:w-24 h-5" />
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-col size-full overflow-y-auto">
        {isDesktop && <PreferencesChatHeader />}
        <div className="flex overflow-y-auto flex-1">
          {isDesktop && (
            <div className="p-6 pt-8 border-r border-neutral-250 dark:border-neutral-800">
              <LeftNav />
            </div>
          )}
          <Chat
            initialMessage={initMessage}
            header={isMobile ? <PreferencesChatHeader /> : undefined}
          />
        </div>
      </div>
      <PreferencesSidebar
        isOpen={rightSidebar === RightSidebars.PREFERENCES}
        onClose={() => switchTo(null)}
      />
    </>
  );
}
