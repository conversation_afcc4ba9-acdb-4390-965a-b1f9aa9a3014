"use client";

import { useState } from "react";
import { useSearchParams } from "next/navigation";
import Header from "@/common/components/header";
import { ROUTES } from "@/common/constants";
import { useClient } from "@/common/hooks/use-client";
import { useFlightSearchEvaluation } from "@/features/admin/hooks/api";
import { useRouter } from "next/navigation";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { Button } from "primereact/button";
import { ScrollPanel } from "primereact/scrollpanel";
import Paginator from "@/common/components/paginator";
import { ADMIN_PAGE_TRIP_LIST_ROWS } from "@/common/constants";
import FlightCard from "@/features/chat/components/rich-controls/flights/flight-card";
import HumanMessagesList from "@/features/admin/components/human-messages-list";
import TravelContextDisplay from "@/features/admin/components/travel-context-display";
import clsx from "clsx";

const ENVIRONMENTS = [
  { label: "All Environments", value: "" },
  { label: "DEV", value: "DEV" },
  { label: "STG", value: "STG" },
  { label: "LIVE", value: "LIVE" },
];

export default function FlightEvaluationPage() {
  const router = useRouter();

  const isClient = useClient();
  const searchParams = useSearchParams();
  
  const [tripId, setTripId] = useState(searchParams.get("trip_id") ?? "");
  const [userEmail, setUserEmail] = useState(searchParams.get("user_email") ?? "");
  const [environment, setEnvironment] = useState(searchParams.get("environment") ?? "");
  const [expandedRows, setExpandedRows] = useState<any>({});
  
  const currentPage = parseInt(searchParams.get("page") ?? "1", 10);
  
  const queryParams: Record<string, string> = {
    page: currentPage.toString(),
    limit: ADMIN_PAGE_TRIP_LIST_ROWS.toString(),
  };
  
  if (tripId) queryParams.trip_id = tripId;
  if (userEmail) queryParams.user_email = userEmail;
  if (environment) queryParams.environment = environment;
  
  const { data, isLoading } = useFlightSearchEvaluation(queryParams);
  const { results = [], total = 0 } = data ?? {};
  
  const handleSearch = () => {
    const params = new URLSearchParams();
    if (tripId) params.set("trip_id", tripId);
    if (userEmail) params.set("user_email", userEmail);
    if (environment) params.set("environment", environment);
    params.set("page", "1");
    
    router.push(`${ROUTES.FLIGHT_EVALUATION}?${params.toString()}`);
  };
  
  const clearFilters = () => {
    setTripId("");
    setUserEmail("");
    setEnvironment("");
    router.push(ROUTES.FLIGHT_EVALUATION);
  };
  
  const renderFlightResults = (rowData: any) => {
    const { main_results, two_stage_results, rank_only_results } = rowData.results;
    
    const renderFlightCards = (results: any, prefix: string) => {
      const cards = [];
      for (const flight of results?.raw || []) {
        cards.push(
          <FlightCard
            key={`${prefix}-${flight._id}`}
            data={flight}
            id={`${prefix}-${rowData._id}-${flight._id}`}
            isDisabled={true}
            isExpired={false}
            isSubmitted={false}
            onSubmit={() => {}}
          />
        );
      }
      return cards;
    };
    
    return (
      <div className="space-y-6 p-4 bg-gray-50">
        <HumanMessagesList 
          messages={rowData.human_messages || []} 
          className="mb-6"
        />
        
        <TravelContextDisplay 
          travelContext={rowData.travel_context} 
          className="mb-6"
        />
        
        <div>
          <h4 className="font-medium mb-3 text-blue-600">Primary Result ({main_results.method})</h4>
          <div className="grid lg:grid-cols-8 gap-2">
            {renderFlightCards(main_results, "main")}
          </div>
        </div>
        
        <div>
          <h4 className="font-medium mb-3 text-green-600">Two Stage Result ({two_stage_results.method})</h4>
          <div className="grid lg:grid-cols-8 gap-2">
            {renderFlightCards(two_stage_results, "two-stage")}
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-3 text-green-600">Rank Only Result ({two_stage_results.method})</h4>
          <div className="grid lg:grid-cols-8 gap-2">
            {renderFlightCards(rank_only_results, "Rank Only")}
          </div>
        </div>        
        
        <div className="mt-4 p-3 bg-white rounded border">
          <h5 className="font-medium mb-2">Context & Metadata</h5>
          <div className="text-sm space-y-1">
            <div><strong>Travel Context:</strong> {rowData.metadata.travel_context_str}</div>
            <div><strong>Airline Codes:</strong> {rowData.metadata.airline_codes.join(', ')}</div>
            <div>
              <strong>Flight Option CSV:</strong>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto whitespace-pre-wrap">
                {rowData.metadata.flight_option_csv}
              </pre>
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  const SearchFilters = (
    <div className="bg-gray-50 p-4 rounded-lg mb-6">
      <h3 className="font-medium mb-4">Search Filters</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <InputText
          value={tripId}
          onChange={(e) => setTripId(e.target.value)}
          placeholder="Trip ID"
        />
        <InputText
          value={userEmail}
          onChange={(e) => setUserEmail(e.target.value)}
          placeholder="User Email"
        />
        <Dropdown
          value={environment}
          onChange={(e) => setEnvironment(e.value)}
          options={ENVIRONMENTS}
          placeholder="Environment"
        />
        <div className="flex gap-2">
          <Button label="Search" onClick={handleSearch} />
          <Button label="Clear" onClick={clearFilters} outlined />
        </div>
      </div>
    </div>
  );
  
  const Content = (
    <div className="px-4 pt-6 md:pt-18 pb-2 w-full max-w-[90rem] mx-auto overflow-y-auto">
      {SearchFilters}
      
      <DataTable
        value={results}
        className={clsx({ "opacity-50": isLoading })}
        emptyMessage="No flight evaluation results found."
        rowExpansionTemplate={renderFlightResults}
        expandedRows={expandedRows}
        onRowToggle={(e) => setExpandedRows(e.data)}
        dataKey="_id"
      >
        <Column expander style={{ width: '3em' }} />
        <Column field="trip_id" header="Trip ID" />
        <Column field="user_email" header="User Email" />
        <Column field="environment" header="Environment" />
        <Column 
          field="created_at" 
          header="Created At"
          body={(rowData) => new Date(rowData.created_at * 1000).toLocaleString()}
        />
        <Column 
          header="Travel Context"
          body={(rowData) => (
            <div className="max-w-xs truncate" title={rowData.metadata.travel_context_str}>
              {rowData.metadata.travel_context_str}
            </div>
          )}
        />
      </DataTable>
      
      {total > 0 && (
        <div className="mt-4">
          <Paginator totalRecords={total} rows={ADMIN_PAGE_TRIP_LIST_ROWS} />
        </div>
      )}
    </div>
  );
  
  if (!isClient) return null;

  
  return (
    <div className="flex flex-col size-full overflow-y-auto">
      <Header title="Flight Search Evaluation" />
      <div className="flex overflow-y-auto flex-1">
        <ScrollPanel
          pt={{
            root: { className: "flex-1 overflow-x-hidden" },
            content: { className: "p-6" },
          }}
        >
          {Content}
        </ScrollPanel>
      </div>
    </div>
  );
}
