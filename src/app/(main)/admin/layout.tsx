"use client";

import { ReactNode, useEffect } from "react";
import { ROUTES } from "@/common/constants";
import WithSidebar from "@/common/components/sidebar/with-sidebar";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";
import { useStoreToggle } from "@/common/hooks/toggles";
import { showSidebarAtom } from "@/common/store/sidebar";
import SidebarButton from "@/common/components/sidebar/sidebar-button";
import { useUserProfile } from "@/features/user/hooks/api";
import { UserRole } from "@/features/user/types/user";
import { useRouter } from "next/navigation";
import LoadingSpinner from "@/common/components/loading-spinner";

const RootLayout = ({
  children,
}: Readonly<{
  children: ReactNode;
}>) => {
  const { role } = useUserProfile();
  const router = useRouter();
  const isAdmin = role === UserRole.ADMIN;
  const { onboardingRedirect } = useOnboardingRedirect();
  const { turnOff: closeSidebar, value: isSidebarOpen } =
    useStoreToggle(showSidebarAtom);

  const onClickLogo = () => {
    closeSidebar();
    onboardingRedirect();
  };

  useEffect(() => {
    !!role && !isAdmin && router.push(ROUTES.HOME);
  }, [isAdmin, role, router]);

  return isAdmin ? (
    <div className="flex relative size-full lg:flex-auto">
      <WithSidebar
        onClickLogo={onClickLogo}
        isOpen={isSidebarOpen}
        onClose={closeSidebar}
      >
        <SidebarButton href={ROUTES.ADMIN_TRIPS}>Trips</SidebarButton>
        <SidebarButton href={ROUTES.ADMIN_USERS}>Users</SidebarButton>
      </WithSidebar>
      <div className="size-full overflow-hidden">{children}</div>
    </div>
  ) : (
    <div className="flex min-h-dvh items-center justify-center p-6 w-full">
      <LoadingSpinner className="w-16 lg:w-24 h-5" />
    </div>
  );
};

export default RootLayout;
