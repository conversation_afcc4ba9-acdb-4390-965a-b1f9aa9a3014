"use client";

import Header from "@/common/components/header";
import Paginator from "@/common/components/paginator";
import { ADMIN_PAGE_USER_LIST_ROWS } from "@/common/constants";
import { isValidSortOrder } from "@/common/utils";
import AllowListInput from "@/features/admin/components/allow-list-input";
import AdminUsersTable from "@/features/admin/components/users-table";
import { useUsersAllowList } from "@/features/admin/hooks/api";
import clsx from "clsx";
import { useSearchParams } from "next/navigation";
import { ScrollPanel } from "primereact/scrollpanel";

export default function AdminUsersPage() {
  const searchParams = useSearchParams();
  const currentPage = parseInt(searchParams.get("page") ?? "1", 10);
  const sortField = searchParams.get("sortField") ?? undefined;
  const sortOrderParam = parseInt(searchParams.get("sortOrder") ?? "0", 10);
  const sortOrder = isValidSortOrder(sortOrderParam) ? sortOrderParam : null;

  const { data, isLoading, exportUsers, isExporting } = useUsersAllowList({
    page: currentPage,
    limit: ADMIN_PAGE_USER_LIST_ROWS,
    sortField,
    sortOrder,
  });
  const { data: users, total } = data ?? { data: [] };

  return (
    <>
      <Header>Users</Header>
      <ScrollPanel
        pt={{
          root: { className: "h-[calc(100dvh-4.3125rem)]" },
          content: { className: "p-6" },
        }}
      >
        <AllowListInput />
        <div className={clsx({ "opacity-50": isLoading })}>
          <AdminUsersTable
            users={users}
            onExport={exportUsers}
            isExporting={isExporting}
            sortField={sortField}
            sortOrder={sortOrder}
          />
        </div>
        <Paginator totalRecords={total ?? 0} rows={ADMIN_PAGE_USER_LIST_ROWS} />
      </ScrollPanel>
    </>
  );
}
