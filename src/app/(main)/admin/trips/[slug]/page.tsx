"use client";

import { useEffect, useRef } from "react";
import { useAtom, useAtomValue } from "jotai";
import { ScrollPanel } from "primereact/scrollpanel";
import { ApiPaths } from "@/common/constants";
import {
  useGetThreadHistory,
  useGetTripOwner,
} from "@/features/admin/hooks/api";
import ChatHistory from "@/features/chat/components/chat-history";
import { chatHistoryAtom, userScrolledAtom } from "@/features/chat/store/chat";

export default function Page({ params }: { params: { slug: string } }) {
  const slug = params.slug;
  const scrollPanelRef = useRef<ScrollPanel>(null);
  const [history, setHistory] = useAtom(chatHistoryAtom);
  const userHasScrolled = useAtomValue(userScrolledAtom);

  const { data: user } = useGetTripOwner(slug);

  const { getThreadHistory } = useGetThreadHistory(
    ApiPaths.ADMIN_GET_TRIP_HISTORY
  );

  useEffect(() => {
    getThreadHistory(`?trip_id=${slug}`);

    return () => setHistory([]);
  }, [getThreadHistory, setHistory, slug]);

  useEffect(() => {
    const element = scrollPanelRef.current?.getContent();

    if (!!element && !userHasScrolled) {
      element.scrollTo(0, element.scrollHeight);
    }
  }, [history, userHasScrolled]);

  return (
    <ScrollPanel
      pt={{
        root: { className: "h-full overflow-x-hidden" },
      }}
      ref={scrollPanelRef}
    >
      <ChatHistory user={user} />
    </ScrollPanel>
  );
}
