"use client";

import Header from "@/common/components/header";
import SearchField from "@/features/admin/components/search-field";
import BookingsTabView from "@/features/admin/components/trips/bookings/bookings-tab-view";
import TripsStatsDateRange from "@/features/admin/components/trips/trips-stats/date-range";
import TripsStats from "@/features/admin/components/trips/trips-stats/trips-stats";
import { ScrollPanel } from "primereact/scrollpanel";

export default function AdminTripsPage() {
  return (
    <>
      <Header>Trips</Header>
      <ScrollPanel
        pt={{
          root: { className: "h-[calc(100dvh-4.3125rem)] overflow-x-hidden" },
          content: { className: "p-4 md:p-6" },
        }}
      >
        <div className="flex flex-col gap-4 items-stretch sm:flex-row">
          <TripsStatsDateRange />
          <SearchField />
        </div>
        <TripsStats />
        <BookingsTabView />
      </ScrollPanel>
    </>
  );
}
