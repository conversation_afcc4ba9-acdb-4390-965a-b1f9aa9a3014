"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAtomValue } from "jotai";
import { ROUTES } from "@/common/constants";
import { isUserSelectedAtom } from "@/features/admin/store/foreign-user";
import Sidebar from "@/common/components/sidebar/main";
import Header from "@/common/components/header";
import { useStoreToggle } from "@/common/hooks/toggles";
import { showSidebarAtom } from "@/common/store/sidebar";
import { TravelPolicyDisplay } from "@/features/travel-policy/components/travel-policy-display";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";
import { useSelectedUserSettings } from "@/features/admin/hooks/api";

export default function UserTravelPolicyPage() {
  const router = useRouter();
  const isUserSelected = useAtomValue(isUserSelectedAtom);
  const { turnOff: onSidebarClose, value: isSidebarOpen } =
    useStoreToggle(showSidebarAtom);

  const { onboardingRedirect } = useOnboardingRedirect();

  const { travelPolicy } = useSelectedUserSettings();

  // Redirect to travel policy page if this page is accessed manually
  useEffect(() => {
    if (!isUserSelected) {
      router.push(ROUTES.TRAVEL_POLICY);
    }
  }, [isUserSelected, router]);

  return (
    <div className="flex size-full relative">
      <Sidebar isOpen={isSidebarOpen} onClose={onSidebarClose} />
      <div className="flex flex-col h-full flex-1 min-w-0">
        <Header
          title="Travel policy"
          showUserSelect
          onClearUserSelect={onboardingRedirect}
        />
        <div className="flex-1 overflow-y-auto">
          <div className="p-6">
            {travelPolicy && (
              <TravelPolicyDisplay travelPolicy={travelPolicy} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
