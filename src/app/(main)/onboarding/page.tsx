"use client";

import { useEffect } from "react";
import { useAtomValue } from "jotai";
import { ApiPaths, ROUTES } from "@/common/constants";
import { onboardingInitMessage } from "@/features/chat/constants/messages";
import { useGetThreadHistory } from "@/features/admin/hooks/api";
import { useStoreToggle } from "@/common/hooks/toggles";
import { showSidebarAtom } from "@/common/store/sidebar";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import Chat from "@/features/chat/components/chat";
import OnboardingChatHeader from "@/features/user/components/onboarding-chat-header";
import OnboardingSidebar from "@/common/components/sidebar/onboarding";
import { useCalendarAuth } from "@/common/hooks/use-calendar-auth";

export default function OnboardingPage() {
  const { turnOff: onSidebarClose, value: isSidebarOpen } =
    useStoreToggle(showSidebarAtom);
  const { getThreadHistory: getOnboardingHistory } = useGetThreadHistory(
    ApiPaths.ADMIN_GET_ONBOARDING_HISTORY
  );

  const foreignUser = useAtomValue(foreignUserAtom);
  const initialMessage = !foreignUser ? onboardingInitMessage : undefined;

  useCalendarAuth({ redirectPath: ROUTES.ONBOARDING });

  useEffect(() => {
    !!foreignUser &&
      !foreignUser?.is_onboarding_completed &&
      getOnboardingHistory(`?user_id=${foreignUser.id}`);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [foreignUser]);

  return (
    <div className="flex h-full relative w-full lg:flex-auto">
      <OnboardingSidebar isOpen={isSidebarOpen} onClose={onSidebarClose} />
      <Chat
        className="flex-auto"
        header={<OnboardingChatHeader />}
        initialMessage={initialMessage}
      />
    </div>
  );
}
