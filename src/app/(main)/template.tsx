"use client";

import { useAccess } from "@/common/hooks/use-access";
import useJ<PERSON><PERSON> from "@/features/js-bridge/hooks/use-bridge";
import { ReactNode, useEffect, useRef } from "react";

export default function MainTemplate({ children }: { children: ReactNode }) {
  const { onNativeCalendarResponse } = useAccess();
  const jSBridge = useJSBridge();
  const calendarCallbackInit = useRef<boolean>(false);

  useEffect(() => {
    if (!calendarCallbackInit.current) {
      calendarCallbackInit.current = true;
      jSBridge({
        onCalendarGranted: onNativeCalendarResponse,
        preventLoadedMessage: true,
      });
    }
  }, [jSBridge, onNativeCalendarResponse]);

  return children;
}
