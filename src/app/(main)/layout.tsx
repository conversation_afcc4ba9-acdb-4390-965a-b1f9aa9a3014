"use client";

import { ReactNode, useEffect, useRef } from "react";
import { ConfirmPopup } from "primereact/confirmpopup";
import usePushNotificationsPopup from "@/common/hooks/use-push-notifications-popup";
import dayjs from "dayjs";
import advancedFormat from "dayjs/plugin/advancedFormat";
import relativeTime from "dayjs/plugin/relativeTime";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import duration from "dayjs/plugin/duration";
import * as nativebridge from "@nrk/nativebridge";
import { OutgoingJSBridgeEvents } from "@/common/types/js-bridge-events";
import { useUserProfile } from "@/features/user/hooks/api";
import TutorialTooltips from "@/features/tutorial/components/tutorial-tooltips";
import { Viewport } from "next";
import { useThemeEffect } from "@/common/hooks/use-theme-effect";
import { PaymentDialog } from "@/features/user/components/payment-dialog";
import { profileformPopup<PERSON>tom } from "@/features/user/store/profile-form-popup";
import { useAtomValue } from "jotai";
import CalendarIntegrationModal from "@/features/chat/components/calendar-integration-modal";
import { WebSocketProvider } from "@/features/chat/context/websocket-context";

export const viewport: Viewport = {
  viewportFit: "cover",
  userScalable: false,
};

dayjs.extend(advancedFormat);
dayjs.extend(relativeTime);
dayjs.extend(timezone);
dayjs.extend(utc);
dayjs.extend(duration);

const RootLayout = ({
  children,
}: Readonly<{
  children: ReactNode;
}>) => {
  const isProfileSentToNative = useRef(false);
  const { PushNotificationsPopup } = usePushNotificationsPopup();
  const { profile } = useUserProfile();
  const contentRef = useRef<HTMLDivElement>(null);

  const profileForm = useAtomValue(profileformPopupAtom);

  useThemeEffect();

  useEffect(() => {
    if (!isProfileSentToNative.current && profile) {
      try {
        nativebridge.emit(OutgoingJSBridgeEvents.USER_PROFILE, {
          id: profile.id,
          email: profile.user_email,
        });
      } catch (e) {}
      isProfileSentToNative.current = true;
    }
  }, [profile]);

  return (
    <WebSocketProvider>
      <div
        className="block overflow-clip relative size-full lg:flex"
        ref={contentRef}
      >
        {children}
      </div>

      <TutorialTooltips />
      <ConfirmPopup />
      {profileForm && <PaymentDialog />}
      {PushNotificationsPopup}
      <CalendarIntegrationModal />
    </WebSocketProvider>
  );
};

export default RootLayout;
