export const DEFAULT_API_URL = "https://dev.api.otto-demo.com";

export enum ApiRestMethod {
  GET = "GET",
  POST = "POST",
  PUT = "PUT",
  DELETE = "DELETE",
  PATCH = "PATCH",
}

export enum HttpStatusCode {
  CONTINUE = 100,
  OK = 200,
  BAD_REQUEST = 400,
  FORBIDDEN = 403,
  GONE = 410,
}

export const COOKIE_ACCESS_TOKEN_NAME = "access_token";
export const COOKIE_REFRESH_TOKEN_NAME = "refresh_token";
export const COOKIE_RETURN_PATH_NAME = "returnPath";
export const COOKIE_OTC_NAME = "otc";

export enum ApiPaths {
  ADMIN_ADD_TO_ALLOW_LIST = "/api/admin/whitelist-users/add-batch",
  ADMIN_GET_TRIP_DATA = "/api/admin/trips",
  ADMIN_GET_FUTURE_TRIPS_HISTORY = "/api/admin/users/future_trips_conversation",
  ADMIN_GET_ONBOARDING_HISTORY = "/api/admin/users/onboarding",
  ADMIN_GET_PREFERENCES_HISTORY = "/api/admin/users/travel_preferences_conversation",
  ADMIN_GET_THREAD_OWNER = "/api/admin/user-trip",
  ADMIN_GET_TRIP_HISTORY = "/api/admin/users/trip_conversation",
  ADMIN_GET_TRIPS_STATS = "/api/admin/statistics",
  ADMIN_GET_TRAVEL_POLICY_HISTORY = "/api/admin/users/travel_policy_conversation",
  ADMIN_GET_USERS = "/api/admin/users",
  ADMIN_GET_USERS_ALLOW_LIST = "/api/admin/whitelist-users/list",
  ADMIN_GET_USERS_TRIPS = "/api/admin/users/trip",
  ADMIN_USER_PROFILE = "/api/admin/users/profile",
  ADMIN_IMPORT_THREAD = "/api/admin/import-thread",
  ADMIN_FLIGHT_SEARCH_EVALUATION = "/api/admin/flight_search_evaluation/data",

  COMPANY_ADMIN_PAYMENT = "/api/company-admin/payment",
  ORGANIZATIONS = "/api/admin/organizations",

  DELETE_ACCOUNT = "/api/user_account_delete",
  PERSONAL_INFORMATION = "/api/user-profile/personal-information",
  GET_SPOTNANA_PROFILE = "/api/spotnana/get-profile",
  CREATE_SPOTNANA_PROFILE = "/api/spotnana/create-profile",
  UPDATE_SPOTNANA_FLYER_NUMBER = "/api/spotnana/frequent-flyer-number",
  NEW_TRIP = "/api/trips/new",
  TRIPS = "/api/trips",
  TRIPS_LIST = "/api/trips/list",
  TRIP_SAMPLES = "/api/trip-samples",
  GOOGLE_LOGIN = "/api/google/login",
  GOOGLE_CALENDAR_TOKEN = "/api/google/login/calendar-token",
  NATIVE_GOOGLE_CALENDAR_TOKEN = "/api/google/login/mobile-calendar-token",
  GOOGLE_CALENDAR_CONNECT = "/api/google/calendar-connect",
  MICROSOFT_LOGIN = "/api/microsoft/login",
  MICROSOFT_CALENDAR_CONNECT = "/api/microsoft/calendar-connect",
  APPLE_LOGIN = "/api/apple/login",
  EMAIL_LOGIN_GENERATE_OTP = "/api/email-login/generate-otp",
  EMAIL_LOGIN_VALIDATE_OTP = "/api/email-login/validate-otp",
  OTC_BETA_REDEEM = "/api/beta/redeem",
  USER_AIRLINE_CREDITS = "/api/user-profile/airline-credits",
  USER_COMPLETE_TUTORIAL = "/api/tutorial-completed",
  USER_FEATURE_FLAGS = "/api/admin/feature-flag",
  USER_LOGOUT = "/api/google/logout",
  USER_PROFILE = "/api/user_profile",
  USER_REMOVE_PREFERENCE = "/api/user_profile/remove_preference",
  USER_PAYMENT_INFORMATION = "/api/user-profile/payment-information",
  USER_PROFILE_HIDE_SAMPLE_TRIP = "/api/user_profile/update_hide_sample_trips",
  USER_SESSION_HISTORY = "/api/user_session_history",
  OPENAI_AUDIO_TRANSCRIPTIONS = "/v1/audio/transcriptions",
  USER_LOYALTY_PROGRAMS = "/api/user-profile/loyalty-programs",
  USER_FLIGHTS_LOYALTY_PROGRAMS = "/api/user-profile/flights-loyalty-programs",
  USER_HOTELS_LOYALTY_PROGRAMS = "/api/user-profile/hotels-loyalty-programs",
}

/** The timeout threshold value in milliseconds. */
export const TimeoutThreshold = 30000;
export const TimeoutErrorName = "TimeoutError";

export const ADMIN_PAGE_USER_LIST_ROWS = 10;
export const ADMIN_PAGE_TRIP_LIST_ROWS = 10;

export const GOOGLE_CALENDAR_SCOPE =
  "https://www.googleapis.com/auth/calendar.events";
