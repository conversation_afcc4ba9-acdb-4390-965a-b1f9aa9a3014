import resolveConfig from "tailwindcss/resolveConfig";
import tailwindConfig from "../../../tailwind.config";

const {
  theme: { screens: TWScreens },
} = resolveConfig(tailwindConfig);

const screens = Object.fromEntries(
  Object.entries(TWScreens)
    .filter(([_, value]) => typeof value === "string")
    .map(([key, value]) => [key, parseInt(value.replace("px", ""), 10)])
);

export default screens;
