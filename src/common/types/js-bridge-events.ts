export enum OutgoingJSBridgeEvents {
  SIGN_IN = "signIn_button_pressed",
  WEB_APP_LOADED = "web_app_loaded",
  WEB_SOCKET_DEBUG = "web_socket_debug",
  USER_PROFILE = "user_profile",
  OTP_LOGIN_SUCCESS = "otp_login_success",
  LOG_OUT_SUCCESS = "log_out_success",
  RECORD_BUTTON_PRESSED = "record_button_pressed",
  MICROPHONE_SETTINGS_BUTTON_PRESSED = "microphone_settings_button_pressed",
  NOTIFICATIONS_SETTINGS_BUTTON_PRESSED = "notifications_settings_button_pressed",
  REQUEST_CALENDAR_ACCESS = "request_calendar_access",
}

export enum IncomingJSBridgeEvents {
  ENABLE_WEB_SOCKET_DEBUG = "enable_web_socket_debug",
  NATIVE_APP_LOADED = "native_app_loaded",
  NOTIFICATION_NEW_TRIP_TAP = "notification_new_trip_tap",
  POST_LOGIN = "post_login",
  LOGIN_ACCESS_DENIED = "login_access_denied",
  SHOW_LOADER = "show_loader",
  HIDE_LOADER = "hide_loader",
  DISPLAY_PUSH_NOTIFICATIONS = "display_push_notifications",
  CALENDAR_ACCESS_GRANTED = "calendar_access_granted",
}
