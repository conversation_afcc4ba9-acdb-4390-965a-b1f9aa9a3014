export const isSafari = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  // Note: Chrome on iOS uses Safari's WebKit engine
  return userAgent.includes("safari") && !userAgent.includes("chrome");
};

export const isClient = () => typeof window !== "undefined";

export const isMobile = () => {
  if (!isClient()) {
    return false; // Cannot determine without window/navigator
  }
  const userAgent = navigator.userAgent || window.opera;
  // Checks for common mobile patterns in user agent string
  return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
    userAgent.toLowerCase(),
  );
};
