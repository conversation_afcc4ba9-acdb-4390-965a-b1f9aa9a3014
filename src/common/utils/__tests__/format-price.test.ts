import { formatPrice } from "../format-price";

describe("formatPrice", () => {
  it("formats number values with 2 decimal places", () => {
    expect(formatPrice(240.3)).toBe("240.30");
    expect(formatPrice(240)).toBe("240.00");
    expect(formatPrice(240.345)).toBe("240.35");
  });

  it("formats string values with 2 decimal places", () => {
    expect(formatPrice("240.3")).toBe("240.30");
    expect(formatPrice("240")).toBe("240.00");
    expect(formatPrice("240.345")).toBe("240.35");
  });

  it("handles negative values by returning absolute value", () => {
    expect(formatPrice(-240.3)).toBe("240.30");
    expect(formatPrice("-240.3")).toBe("240.30");
  });

  it("handles edge cases", () => {
    expect(formatPrice(null)).toBe("0.00");
    expect(formatPrice(undefined)).toBe("0.00");
    expect(formatPrice("invalid")).toBe("0.00");
  });

  it("handles floating point precision issues", () => {
    expect(formatPrice(348.29999999999995)).toBe("348.30");
  });
});
