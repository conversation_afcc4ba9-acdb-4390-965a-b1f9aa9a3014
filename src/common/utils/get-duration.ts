import dayjs from "dayjs";
import { LocalizedTimestamp } from "@/features/chat/types/flights";

export default function getDuration(
  start?: LocalizedTimestamp,
  end?: LocalizedTimestamp
): string | undefined {
  if (
    !start?.timestamp ||
    !start?.timezone ||
    !end?.timestamp ||
    !end?.timezone
  ) {
    return;
  }

  const startDjs = dayjs.tz(start.timestamp, start.timezone);
  const durationInMinutes = dayjs
    .tz(end.timestamp, end.timezone)
    .diff(startDjs, "minutes");

  const formatted = !!durationInMinutes
    ? dayjs
        .duration({
          hours: Math.floor(durationInMinutes / 60),
          minutes: durationInMinutes % 60,
        })
        .format(`${Math.floor(durationInMinutes / 60) > 0 ? "H[h]" : ""}m[m]`)
    : "";

  return formatted;
}
