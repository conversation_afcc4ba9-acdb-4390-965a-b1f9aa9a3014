import { library } from "@fortawesome/fontawesome-svg-core";
import {
  faCircleUser,
  faBookmark,
  faMessage,
} from "@fortawesome/free-regular-svg-icons";
import {
  faPenToSquare,
  faMap,
  faCopy,
  faThumbTack,
  faThumbTackSlash,
  faArrowLeft,
  faCircleExclamation,
  faGridHorizontal,
  faTrash,
  faBan,
  faInfoCircle,
  faWallet,
  faArrowRotateLeft,
  faEllipsisVertical,
  faReceipt,
  faPen,
  faChevronDown as faChevronDownRegular,
  faEllipsis,
  faDownload,
  faMessagePlus,
} from "@fortawesome/pro-regular-svg-icons";
import {
  faAngleDown,
  faBed,
  faPlane,
  faCheck,
  faEye,
  faChevronLeft,
  faArrowUpRightFromSquare,
  faXmark,
  faMapPin,
  faPaperclip,
  faThumbTack as faThumbTackSolid,
  faThumbTackSlash as faThumbTackSlashSolid,
  faChevronRight,
  faChevronDown,
  faCircle1,
  faCircle2,
  faShieldCheck,
  faGear,
} from "@fortawesome/pro-solid-svg-icons";
import { faCircleCheck } from "@fortawesome/free-solid-svg-icons";

library.add(
  faPenToSquare,
  faMap,
  faCheck,
  faCopy,
  faAngleDown,
  faBed,
  faPlane,
  faEye,
  faChevronLeft,
  faArrowUpRightFromSquare,
  faXmark,
  faMapPin,
  faPaperclip,
  faThumbTack,
  faThumbTackSlash,
  faThumbTackSolid,
  faThumbTackSlashSolid,
  faChevronRight,
  faChevronDown,
  faCircle1,
  faCircle2,
  faArrowLeft,
  faCircleCheck,
  faShieldCheck,
  faGear,
  faCircleUser,
  faCircleExclamation,
  faGridHorizontal,
  faTrash,
  faBan,
  faInfoCircle,
  faWallet,
  faArrowRotateLeft,
  faEllipsisVertical,
  faReceipt,
  faPen,
  faChevronDownRegular,
  faBookmark,
  faEllipsis,
  faDownload,
  faMessagePlus,
  faMessage
);
