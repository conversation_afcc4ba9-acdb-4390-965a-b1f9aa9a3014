import { Environment } from "../constants/env";

export const getEnvironment = () =>
  process.env.NEXT_PUBLIC_APP_ENV || Environment.Development;

export const isProduction = () => getEnvironment() === Environment.Production;
export const isStaging = () => getEnvironment() === Environment.Staging;
export const isDevelopment = () => getEnvironment() === Environment.Development;

export const getParentDomain = () => process.env.NEXT_PUBLIC_PARENT_DOMAIN;
