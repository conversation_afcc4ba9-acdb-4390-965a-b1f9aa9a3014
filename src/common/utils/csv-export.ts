/**
 * Converts data array to CSV and triggers download
 */
export async function downloadAsCSV(
  data: any[] | (() => Promise<any[]>),
  filename: string
) {
  const finalData = typeof data === 'function' ? await data() : data;
  if (!finalData?.length) return;
  
  const headers = Object.keys(finalData[0]);
  const csvContent = [
    headers.join(','),
    ...finalData.map(row => 
      headers.map(header => {
        const value = row[header];
        // Handle special cases like booleans (convert to ✅/❌)
        if (typeof value === 'boolean') {
          return value ? '✅' : '❌';
        }
        // Escape and quote strings that contain commas
        if (typeof value === 'string' && value.includes(',')) {
          return `"${value}"`;
        }
        return value ?? '';
      }).join(',')
    )
  ].join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
  URL.revokeObjectURL(link.href);
}
