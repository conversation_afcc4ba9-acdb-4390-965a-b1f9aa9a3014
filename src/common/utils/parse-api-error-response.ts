export default function parseApiErrorResponse(data: any) {
  if (typeof data === "string") {
    return data;
  } else if (typeof data.detail === "string") {
    return data.detail;
  } else if (typeof data?.detail?.message === "string") {
    return data.detail.message;
  } else if (Array.isArray(data?.detail && !!data.detail?.[0]?.msg)) {
    const { loc, msg } = data.detail[0];
    return `${msg} [${loc[loc.length - 1]}]`;
  }

  return null;
}
