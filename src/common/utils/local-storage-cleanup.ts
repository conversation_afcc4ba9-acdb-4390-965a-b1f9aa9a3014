import {
  ACCOMMODATION_SELECTIONS_KEY,
  AGENTS_DEBUG_KEY,
  ENABLE_DELETE_MESSAGE_KEY,
  FLIGHT_CARDS_DEBUG_KEY,
  RAW_TOOL_OUTPUT_DEBUG_KEY,
  TRAVEL_CONTEXT_DEBUG_KEY,
  USER_SELECT_DEBUG_KEY,
} from "@/features/chat/constants/local-storage-keys";
import {
  PLANNING_TRIPS_EXPANDED_ALL_KEY,
  PLANNING_TRIPS_EXPANDED_KEY,
  BOOKED_TRIPS_EXPANDED_ALL_KEY,
  BOOKED_TRIPS_EXPANDED_KEY,
  DARK_MODE_KEY,
} from "@/common/constants/local-storage-keys";
import {
  ENABLE_MEMORY_KEY,
  ENABLE_SAVED_TRIP_KEY,
} from "@/features/user/constants/feature-flags";
import { LAST_LOGGED_IN_GMAIL_KEY } from "@/common/constants/local-storage-keys";

// List of all localStorage keys that should be cleared on logout
const CLEARABLE_KEYS = [
  // Debug flags
  AGENTS_DEBUG_KEY,
  ENABLE_DELETE_MESSAGE_KEY,
  FLIGHT_CARDS_DEBUG_KEY,
  RAW_TOOL_OUTPUT_DEBUG_KEY,
  TRAVEL_CONTEXT_DEBUG_KEY,
  USER_SELECT_DEBUG_KEY,

  // Feature flags
  ENABLE_MEMORY_KEY,
  ENABLE_SAVED_TRIP_KEY,
  
  LAST_LOGGED_IN_GMAIL_KEY,

  // UI state
  PLANNING_TRIPS_EXPANDED_ALL_KEY,
  PLANNING_TRIPS_EXPANDED_KEY,
  BOOKED_TRIPS_EXPANDED_ALL_KEY,
  BOOKED_TRIPS_EXPANDED_KEY,
  DARK_MODE_KEY,
  ACCOMMODATION_SELECTIONS_KEY,
] as const;

/**
 * Cleans up localStorage by removing all non-preserved keys.
 * This should be called during logout
 */
export function cleanupLocalStorage() {
  CLEARABLE_KEYS.forEach((key) => localStorage.removeItem(key));
}
