import { Flight, FlightSegment } from "@/features/chat/types/api";
import { FlightData } from "@/features/chat/types/flights";
import { dateWithTimezone } from "./date";
import dayjs, { Dayjs } from "dayjs";

export function formatFlightNumbers(flight?: FlightData | Flight): string {
  if (!flight?.flight_segments?.length) {
    return "";
  }

  const flightStops = flight.flight_segments[0]?.flight_stops;
  if (!flightStops?.length) {
    return "";
  }

  return flightStops
    .map(
      ({ airline_code, flight_number }) => `${airline_code} ${flight_number}`
    )
    .join(", ");
}

export function formatFlightTimes(flight: FlightData | Flight): {
  departureTime: string;
  arrivalTime: string;
  departureDate: Dayjs;
  arrivalDate: Dayjs;
} {
  const flightStops = flight.flight_segments[0]?.flight_stops;
  const firstStop = flightStops[0];
  const lastStop = flightStops[flightStops.length - 1];

  const departureTime = dateWithTimezone(
    firstStop.departure,
    firstStop.departure_timezone
  ).format("h:mma");
  const arrivalTime = dateWithTimezone(
    lastStop.arrival,
    lastStop.arrival_timezone
  ).format("h:mma");
  const departureDate = dateWithTimezone(
    firstStop.departure,
    firstStop.departure_timezone
  );
  const arrivalDate = dateWithTimezone(
    lastStop.arrival,
    lastStop.arrival_timezone
  );

  return {
    departureTime,
    arrivalTime,
    departureDate,
    arrivalDate,
  };
}

export function formatFlightSegmentDuration(flightSegment: FlightSegment) {
  const flightStops = flightSegment?.flight_stops;
  const firstStop = flightStops[0];
  const lastStop = flightStops[flightStops.length - 1];

  const departureTime = dateWithTimezone(
    firstStop.departure,
    firstStop.departure_timezone
  );
  const arrivalTime = dateWithTimezone(
    lastStop.arrival,
    lastStop.arrival_timezone
  );

  const durationInMinutes = arrivalTime.diff(departureTime, "minutes");

  return dayjs
    .duration({
      hours: Math.floor(durationInMinutes / 60),
      minutes: durationInMinutes % 60,
    })
    .format(`${Math.floor(durationInMinutes / 60) > 0 ? "H[h]" : ""} m[m]`);
}

export function formatFlightDuration(flight: FlightData | Flight) {
  return formatFlightSegmentDuration(flight.flight_segments[0]);
}

export function getArrivalDays(departureDate: Dayjs, arrivalDate: Dayjs) {
  const depDay = departureDate.startOf("day");
  const arrDay = arrivalDate.startOf("day");
  return arrDay.diff(depDay, "days");
}
