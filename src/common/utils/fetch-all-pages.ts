import { BookingListResponse } from "@/features/admin/types/api";
import { fetcher } from "@/common/api/fetcher";

/**
 * Recursively fetches all pages of data from a paginated API endpoint
 */
export async function fetchAllPages(
  baseUrl: string,
  currentPage: number = 1
): Promise<BookingListResponse['data']> {
  const response = await fetcher(baseUrl + `&page=${currentPage}`);
  const { data, total } = response as BookingListResponse;
  
  if (!total || !data?.length) return data;
  
  const totalPages = Math.ceil(total / data.length);
  if (currentPage >= totalPages) return data;
  
  const nextPageData = await fetchAllPages(baseUrl, currentPage + 1);
  return [...data, ...nextPageData];
}
