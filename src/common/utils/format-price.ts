/**
 * Formats a price value to always show 2 decimal places
 * @param value - The price value to format (can be string or number)
 * @returns Formatted price string with exactly 2 decimal places
 */
export function formatPrice(value: string | number | null | undefined): string {
  if (value === null || value === undefined) {
    return "0.00";
  }

  const numValue = typeof value === "string" ? parseFloat(value) : value;
  
  if (Number.isNaN(numValue)) {
    return "0.00";
  }

  return (Math.round(Math.abs(numValue) * 100) / 100).toFixed(2);
}
