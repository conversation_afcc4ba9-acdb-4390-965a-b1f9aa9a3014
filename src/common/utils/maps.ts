/**
 * Get a Google maps bounds literal object from set of coordinates.
 */
export function getMapBounds(
  positions: { lat: number; lng: number }[]
): google.maps.LatLngBoundsLiteral {
  const mapBounds: google.maps.LatLngBoundsLiteral = {
    east: -180,
    north: -90,
    south: 90,
    west: 180,
  };

  positions.forEach((item) => {
    const { lat, lng } = item;

    mapBounds.east = Math.max(lng, mapBounds.east);
    mapBounds.west = Math.min(lng, mapBounds.west);
    mapBounds.north = Math.max(lat, mapBounds.north);
    mapBounds.south = Math.min(lat, mapBounds.south);
  });

  return mapBounds;
}

/**
 * Get the coordinates that are the furthest from the origin.
 *
 * Note: The result coordinates might not come from the same set.
 */
export function getFurthestPointCoordinates(
  origin: google.maps.LatLngLiteral,
  points: google.maps.LatLngLiteral[]
) {
  return points.reduce(
    (prev, current) => ({
      lat:
        Math.abs(origin.lat - current.lat) > Math.abs(origin.lat - prev.lat)
          ? current.lat
          : prev.lat,
      lng:
        Math.abs(origin.lng - current.lng) > Math.abs(origin.lng - prev.lng)
          ? current.lng
          : prev.lng,
    }),
    origin
  );
}
