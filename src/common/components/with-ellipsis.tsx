import { ReactNode, useEffect, useRef, useState } from "react";
import { Tooltip } from "./tooltip";

export function WithEllipsis({
  id,
  children,
  onTruncatedChange,
  showTooltip = true,
}: {
  id: string;
  children: ReactNode;
  onTruncatedChange?: (isTruncated: boolean) => void;
  showTooltip?: boolean;
}) {
  const tooltipId = `tooltip-${id}`;
  const contentRef = useRef<HTMLDivElement>(null);
  const [isTruncated, setIsTruncated] = useState(false);

  useEffect(() => {
    const element = contentRef.current;
    if (element) {
      const truncated = element.scrollWidth > element.clientWidth;
      setIsTruncated(truncated);
      onTruncatedChange?.(truncated);
    }
  }, [children, onTruncatedChange]);

  return (
    <>
      <div
        ref={contentRef}
        className="truncate truncate-1-lines"
        data-tooltip-id={isTruncated ? tooltipId : undefined}
      >
        {children}
      </div>
      {isTruncated && showTooltip && (
        <Tooltip id={tooltipId}>{children}</Tooltip>
      )}
    </>
  );
}
