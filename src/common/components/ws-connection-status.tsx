import { useAtomValue, use<PERSON>et<PERSON><PERSON> } from "jotai";
import { But<PERSON> } from "primereact/button";
import { WSReconnectState } from "@/features/chat/types/web-socket";
import {
  connectWebsocketAtom,
  reconnectAttemptAtom,
} from "@/features/chat/store/chat";
import IconRotate from "@/common/components/icons/rotate";

export default function WSConnectionStatus() {
  const reconnectAttempt = useAtomValue(reconnectAttemptAtom);
  const setConnectWebsocket = useSetAtom(connectWebsocketAtom);

  if (reconnectAttempt !== WSReconnectState.FAILED) {
    return;
  }

  const onReconnect = () => setConnectWebsocket(true);

  return (
    <Button
      className="absolute btn-gradient leading-4.5 rounded-full left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-10"
      onClick={onReconnect}
    >
      The connection has stopped, click here to reconnect.
      <IconRotate
        className="shrink-0 grow-0 basis-4"
        fill="white"
        width={16}
        height={16}
      />
    </Button>
  );
}
