import {
  AdvancedMarker,
  useAdvancedMarkerRef,
} from "@vis.gl/react-google-maps";
import { SVGProps } from "react";
import { MapMarker as MapMarkerType } from "@/common/types/map";
import IconMapMarker from "../icons/map-marker";
import clsx from "clsx";

type MapMarkerProps = MapMarkerType &
  SVGProps<SVGSVGElement> & {
    isActive?: boolean;
    onClick?: VoidFunction;
    zIndex?: number;
  };

export function MapMarker({
  children,
  isActive,
  onClick,
  position,
  zIndex,
  ...svgProps
}: MapMarkerProps) {
  const [markerRef, marker] = useAdvancedMarkerRef();

  if (isNaN(position?.lat) || isNaN(position?.lng)) {
    return null;
  }

  return (
    <>
      <AdvancedMarker
        onClick={onClick}
        position={position}
        ref={markerRef}
        zIndex={zIndex}
      >
        <IconMapMarker
          {...svgProps}
          className={clsx(svgProps.className, {
            "text-neutral-460 [&_path]:stroke-neutral-490": !isActive,
            "text-black": isActive,
          })}
        />
      </AdvancedMarker>
    </>
  );
}
