import colors from "@/common/constants/colors";
import { MapCircle, MapMarker } from "@/common/components/maps";

type MapSearchLocationProps = {
  coordinates?: { lat?: number; lng?: number };
  radius?: number;
};

export function MapSearchLocation({
  coordinates,
  radius,
}: MapSearchLocationProps) {
  const { lat, lng } = coordinates ?? {};

  if (!lat || !lng) {
    return;
  }

  return (
    <>
      <MapMarker
        className="text-red-500 [&_path]:stroke-red-700"
        position={coordinates as google.maps.LatLngLiteral}
      />
      {!!radius && (
        <MapCircle
          fillColor={colors.primary[500]}
          fillOpacity={0.1}
          strokeColor={colors.primary[500]}
          strokeOpacity={0.3}
          center={coordinates as google.maps.LatLngLiteral}
          radius={radius}
        />
      )}
    </>
  );
}
