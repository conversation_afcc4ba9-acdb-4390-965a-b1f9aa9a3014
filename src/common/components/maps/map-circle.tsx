import {
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from "react";
import type { Ref } from "react";
import { GoogleMapsContext, latLngEquals } from "@vis.gl/react-google-maps";

export type CircleProps = google.maps.CircleOptions;
export type CircleRef = Ref<google.maps.Circle | null>;

function useCircle(props: CircleProps) {
  const { radius, center, ...circleOptions } = props;

  const circle = useRef(new google.maps.Circle()).current;
  circle.setOptions(circleOptions);

  useEffect(() => {
    if (!center) return;
    if (!latLngEquals(center, circle.getCenter())) circle.setCenter(center);
  }, [center, circle]);

  useEffect(() => {
    if (radius == null) return;
    if (radius !== circle.getRadius()) circle.setRadius(radius);
  }, [radius, circle]);

  const map = useContext(GoogleMapsContext)?.map;

  useEffect(() => {
    if (!map) {
      if (map === undefined)
        console.error("<Circle> has to be inside a Map component.");

      return;
    }

    circle.setMap(map);

    return () => {
      circle.setMap(null);
    };
  }, [map, circle]);

  return circle;
}

const MapCircle = forwardRef((props: CircleProps, ref: CircleRef) => {
  const circle = useCircle(props);
  useImperativeHandle(ref, () => circle);

  return null;
});

MapCircle.displayName = "MapCircle";

export { MapCircle };
