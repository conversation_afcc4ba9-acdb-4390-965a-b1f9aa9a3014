import { But<PERSON> } from "primereact/button";
import { Dialog } from "primereact/dialog";

interface PermissionPopupProps {
  title: string;
  description: string;
  visible?: boolean;
  onHide: VoidFunction;
  onConfirm: VoidFunction;
}

export default function PermissionPopup({
  title,
  description,
  visible,
  onHide,
  onConfirm,
}: PermissionPopupProps) {
  return (
    <Dialog visible={visible} onHide={onHide} draggable={false}>
      <div className="text-center space-y-2">
        <h1 className="text-xl leading-5 font-semibold">{title}</h1>
        <p className="leading-5 px-2">{description}</p>
      </div>
      <Button
        label="Go to Settings"
        className="w-full"
        size="large"
        onClick={onConfirm}
      />
      <Button label="Cancel" className="p-0" plain onClick={onHide} />
    </Dialog>
  );
}
