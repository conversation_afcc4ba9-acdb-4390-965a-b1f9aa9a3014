import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { But<PERSON> } from "primereact/button";
import { XmarkLargeSolid } from "@/common/components/icons/xmark-large-solid";
import { twMerge } from "tailwind-merge";
import { ReactNode } from "react";
import clsx from "clsx";

export const SettingsHeader = ({
  className,
  title,
  onBack,
  onClose,
  children,
}: {
  className?: string;
  title?: string;
  onBack?: VoidFunction;
  onClose?: VoidFunction;
  children?: ReactNode;
}) => {
  return (
    <div
      className={twMerge(
        "flex items-center justify-between p-3.5 gap-2 w-full bg-white dark:bg-gray-800",
        className
      )}
    >
      <Button
        onClick={onBack}
        text
        rounded
        className={clsx({ invisible: !onBack })}
      >
        <FontAwesomeIcon icon="chevron-left" className="text-lg size-4.5" />
      </Button>
      <h1 className="text-xl leading-5.5 font-medium">{title}</h1>
      {children}
      <Button
        onClick={onClose}
        text
        rounded
        className={clsx({
          invisible: !onClose,
          hidden: !onClose && !!children,
        })}
      >
        <XmarkLargeSolid />
      </Button>
    </div>
  );
};
