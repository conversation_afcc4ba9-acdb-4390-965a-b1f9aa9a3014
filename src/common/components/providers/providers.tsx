"use client";

import { fetcher } from "@/common/api/fetcher";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { ReactNode } from "react";
import { SWRConfig } from "swr";
import { Provider as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "jotai";
import { twMerge } from "tailwind-merge";
import { PrimeReactProvider } from "primereact/api";
import designSystem from "@/common/styles/designSystem";
import { APIProvider } from "@vis.gl/react-google-maps";

const Providers = ({
  children,
  googleClientId = "",
  googleMapsAPIKey = "",
}: {
  children: ReactNode;
  googleClientId?: string;
  googleMapsAPIKey?: string;
}) => {
  return (
    <GoogleOAuthProvider clientId={googleClientId}>
      <SWRConfig value={{ fetcher }}>
        <PrimeReactProvider
          value={{
            unstyled: true,
            pt: designSystem,
            ptOptions: {
              mergeSections: true,
              mergeProps: true,
              classNameMergeFunction: twMerge,
            },
          }}
        >
          <JotaiProvider>
            <APIProvider apiKey={googleMapsAPIKey}>{children}</APIProvider>
          </JotaiProvider>
        </PrimeReactProvider>
      </SWRConfig>
    </GoogleOAuthProvider>
  );
};

export { Providers };
