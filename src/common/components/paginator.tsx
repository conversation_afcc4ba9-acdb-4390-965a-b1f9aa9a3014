import { useSearchParams } from "next/navigation";
import { <PERSON><PERSON><PERSON> as PRPaginator } from "primereact/paginator";
import { useUpdateQueryString } from "../hooks/use-query-string";
import { ADMIN_PAGE_USER_LIST_ROWS } from "../constants";

type PaginatorProps = {
  totalRecords: number;
  rows: number;
};

export default function Paginator({ totalRecords, rows }: PaginatorProps) {
  const searchParams = useSearchParams();
  const currentPage = parseInt(searchParams.get("page") ?? "1", 10);
  const updateQueryString = useUpdateQueryString();

  return (
    <PRPaginator
      first={(currentPage - 1) * rows}
      rows={rows}
      totalRecords={totalRecords}
      onPageChange={(e) => updateQueryString({ page: (e.page + 1).toString() })}
    />
  );
}
