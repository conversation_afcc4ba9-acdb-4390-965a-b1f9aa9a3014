import { SVGProps } from "react";

export default function IconCheckSolid({
  className,
  fill = "#08C0CC",
  height = "24",
  width = "24",
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      className={className}
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        clipRule="evenodd"
        d="M11.9999 24C18.6274 24 23.9999 18.6274 23.9999 12C23.9999 5.37259 18.6274 0 11.9999 0C5.37253 0 -6.10352e-05 5.37259 -6.10352e-05 12C-6.10352e-05 18.6274 5.37253 24 11.9999 24ZM18.4861 9.30687C19.0042 8.72073 18.9762 7.8001 18.4237 7.25061C17.871 6.70109 17.0033 6.7308 16.4852 7.31693L10.5414 14.0421L7.42537 11.1039C6.85926 10.5702 5.99241 10.6243 5.48922 11.2247C4.98602 11.8252 5.03701 12.7448 5.60312 13.2785L9.05192 16.5303C9.97779 17.4034 11.3907 17.3352 12.238 16.3766L18.4861 9.30687Z"
        fill={fill}
        fillRule="evenodd"
      />
    </svg>
  );
}
