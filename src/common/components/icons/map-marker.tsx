import { SVGProps } from "react";

export default function IconMapMarker({
  className,
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      className={className}
      fill="none"
      height="42"
      viewBox="0 0 22 30"
      width="30"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 0C15.5228 0 20 4.47716 20 10C20 11.9646 18.9746 14.6479 17.1566 18.0914C16.7196 18.9192 16.242 19.7778 15.73 20.6604C14.8366 22.2004 13.8819 23.7408 12.9273 25.2106C12.5932 25.7252 12.2826 26.1944 12.0034 26.6098L11.6456 27.1366C10.8505 28.2878 9.14952 28.2878 8.35442 27.1366L7.7072 26.1768L7.0727 25.2106C6.1181 23.7408 5.16338 22.2004 4.27 20.6604C3.75806 19.7778 3.2804 18.9192 2.84332 18.0914C1.02544 14.6479 0 11.9646 0 10C0 4.47716 4.47716 0 10 0ZM10 6C7.79086 6 6 7.79086 6 10C6 12.2091 7.79086 14 10 14C12.2091 14 14 12.2091 14 10C14 7.79086 12.2091 6 10 6Z"
        fill="currentColor"
        strokeWidth="1"
        transform="translate(1 1)"
      />
    </svg>
  );
}
