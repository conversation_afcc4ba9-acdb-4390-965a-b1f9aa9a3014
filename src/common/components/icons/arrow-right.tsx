import { SVGProps } from "react";

export const ArrowRight = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="26"
      height="40"
      viewBox="0 0 26 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="none"
      {...props}
    >
      <g filter="url(#filter0_d_124_7218)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.14243 33.6986L5.55664 35.1128L19.6988 20.9707L21.113 19.5564L19.6988 18.1422L5.55664 4.0001L4.14242 5.41431L18.2846 19.5564L4.14243 33.6986Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_124_7218"
          x="0.142578"
          y="0.00012207"
          width="24.9707"
          height="39.1127"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.7 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_124_7218"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_124_7218"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};
