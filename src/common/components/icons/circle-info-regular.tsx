// Fontawesome pro regular icon

import { SVGProps } from "react";

export function CircleInfoRegular({
  size = 10,
  ...props
}: SVGProps<SVGSVGElement> & {
  size?: number;
}) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 10 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5 0.9375C6.07744 0.9375 7.11075 1.36551 7.87262 2.12738C8.63449 2.88925 9.0625 3.92256 9.0625 5C9.0625 6.07744 8.63449 7.11075 7.87262 7.87262C7.11075 8.63449 6.07744 9.0625 5 9.0625C3.92256 9.0625 2.88925 8.63449 2.12738 7.87262C1.36551 7.11075 0.9375 6.07744 0.9375 5C0.9375 3.92256 1.36551 2.88925 2.12738 2.12738C2.88925 1.36551 3.92256 0.9375 5 0.9375ZM5 10C6.32608 10 7.59785 9.47322 8.53553 8.53553C9.47322 7.59785 10 6.32608 10 5C10 3.67392 9.47322 2.40215 8.53553 1.46447C7.59785 0.526784 6.32608 0 5 0C3.67392 0 2.40215 0.526784 1.46447 1.46447C0.526784 2.40215 0 3.67392 0 5C0 6.32608 0.526784 7.59785 1.46447 8.53553C2.40215 9.47322 3.67392 10 5 10ZM4.21875 6.5625C3.95898 6.5625 3.75 6.77148 3.75 7.03125C3.75 7.29102 3.95898 7.5 4.21875 7.5H5.78125C6.04102 7.5 6.25 7.29102 6.25 7.03125C6.25 6.77148 6.04102 6.5625 5.78125 6.5625H5.625V4.84375C5.625 4.58398 5.41602 4.375 5.15625 4.375H4.21875C3.95898 4.375 3.75 4.58398 3.75 4.84375C3.75 5.10352 3.95898 5.3125 4.21875 5.3125H4.6875V6.5625H4.21875ZM5 3.75C5.16576 3.75 5.32473 3.68415 5.44194 3.56694C5.55915 3.44973 5.625 3.29076 5.625 3.125C5.625 2.95924 5.55915 2.80027 5.44194 2.68306C5.32473 2.56585 5.16576 2.5 5 2.5C4.83424 2.5 4.67527 2.56585 4.55806 2.68306C4.44085 2.80027 4.375 2.95924 4.375 3.125C4.375 3.29076 4.44085 3.44973 4.55806 3.56694C4.67527 3.68415 4.83424 3.75 5 3.75Z"
        fill="currentColor"
      />
    </svg>
  );
}
