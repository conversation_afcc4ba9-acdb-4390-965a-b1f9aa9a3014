import clsx from "clsx";

type PillProps = {
  className?: string;
  number: number | string;
};

export default function NumberPill({ className, number }: PillProps) {
  return (
    <div
      className={clsx(
        "flex items-center justify-center rounded-full w-fit px-1 py-0.5 min-w-6 h-fit",
        className
      )}
    >
      <span className="leading-none">{number}</span>
    </div>
  );
}
