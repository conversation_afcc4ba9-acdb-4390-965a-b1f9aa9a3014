import { SVGProps } from "react";
import colors from "@/common/constants/colors";

export default function IconStopSolid({
  className,
  fill = colors.primary[500],
  height = "41",
  stroke = colors.white,
  width = "40",
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      className={className}
      width={width}
      height={height}
      viewBox="0 0 40 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.968994" width="40" height="40" rx="20" fill={fill} />
      <rect
        x="9"
        y="9.96899"
        width="22"
        height="22"
        rx="11"
        stroke={stroke}
        strokeWidth="2"
      />
      <rect x="15" y="15.969" width="10" height="10" rx="2" fill="white" />
    </svg>
  );
}
