import { SVGProps } from "react";

export default function IconArrowSolid({
  className,
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      className={className}
      fill="none"
      height="41"
      viewBox="0 0 40 41"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect height="40" rx="20" width="40" y="0.968994" />
      <path d="M14.4318 17.2472C13.9227 17.6715 13.8539 18.4282 14.2782 18.9373C14.7024 19.4464 15.4591 19.5152 15.9682 19.0909L18.8 16.7312L18.8 27.769C18.8 28.4317 19.3372 28.969 19.9999 28.969C20.6627 28.969 21.1999 28.4317 21.1999 27.769L21.1999 16.731L24.0317 19.0909C24.5409 19.5152 25.2975 19.4464 25.7218 18.9373C26.1461 18.4282 26.0773 17.6715 25.5682 17.2472L20.7682 13.2472C20.3232 12.8764 19.6768 12.8764 19.2318 13.2472L14.4318 17.2472Z" />
    </svg>
  );
}
