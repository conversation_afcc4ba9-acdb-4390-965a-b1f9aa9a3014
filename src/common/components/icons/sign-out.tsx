import { SVGProps } from "react";
import colors from "@/common/constants/colors";

export default function IconSignOut({
  fill = colors.primary[500],
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      fill="none"
      height="16"
      viewBox="0 0 16 16"
      width="16"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7 6.99998C7.33244 7.00669 7.66289 6.94702 7.97199 6.82446C8.28109 6.7019 8.56263 6.51891 8.80014 6.28619C9.03765 6.05348 9.22635 5.77573 9.35519 5.46919C9.48403 5.16266 9.55043 4.8335 9.5505 4.50099C9.55056 4.16848 9.4843 3.83929 9.35558 3.5327C9.22686 3.22612 9.03827 2.94829 8.80085 2.71548C8.56344 2.48268 8.28197 2.29957 7.97292 2.17688C7.66387 2.0542 7.33345 1.9944 7.001 2.00098C6.34675 2.01393 5.72365 2.2829 5.26549 2.75013C4.80733 3.21736 4.55063 3.84561 4.5505 4.49999C4.55036 5.15437 4.80681 5.78272 5.26479 6.25013C5.72276 6.71755 6.34575 6.98676 7 6.99998ZM7 7.99998C5.665 7.99998 3 8.89298 3 10.667V11.333C3 11.7 3.225 12 3.5 12H5.549C6.453 11.091 7.966 10.089 10.276 9.99098V9.27098C10.2759 9.24978 10.2782 9.22863 10.283 9.20798C9.397 8.40398 7.898 7.99998 7 7.99998ZM11.427 10.028C11.4727 10.0053 11.524 9.99629 11.5747 10.002C11.6254 10.0076 11.6734 10.0278 11.713 10.06L13.876 11.783C13.9066 11.8075 13.9315 11.8383 13.949 11.8733C13.9665 11.9084 13.9763 11.9468 13.9775 11.986C13.9787 12.0251 13.9715 12.0641 13.9562 12.1002C13.9409 12.1363 13.918 12.1686 13.889 12.195L11.726 14.165C11.6873 14.2003 11.6391 14.2236 11.5874 14.232C11.5357 14.2405 11.4827 14.2337 11.4347 14.2124C11.3868 14.1912 11.3461 14.1566 11.3176 14.1126C11.289 14.0687 11.2739 14.0174 11.274 13.965V13.009C7.946 13.142 5.992 14.517 5.987 14.544C5.9768 14.6072 5.94444 14.6648 5.89571 14.7064C5.84699 14.7479 5.78506 14.7708 5.721 14.771H5.699C5.63092 14.7657 5.56738 14.7348 5.52118 14.6845C5.47497 14.6342 5.44954 14.5683 5.45 14.5C5.45 14.454 6.999 11.172 11.274 10.991V10.271C11.2741 10.2202 11.2885 10.1704 11.3155 10.1275C11.3426 10.0845 11.3812 10.05 11.427 10.028Z"
        fill={fill}
      />
    </svg>
  );
}
