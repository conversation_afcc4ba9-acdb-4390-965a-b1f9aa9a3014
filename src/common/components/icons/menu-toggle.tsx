import colors from "@/common/constants/colors";
import { SVGProps } from "react";

export default function IconMenuToggle({
  fill = colors.neutral[600],
  ...restProps
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...restProps}
      fill="none"
      height="20"
      viewBox="0 0 20 20"
      width="20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20 9.6L20 8.4C20 7.73726 19.2538 7.2 18.3333 7.2L1.66667 7.2C0.746191 7.2 -3.38206e-07 7.73726 -3.67176e-07 8.4L-4.19629e-07 9.6C-4.48599e-07 10.2627 0.746191 10.8 1.66667 10.8L18.3333 10.8C19.2538 10.8 20 10.2627 20 9.6Z"
        fill={fill}
      />
      <path
        d="M20 2.4L20 1.2C20 0.537258 19.2538 -3.26171e-08 18.3333 -7.28524e-08L1.66667 -5.15273e-07C0.746191 -5.55508e-07 -2.34843e-08 0.537258 -5.24537e-08 1.2L-1.04907e-07 2.4C-1.33877e-07 3.06274 0.746191 3.6 1.66667 3.6L18.3333 3.6C19.2538 3.6 20 3.06274 20 2.4Z"
        fill={fill}
      />
      <path
        d="M20 16.8L20 15.6C20 14.9373 19.2538 14.4 18.3333 14.4L1.66667 14.4C0.74619 14.4 -6.52928e-07 14.9373 -6.81898e-07 15.6L-7.34351e-07 16.8C-7.63321e-07 17.4627 0.74619 18 1.66667 18L18.3333 18C19.2538 18 20 17.4627 20 16.8Z"
        fill={fill}
      />
    </svg>
  );
}
