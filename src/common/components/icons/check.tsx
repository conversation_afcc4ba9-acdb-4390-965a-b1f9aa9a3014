import { SVGProps } from "react";

export default function IconCheck({
  size = 10,
  ...props
}: {
  size?: number;
} & SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={size}
      height={size * 0.7}
      viewBox="0 0 10 7"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.79185 0.201416C10.0709 0.469971 10.0709 0.906103 9.79185 1.17466L4.07757 6.67466C3.79855 6.94321 3.34542 6.94321 3.06641 6.67466L0.209263 3.92466C-0.0697545 3.6561 -0.0697545 3.21997 0.209263 2.95142C0.488281 2.68286 0.941406 2.68286 1.22042 2.95142L3.5731 5.21372L8.78292 0.201416C9.06194 -0.0671387 9.51507 -0.0671387 9.79409 0.201416H9.79185Z"
        fill="currentColor"
      />
    </svg>
  );
}
