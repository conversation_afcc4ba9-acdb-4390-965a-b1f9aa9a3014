import colors from "@/common/constants/colors";
import { SVGProps } from "react";

export default function IconExclamationSolid({
  className,
  fill = colors.white,
  height = 20,
  width = 20,
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      className={className}
      fill="none"
      height={height}
      viewBox="0 0 20 20"
      width={width}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 20C15.5229 20 20 15.5229 20 10C20 4.47716 15.5229 0 10 0C4.47716 0 0 4.47716 0 10C0 15.5229 4.47716 20 10 20ZM8.60893 5.03567C8.58853 4.62769 8.91383 4.28571 9.32231 4.28571H10.6777C11.0862 4.28571 11.4115 4.62769 11.3911 5.03567L11.1054 10.75C11.0864 11.1301 10.7726 11.4286 10.392 11.4286H9.60803C9.22741 11.4286 8.91364 11.1301 8.89464 10.75L8.60893 5.03567ZM8.57143 14.2857C8.57143 13.4967 9.21103 12.8571 10 12.8571C10.789 12.8571 11.4286 13.4967 11.4286 14.2857C11.4286 15.0747 10.789 15.7143 10 15.7143C9.21103 15.7143 8.57143 15.0747 8.57143 14.2857Z"
        fill={fill}
      />
    </svg>
  );
}
