import { SVGProps } from "react";
import colors from "@/common/constants/colors";

export default function IconClose({
  fill = colors.neutral[600],
  height = "21",
  width = "21",
  ...restProps
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...restProps}
      height={height}
      width={width}
      viewBox="0 0 21 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.2929 19.0312L20 18.3241L11.6915 10.0157L20 1.70713L19.2929 1.00002L10.9844 9.30855L2.67586 1L1.96875 1.70711L10.2773 10.0157L1.96882 18.3241L2.67593 19.0312L10.9844 10.7228L19.2929 19.0312Z"
        fill={fill}
      />
      <path
        d="M20 18.3241L20.7071 19.0312C21.0976 18.6407 21.0976 18.0075 20.7071 17.617L20 18.3241ZM19.2929 19.0312L18.5858 19.7383C18.7733 19.9259 19.0276 20.0312 19.2929 20.0312C19.5581 20.0312 19.8124 19.9259 20 19.7383L19.2929 19.0312ZM11.6915 10.0157L10.9844 9.30855C10.5939 9.69908 10.5939 10.3322 10.9844 10.7228L11.6915 10.0157ZM20 1.70713L20.7072 2.41423C20.8947 2.2267 21 1.97234 21 1.70713C21 1.44191 20.8947 1.18756 20.7072 1.00002L20 1.70713ZM19.2929 1.00002L20 0.292913C19.6095 -0.0976105 18.9764 -0.0976104 18.5858 0.292914L19.2929 1.00002ZM10.9844 9.30855L10.2773 10.0157C10.4648 10.2032 10.7192 10.3086 10.9844 10.3086C11.2496 10.3086 11.504 10.2032 11.6915 10.0157L10.9844 9.30855ZM2.67586 1L3.38296 0.292893C2.99244 -0.0976311 2.35927 -0.0976311 1.96875 0.292893L2.67586 1ZM1.96875 1.70711L1.26164 1C0.871119 1.39052 0.871119 2.02369 1.26164 2.41421L1.96875 1.70711ZM10.2773 10.0157L10.9844 10.7228C11.3749 10.3322 11.3749 9.69908 10.9844 9.30855L10.2773 10.0157ZM1.96882 18.3241L1.26172 17.617C0.871193 18.0076 0.871193 18.6407 1.26172 19.0312L1.96882 18.3241ZM2.67593 19.0312L1.96882 19.7383C2.15636 19.9259 2.41071 20.0312 2.67593 20.0312C2.94115 20.0312 3.1955 19.9259 3.38304 19.7383L2.67593 19.0312ZM10.9844 10.7228L11.6915 10.0157C11.504 9.82812 11.2496 9.72277 10.9844 9.72277C10.7192 9.72277 10.4648 9.82812 10.2773 10.0157L10.9844 10.7228ZM19.2929 17.617L18.5858 18.3241L20 19.7383L20.7071 19.0312L19.2929 17.617ZM10.9844 10.7228L19.2929 19.0312L20.7071 17.617L12.3986 9.30855L10.9844 10.7228ZM19.2929 1.00002L10.9844 9.30855L12.3986 10.7228L20.7072 2.41423L19.2929 1.00002ZM18.5858 1.70713L19.2929 2.41423L20.7072 1.00002L20 0.292913L18.5858 1.70713ZM11.6915 10.0157L20 1.70713L18.5858 0.292914L10.2773 8.60145L11.6915 10.0157ZM1.96875 1.70711L10.2773 10.0157L11.6915 8.60145L3.38296 0.292893L1.96875 1.70711ZM2.67586 2.41421L3.38296 1.70711L1.96875 0.292893L1.26164 1L2.67586 2.41421ZM10.9844 9.30855L2.67586 1L1.26164 2.41421L9.57019 10.7228L10.9844 9.30855ZM2.67593 19.0312L10.9844 10.7228L9.57019 9.30855L1.26172 17.617L2.67593 19.0312ZM3.38304 18.3241L2.67593 17.617L1.26172 19.0312L1.96882 19.7383L3.38304 18.3241ZM10.2773 10.0157L1.96882 18.3241L3.38304 19.7383L11.6915 11.4299L10.2773 10.0157ZM20 18.3241L11.6915 10.0157L10.2773 11.4299L18.5858 19.7383L20 18.3241Z"
        fill={fill}
      />
    </svg>
  );
}
