import clsx from "clsx";
import { DivProps } from "../types/html-elements-props";
import { createPortal } from "react-dom";
import { useClient } from "../hooks/use-client";

type BackDropProps = DivProps & {
  absolute?: boolean;
  usePortal?: boolean;
};

export default function BackDrop({
  absolute,
  className,
  usePortal,
  ...restProps
}: BackDropProps) {
  const isClient = useClient();

  const backdrop = (
    <div
      {...restProps}
      className={clsx(
        "animate-backdrop backdrop bg-black top-0 left-0 z-10 dark:bg-white",
        className,
        {
          "absolute size-full": absolute,
          "fixed h-screen w-screen": !absolute,
        }
      )}
    />
  );
  if (usePortal && isClient) {
    return createPortal(backdrop, document.body);
  }
  return backdrop;
}
