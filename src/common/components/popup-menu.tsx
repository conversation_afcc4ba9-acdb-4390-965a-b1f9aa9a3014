import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>actN<PERSON>, useRef } from "react";
import { Menu, MenuProps } from "primereact/menu";
import clsx from "clsx";

type PopupMenuProps = MenuProps & {
  children: ReactNode;
  containerClassName?: string;
};

export default function PopupMenu({
  children,
  containerClassName,
  pt,
  className,
  ...menuProps
}: PopupMenuProps) {
  const { action, menu, ...restPt } = pt ?? {};
  const menuRef = useRef<Menu>(null);

  const handleButtonClick: MouseEventHandler<HTMLDivElement> = (event) => {
    menuRef.current?.toggle(event);
  };

  return (
    <div className={containerClassName}>
      <div
        onClick={handleButtonClick}
        aria-controls="popup_menu"
        aria-haspopup
        className="cursor-pointer"
      >
        {children}
      </div>
      <Menu
        {...menuProps}
        popup
        ref={menuRef}
        id="popup_menu"
        className={clsx(
          "bg-white rounded-[0.813rem] shadow-[0_4px_22px_0_rgba(0,0,0,0.10)] dark:shadow-md-light",
          "dark:bg-gray-800 font-normal tracking-wide w-[16.125rem] md:w-[15.75rem]",
          className
        )}
        pt={{
          // @ts-ignore
          action: { className: clsx("md:py-2", action?.className) },
          // @ts-ignore
          menu: {
            className: clsx(
              "md:py-1.5 divide-y divide-neutral-250 md:divide-none",
              // @ts-ignore
              menu?.className
            ),
          },
          ...restPt,
        }}
      />
    </div>
  );
}
