import { Menu } from "primereact/menu";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { useCurrentRoute } from "@/common/hooks/use-current-route";
import { useClient } from "@/common/hooks/use-client";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { ReactNode } from "react";
import { useRouter } from "next/navigation";

export interface SubMenuItem {
  label: string;
  route: string;
  icon?: ReactNode;
}

interface SubMenuProps {
  items: SubMenuItem[];
}

export const SubMenu = ({ items }: SubMenuProps) => {
  const isClient = useClient();
  const router = useRouter();
  const currentRoute = useCurrentRoute();
  const { isMobile } = useViewportSize();

  if (!isClient) return null;

  const menuPt = isMobile
    ? {
        root: {
          className: "w-full p-5",
        },
        menu: {
          className: "space-y-4",
        },
        content: {
          className: "rounded-md",
        },
        action: {
          className: "justify-between",
        },
      }
    : undefined;

  const defaultIcon = isMobile ? (
    <FontAwesomeIcon icon="chevron-right" className="order-1" />
  ) : undefined;

  const menuItems = items.map((item) => ({
    label: item.label,
    command: () => router.push(item.route),
    expanded: currentRoute === item.route || isMobile,
    icon: item.icon ?? defaultIcon,
  }));

  return <Menu model={menuItems} pt={menuPt} />;
};
