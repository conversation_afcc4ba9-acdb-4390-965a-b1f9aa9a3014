import { SVGProps } from "react";

export default function Logo(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="91"
      height="29"
      viewBox="0 0 91 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M63.4629 10.8507H56.0089V20.1917C56.0089 22.5977 57.1412 23.3525 59.9718 23.3525C60.9625 23.3525 62.425 23.211 63.227 23.0223V28.4476C62.6137 28.5892 60.6323 28.9666 58.698 28.9666C51.4328 28.9666 48.8381 25.7585 48.8381 20.5691V10.8507H37.9402V20.1917C37.9402 22.5977 39.0725 23.3525 41.9031 23.3525C42.8938 23.3525 44.3563 23.211 45.1583 23.0223V28.4476C44.545 28.5892 42.5636 28.9666 40.6293 28.9666C33.3641 28.9666 30.7693 25.7585 30.7693 20.5691V10.8507H27.3254V5.2838H30.1089C31.1939 5.2838 31.5714 4.6705 31.7129 3.2552L32.0431 0H37.9402V5.2838H48.1776C49.2627 5.2838 49.6401 4.6705 49.7816 3.2552L50.1118 0H56.0089V5.2838H63.4629V10.8507ZM78.31 28.9667C70.6201 28.9667 65.6194 24.5321 65.6194 16.9838C65.6194 9.38829 70.6201 4.90649 78.31 4.90649C85.9998 4.90649 91.0005 9.38829 91.0005 16.9838C91.0005 24.5321 85.9998 28.9667 78.31 28.9667ZM78.31 23.4942C81.801 23.4942 83.8296 21.0881 83.8296 16.9838C83.8296 12.8322 81.801 10.379 78.31 10.379C74.8189 10.379 72.7903 12.8322 72.7903 16.9838C72.7903 21.0881 74.8189 23.4942 78.31 23.4942ZM0 16.9838C0 24.5321 5.00074 28.9667 12.6906 28.9667C20.3804 28.9667 25.3811 24.5321 25.3811 16.9838C25.3811 9.38829 20.3804 4.90649 12.6906 4.90649C5.00074 4.90649 0 9.38829 0 16.9838ZM18.2103 16.9838C18.2103 21.0881 16.1817 23.4942 12.6906 23.4942C9.19948 23.4942 7.17088 21.0881 7.17088 16.9838C7.17088 12.8322 9.19948 10.379 12.6906 10.379C16.1817 10.379 18.2103 12.8322 18.2103 16.9838Z"
        fill="url(#paint0_linear_3342_5900)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_3342_5900"
          x1="-2"
          y1="1.5"
          x2="75"
          y2="20.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3DDFD6" />
          <stop offset="1" stopColor="#36B5F3" />
        </linearGradient>
      </defs>
    </svg>
  );
}
