import clsx from "clsx";
import { DivProps } from "@/common/types/html-elements-props";

type DividerProps = DivProps & {
  layout?: "horizontal" | "vertical";
  lineClassName?: string;
};

export default function Divider({
  layout = "horizontal",
  children,
  className,
  lineClassName,
  ...props
}: DividerProps) {
  return (
    <div
      className={clsx(
        "flex items-center",
        {
          "w-full": layout === "horizontal",
          "min-h-full flex-col": layout === "vertical",
        },
        className
      )}
      {...props}
    >
      {/* First line */}
      <div
        className={clsx(
          "flex-1 border-solid",
          {
            "h-px border-t": layout === "horizontal",
            "w-px border-l": layout === "vertical",
          },
          lineClassName
        )}
      />

      {/* Content */}
      {children && (
        <div
          className={clsx(
            "px-2.5 whitespace-nowrap justify-center flex items-center",
            {
              "flex-col": layout === "vertical",
            }
          )}
        >
          {children}
        </div>
      )}

      {/* Second line */}
      <div
        className={clsx(
          "flex-1 border-solid",
          {
            "h-px border-t": layout === "horizontal",
            "w-px border-l": layout === "vertical",
          },
          lineClassName
        )}
      />
    </div>
  );
}
