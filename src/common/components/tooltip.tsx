import { Tooltip as ReactTooltip } from "react-tooltip";
import { ComponentProps } from "react";
import clsx from "clsx";

export function Tooltip(props: ComponentProps<typeof ReactTooltip>) {
  return (
    <ReactTooltip
      {...props}
      opacity={1}
      className={clsx(
        "!max-w-86 md:!max-w-[29rem] dark:border dark:border-neutral-600",
        props.className
      )}
      classNameArrow="dark:bg-gray-800 dark:border-b dark:border-r dark:border-neutral-600"
    />
  );
}
