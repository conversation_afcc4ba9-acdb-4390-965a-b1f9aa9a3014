import Link from "next/link";
import { useAtomValue } from "jotai";
import { usePathname } from "next/navigation";
import clsx from "clsx";
import { But<PERSON> } from "primereact/button";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { TutorialSteps } from "@/features/tutorial/types/tutorial-types";
import { ROUTES } from "@/common/constants";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { useCreateTrip, useTripsList } from "@/common/hooks/api";
import { useCompleteTutorial } from "@/features/tutorial/hooks/api";
import { useStoreToggle } from "@/common/hooks/toggles";
import { foreignTripsAtom } from "@/features/admin/store/trips";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import {
  bookedTripsExpandedAllAtom,
  bookedTripsExpandedAtom,
  planningTripsExpandedAllAtom,
  planningTripsExpanded<PERSON>tom,
  savedTripsExpanded<PERSON>ll<PERSON>tom,
  savedTripsExpanded<PERSON>tom,
  showSidebar<PERSON>tom,
} from "@/common/store/sidebar";
import { tutorialStepAtom } from "@/features/tutorial/store/tutorial-store";
import arrayHasElements from "@/common/utils/array-has-elements";
import LoadingSpinner from "@/common/components/loading-spinner";
import BackDrop from "@/common/components/backdrop";
import WithSidebar from "./with-sidebar";
import SidebarButton from "./sidebar-button";
import TripsList from "@/common/components/sidebar/trips-list";
import { memo, useCallback, useRef } from "react";
import { isDevelopment } from "@/common/utils";

export type SidebarProps = {
  className?: string;
  isOpen?: boolean;
  onClose?: VoidFunction;
};

function Sidebar(props: SidebarProps) {
  const { createTrip, isMutating } = useCreateTrip();
  const { completeTutorial } = useCompleteTutorial();
  const { data: trips, isLoading } = useTripsList();
  const foreignUser = useAtomValue(foreignUserAtom);
  const foreignTrips = useAtomValue(foreignTripsAtom);
  const tripsList = foreignUser ? foreignTrips : trips;
  const { turnOff: closeSidebar } = useStoreToggle(showSidebarAtom);
  const { closeRightSidebar } = useRightSidebarSwitch();
  const pathname = usePathname();
  const isItinerariesActive = pathname === ROUTES.ITINERARIES;

  const tutorialStep = useAtomValue(tutorialStepAtom);
  const isCreateTripStep = tutorialStep === TutorialSteps.CREATE_TRIP;
  const isTripsStep = tutorialStep === TutorialSteps.TRIPS;
  const showUserSettings =
    foreignUser?.has_company_travel_policy || foreignUser?.has_user_preferences;

  const createNewTrip = () => {
    closeRightSidebar();
    closeSidebar();
    createTrip();
  };

  const sidebarRef = useRef<HTMLDivElement>(null);

  /**
   * Needed to force a redraw after the sidebar is resized to avoid artefacts at the top of the scroll panel.
   */
  const triggerRedraw = useCallback(() => {
    if (sidebarRef?.current) {
      sidebarRef.current.classList.add("rounded-lg");
      setTimeout(
        () =>
          !!sidebarRef?.current &&
          sidebarRef.current.classList.remove("rounded-lg"),
        0
      );
    }
  }, []);

  const { toggle: togglePlanning, value: planningExpanded } = useStoreToggle(
    planningTripsExpandedAtom
  );
  const { toggle: togglePlanningAll, value: planningExpandedAll } =
    useStoreToggle(planningTripsExpandedAllAtom);

  const { toggle: toggleBooked, value: bookedExpanded } = useStoreToggle(
    bookedTripsExpandedAtom
  );
  const { toggle: toggleBookedAll, value: bookedExpandedAll } = useStoreToggle(
    bookedTripsExpandedAllAtom
  );

  const { toggle: toggleSaved, value: savedExpanded } = useStoreToggle(
    savedTripsExpandedAtom
  );
  const { toggle: toggleSavedAll, value: savedExpandedAll } = useStoreToggle(
    savedTripsExpandedAllAtom
  );

  return (
    <WithSidebar onClickLogo={createNewTrip} wrapperRef={sidebarRef} {...props}>
      {isLoading ? (
        <LoadingSpinner className="mx-auto" />
      ) : (
        <>
          {(!!isTripsStep || !!isCreateTripStep) && (
            <BackDrop
              className="z-10"
              onClick={() => isCreateTripStep && completeTutorial()}
            />
          )}
          {isDevelopment() && (
            <Link href={ROUTES.ONBOARDING}>
              <Button outlined>
                Old onboarding <small>dev only</small>
              </Button>
            </Link>
          )}
          <Button
            className={clsx(
              "box-border font-medium h-max justify-start px-0 py-2 text-base w-full ",
              {
                "bg-neutral-100 px-4 -mx-4 relative z-10 dark:bg-gray-900":
                  !!isCreateTripStep,
              }
            )}
            icon={
              <FontAwesomeIcon
                icon={["far", "message-plus"]}
                className="h-4 text-center w-4"
              />
            }
            data-tooltip-id={TutorialSteps.CREATE_TRIP}
            onClick={() => {
              if (isMutating) return;
              createNewTrip();
              if (!!tutorialStep && tutorialStep !== TutorialSteps.COMPLETE) {
                completeTutorial();
              }
            }}
            plain
          >
            Create new trip
            {isMutating && <LoadingSpinner className="h-auto ml-auto w-6" />}
          </Button>
          <Link
            className="flex font-medium gap-x-2 items-center mt-1.5 py-2"
            href={ROUTES.ITINERARIES}
            onClick={() => {
              if (!isItinerariesActive) {
                closeRightSidebar();
                closeSidebar();
              }
            }}
          >
            <FontAwesomeIcon
              icon={["far", "map"]}
              className="h-4 text-center w-4"
            />
            View your itineraries
          </Link>

          <div
            className={clsx({
              "tutorial-trips-highlighted": isTripsStep,
            })}
            data-tooltip-id={TutorialSteps.TRIPS}
          >
            {arrayHasElements(tripsList?.saved) && (
              <TripsList
                expanded={savedExpanded}
                expandedAll={savedExpandedAll}
                title="Saved"
                toggle={() => {
                  toggleSaved();
                  triggerRedraw();
                }}
                toggleAll={() => {
                  toggleSavedAll();
                  triggerRedraw();
                }}
                trips={tripsList.saved}
              />
            )}

            {arrayHasElements(tripsList?.planned) && (
              <TripsList
                expanded={planningExpanded}
                expandedAll={planningExpandedAll}
                title="Planning"
                toggle={() => {
                  togglePlanning();
                  triggerRedraw();
                }}
                toggleAll={() => {
                  togglePlanningAll();
                  triggerRedraw();
                }}
                trips={tripsList.planned}
              />
            )}

            {arrayHasElements(tripsList?.booked) && (
              <TripsList
                expanded={bookedExpanded}
                expandedAll={bookedExpandedAll}
                title="Booked"
                toggle={toggleBooked}
                toggleAll={toggleBookedAll}
                trips={tripsList.booked}
              />
            )}
          </div>

          {showUserSettings && (
            <>
              <div className="mt-6 text-neutral-500">User&apos;s setting</div>
              {foreignUser.has_user_preferences && (
                <SidebarButton href={ROUTES.USER_PREFERENCES}>
                  Travel Preferences
                </SidebarButton>
              )}
              {foreignUser.has_company_travel_policy && (
                <SidebarButton href={ROUTES.USER_TRAVEL_POLICY}>
                  Travel Policy
                </SidebarButton>
              )}
            </>
          )}
        </>
      )}
    </WithSidebar>
  );
}

export default memo(Sidebar);
