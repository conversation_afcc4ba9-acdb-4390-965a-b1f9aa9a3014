import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, use<PERSON>em<PERSON>, useRef } from "react";
import Link from "next/link";
import { useSet<PERSON>tom } from "jotai";
import dayjs from "dayjs";
import clsx from "clsx";
import { MenuItem } from "primereact/menuitem";
import { Trip as TripType } from "@/features/chat/types/trip";
import { ROUTES } from "@/common/constants";
import colors from "@/common/constants/colors";
import defaultTripName from "@/features/chat/constants/default-trip-name";
import { useStoreToggle } from "@/common/hooks/toggles";
import useTripDeleteConfirmation from "@/features/chat/hooks/use-trip-delete-confirmation";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { ottoReplyingAtom } from "@/features/chat/store/chat";
import { showSidebarAtom } from "@/common/store/sidebar";
import SplitButton from "@/common/components/split-button";
import IconWarn from "@/common/components/icons/exclamation-solid";
import { BookingStatuses } from "@/features/itineraries/types/itinerary";
import { Tag } from "primereact/tag";
import { SplitButton as SplitButtonPR } from "primereact/splitbutton";
import arrayHasElements from "@/common/utils/array-has-elements";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useWebSocketContext } from "@/features/chat/context/websocket-context";
import { useReset } from "@/features/chat/hooks/use-reset";

function formatTripDate(start: string, end?: string) {
  const startDate = dayjs.utc(start);
  const endDate = dayjs.utc(end);
  const formattedStart = startDate.isValid() && startDate.format("MMM D");
  const endDateFormatString = `${
    startDate.get("month") !== endDate.get("month") ? "MMM " : ""
  }D`;
  const formattedEnd = endDate.isValid() && endDate.format(endDateFormatString);

  if (!formattedStart && !formattedEnd) {
    return "";
  }

  return `${formattedStart}${formattedEnd ? ` - ${formattedEnd}` : ""}`;
}

type TripButtonProps = TripType & {
  active?: boolean;
  className?: string;
  currentTimestamp: string;
  onClick?: MouseEventHandler<HTMLElement>;
};

export default function TripButton({
  active = false,
  className,
  created_date,
  currentTimestamp,
  date_end,
  date_start,
  id,
  onClick,
  showWarn,
  title,
  itinerary,
  extra,
  change_trip_action,
  cancel_trip_action,
}: TripButtonProps) {
  const { turnOff: closeSidebar } = useStoreToggle(showSidebarAtom);
  const { closeRightSidebar } = useRightSidebarSwitch();
  const { resetChatHistory } = useReset();
  const setIsOttoReplying = useSetAtom(ottoReplyingAtom);
  const { sendMessageToTrip } = useWebSocketContext();

  const buttonRef = useRef<SplitButtonPR>(null);

  const tagClassNames = "leading-4 ml-auto px-1 rounded";

  useEffect(() => {
    const sidebarContent = document.querySelector(".sidebar-content");
    const element = buttonRef.current?.getElement();

    if (active && buttonRef?.current && sidebarContent && element) {
      const buttonBoundingClientRect = element.getBoundingClientRect();
      const buttonTop = buttonBoundingClientRect.top;
      const buttonBottom = buttonBoundingClientRect.bottom;

      const sidebarBoundingClientRect = sidebarContent.getBoundingClientRect();
      const sidebarTop = sidebarBoundingClientRect.top;
      const sidebarBottom = sidebarBoundingClientRect.bottom;

      if (buttonTop < sidebarTop || buttonBottom > sidebarBottom) {
        element.scrollIntoView({ block: "nearest" });
      }
    }
  }, [active]);

  const isTripCanceled = useMemo(() => {
    const { accommodations, flight } = itinerary ?? {};
    const isAllAccommodationsCanceled = accommodations?.length
      ? accommodations.every((acc) => acc.status === BookingStatuses.CANCELLED)
      : false;

    const isFlightCancelled =
      arrayHasElements(flight?.legs) &&
      flight.legs.every((flight) => flight?.cancelled === true);

    if (accommodations?.length && flight) {
      return isAllAccommodationsCanceled && isFlightCancelled;
    }
    if (accommodations?.length) {
      return isAllAccommodationsCanceled;
    }
    return isFlightCancelled;
  }, [itinerary]);

  const icon = showWarn ? (
    <IconWarn fill={active ? colors.white : colors.gray[950]} />
  ) : undefined;

  const date =
    title !== defaultTripName
      ? formatTripDate(date_start, date_end)
      : dayjs.utc(created_date).from(dayjs.utc(currentTimestamp));

  const { openDeleteDialog, DeleteDialog } = useTripDeleteConfirmation(
    id,
    title
  );

  const dangerItemTemplate = ({ icon, label }: MenuItem) => (
    <div className="py-2 px-4 flex items-center gap-4 text-red-550">
      {icon}
      {label}
    </div>
  );

  const model: MenuItem[] = [
    {
      icon: <FontAwesomeIcon icon={["far", "trash"]} />,
      label: "Delete trip",
      template: dangerItemTemplate,
      visible: !itinerary,
      command: openDeleteDialog,
    },
    {
      icon: <FontAwesomeIcon icon={["far", "pen"]} />,
      label: "Change trip",
      visible: !!itinerary && !!change_trip_action,
      command: () => sendMessageToTrip(id, change_trip_action ?? ""),
    },
    {
      icon: <FontAwesomeIcon icon={["far", "ban"]} />,
      label: "Cancel trip",
      template: dangerItemTemplate,
      className: "text-red-550",
      visible: !!itinerary && !!cancel_trip_action,
      command: () => sendMessageToTrip(id, cancel_trip_action ?? ""),
    },
  ];

  return (
    <>
      <SplitButton
        id={id.toString()}
        active={active}
        className={className}
        icon={icon}
        buttonClassName={clsx("border-box w-[calc(100%-2.75rem)]", {
          "text-gray-900 dark:text-white": !active,
        })}
        buttonTemplate={() => (
          <Link
            className="flex items-center gap-x-2 py-3 size-full"
            href={`${ROUTES.TRIPS}/${id}`}
          >
            <span className="font-medium inline-block overflow-hidden text-nowrap text-ellipsis">
              {title}
            </span>
            {!!date && (
              <span
                className={clsx("font-normal min-w-max max-w-max", {
                  "text-neutral-500": !active,
                })}
              >
                {date}
              </span>
            )}
            {isTripCanceled && (
              <Tag className={tagClassNames} severity="danger">
                Cancelled
              </Tag>
            )}
            {extra?.isSampleTrip && !isTripCanceled && !extra?.isDeleted && (
              <Tag className={tagClassNames} severity="info">
                Sample trip
              </Tag>
            )}
            {extra?.isDeleted && (
              <Tag className={tagClassNames} severity="danger">
                Deleted
              </Tag>
            )}
          </Link>
        )}
        model={model}
        onClick={(event) => {
          if (!active) {
            closeRightSidebar();
            closeSidebar();
            resetChatHistory();
            setIsOttoReplying(true);
            onClick?.(event);
          }
        }}
        buttonRef={active ? buttonRef : undefined}
      />
      {DeleteDialog}
    </>
  );
}
