import { PropsWithChildren } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import clsx from "clsx";
import { useStoreToggle } from "@/common/hooks/toggles";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { showSidebarAtom } from "@/common/store/sidebar";
import NumberPill from "@/common/components/icons/number-pill";

type SidebarButtonProps = PropsWithChildren<{
  className?: string;
  href: string;
  notificationsCount?: number;
}>;

export default function SidebarButton({
  children,
  className,
  href,
  notificationsCount,
}: SidebarButtonProps) {
  const pathname = usePathname();
  const isActive = pathname === href;

  const { turnOff: closeSidebar } = useStoreToggle(showSidebarAtom);
  const { closeRightSidebar } = useRightSidebarSwitch();

  return (
    <Link
      className={clsx(
        "flex items-center box-border justify-between mt-2 px-4 py-3 rounded-lg text-base w-full",
        className,
        { "btn-gradient text-white": isActive }
      )}
      href={href}
      onClick={() => {
        if (!isActive) {
          closeRightSidebar();
          closeSidebar();
        }
      }}
    >
      <span className="font-medium">{children}</span>
      {!!notificationsCount && (
        <NumberPill
          className={clsx({
            "bg-primary-400 text-white": !isActive,
            "bg-white text-primary-400": isActive,
          })}
          number={notificationsCount > 99 ? "99+" : notificationsCount}
        />
      )}
    </Link>
  );
}
