import { PropsWithChildren, useEffect, useRef } from "react";
import { ScrollPanel } from "primereact/scrollpanel";
import clsx from "clsx";
import WithBottomShadow from "@/common/components/with-bottom-shadow";
import BackDrop from "@/common/components/backdrop";
import IconClose from "@/common/components/icons/close";

type WithSidebarProps = PropsWithChildren<{
  backdropClassName?: string;
  className?: string;
  header?: string;
  isOpen?: boolean;
  onClose: VoidFunction;
}>;

export default function WithRightSidebar({
  children,
  className,
  header,
  isOpen,
  onClose,
}: WithSidebarProps) {
  const scrollPanelRef = useRef<ScrollPanel>(null);

  useEffect(() => {
    const barY = scrollPanelRef.current?.getYBar();
    const scrollPanel = scrollPanelRef.current?.getElement();
    if (barY && scrollPanel) {
      setTimeout(() => {
        if (isOpen) {
          barY.style.right = `-${scrollPanel.clientWidth - barY.clientWidth}px`;
          barY.classList.remove("opacity-0");
        } else {
          barY.classList.add("opacity-0");
        }
      }, 300);
    }
  }, [isOpen]);

  return (
    <>
      <div
        className={clsx(
          "absolute bg-white border-neutral-250 duration-300 h-full left-full top-0 max-w-[30.25rem] transition-width-transform z-20",
          "3xl:left-0 3xl:relative 3xl:shrink-0",
          "dark:bg-gray-800 dark:border-gray-900",
          isOpen
            ? "border-s -translate-x-full 3xl:translate-x-0 w-11/12"
            : "translate-x-0 w-0",
          className
        )}
      >
        <WithBottomShadow>
          <div className="flex h-15 items-center justify-between px-6 w-full">
            {header && (
              <div className="font-medium leading-6 text-xl">{header}</div>
            )}
            <IconClose
              className="cursor-pointer"
              width={18}
              height={18}
              onClick={onClose}
            />
          </div>
        </WithBottomShadow>
        <ScrollPanel
          pt={{
            barY: { className: "opacity-0 transition-opacity" },
            root: {
              className: "h-[calc(100dvh-3.75rem)]",
            },
          }}
          ref={scrollPanelRef}
        >
          <div className="p-4 lg:p-6">{children}</div>
        </ScrollPanel>
      </div>
      {isOpen && <BackDrop className="3xl:hidden" onClick={onClose} />}
    </>
  );
}
