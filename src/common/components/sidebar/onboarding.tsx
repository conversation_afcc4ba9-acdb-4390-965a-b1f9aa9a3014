import { useEffect, useRef } from "react";
import { useAtom } from "jotai";
import { useUserProfile } from "@/features/user/hooks/api";
import WithSidebar from "./with-sidebar";
import Checklist from "@/common/components/checklist";
import { SidebarProps } from "./main";
import {
  onboardingChecklistAtom,
  showSidebar<PERSON>tom,
} from "@/common/store/sidebar";
import {
  onboardingChecklistLabels,
  OnboardingChecklistItems,
} from "@/common/types/onboarding-checklist";
import { useStoreToggle } from "@/common/hooks/toggles";
import { useCreateTrip } from "@/common/hooks/api";

type OnboardingSidebarProps = SidebarProps;

export default function OnboardingSidebar({
  ...props
}: OnboardingSidebarProps) {
  const [onboardingChecklist, setOnboardingChecklist] = useAtom(
    onboardingChecklistAtom
  );
  const { turnOff: closeSidebar } = useStoreToggle(showSidebarAtom);
  const checklistItems = Object.entries(onboardingChecklist).map(([key]) => ({
    checked: onboardingChecklist[key as OnboardingChecklistItems],
    text: onboardingChecklistLabels[key as OnboardingChecklistItems],
  }));

  const { preferences, travelPolicy } = useUserProfile();
  const { createTrip } = useCreateTrip();

  useEffect(() => {
    const { travel_policy, travel_preferences } = onboardingChecklist;
    if (
      travel_policy !== !!travelPolicy ||
      travel_preferences !== !!preferences
    ) {
      setOnboardingChecklist({
        ...onboardingChecklist,
        [OnboardingChecklistItems.TRAVEL_POLICY]: !!travelPolicy,
        [OnboardingChecklistItems.TRAVEL_PREFERENCES]: !!preferences,
      });
    }
  }, [onboardingChecklist, preferences, setOnboardingChecklist, travelPolicy]);

  const createNewTrip = () => {
    closeSidebar();
    createTrip();
  };

  return (
    <WithSidebar onClickLogo={createNewTrip} {...props}>
      <Checklist items={checklistItems} />
    </WithSidebar>
  );
}
