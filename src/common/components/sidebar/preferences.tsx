import WithSidebar from "@/common/components/sidebar/with-right-sidebar";
import { useUserProfile } from "@/features/user/hooks/api";
import useDeletePreferencePopup from "@/common/hooks/use-delete-preference-popup";
import BackDrop from "@/common/components/backdrop";
import { PreferencesList } from "@/features/user/components/preferences-list";

type PreferencesSidebarProps = {
  isOpen?: boolean;
  onClose: VoidFunction;
};

export default function PreferencesSidebar(props: PreferencesSidebarProps) {
  const { preferences } = useUserProfile();
  const { close, isOpen, open, Popup } = useDeletePreferencePopup();

  return (
    <>
      <WithSidebar header="Saved preferences" {...props}>
        {preferences && (
          <PreferencesList
            preferences={preferences}
            canEdit
            onDeletePreference={(key, value) => open({ key, value })}
          />
        )}
      </WithSidebar>
      {isOpen && (
        <>
          {Popup}
          <BackDrop className="z-20" onClick={close} />
        </>
      )}
    </>
  );
}
