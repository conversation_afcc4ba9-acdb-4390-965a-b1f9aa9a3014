import { usePathname } from "next/navigation";
import { But<PERSON> } from "primereact/button";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import { Trip } from "@/features/chat/types/trip";
import { ROUTES } from "@/common/constants";
import useIntervalTimestamp from "@/features/chat/hooks/use-interval-timestamp";
import arrayHasElements from "@/common/utils/array-has-elements";
import TripButton from "@/common/components/sidebar/trip-button";

type TripsListProps = {
  toggle?: VoidFunction;
  toggleAll?: VoidFunction;
  expanded?: boolean;
  expandedAll?: boolean;
  title: string;
  trips: Trip[];
};

export default function TripsList({
  expanded,
  expandedAll,
  toggle,
  toggleAll,
  title,
  trips,
}: TripsListProps) {
  const pathname = usePathname();
  const currentTimestamp = useIntervalTimestamp();

  if (!arrayHasElements(trips)) {
    return null;
  }

  return (
    <>
      <Button
        className="mt-8 mb-3 p-0 relative text-neutral-500 text-base"
        onClick={toggle}
        plain
      >
        {title}
        <FontAwesomeIcon
          icon="angle-down"
          rotation={expanded ? 180 : undefined}
        />
      </Button>
      <div
        style={{
          maxHeight: expanded
            ? `${expandedAll ? trips.length * 3 + 3 : 20.25}rem`
            : "0px",
        }}
        className="transition-[max-height] overflow-hidden relative"
      >
        {trips.slice(0, 6).map((item, index) => (
          <TripButton
            currentTimestamp={currentTimestamp}
            {...item}
            active={pathname === `${ROUTES.TRIPS}/${item.id}`}
            key={index}
          />
        ))}
        {trips.length > 6 && (
          <>
            <div
              style={{
                maxHeight: expandedAll
                  ? `${trips.slice(6, undefined).length * 3}rem`
                  : "0px",
              }}
              className="transition-[max-height] overflow-hidden relative"
            >
              {trips.slice(6, undefined).map((item, index) => (
                <TripButton
                  currentTimestamp={currentTimestamp}
                  {...item}
                  active={pathname === `${ROUTES.TRIPS}/${item.id}`}
                  key={index}
                />
              ))}
            </div>
            <Button
              className="bottom-0 mt-3 px-4 py-0 relative text-neutral-500 text-base"
              onClick={toggleAll}
              plain
            >
              {expandedAll ? "Less" : "More"}
              <FontAwesomeIcon
                icon="angle-down"
                rotation={expandedAll ? 180 : undefined}
              />
            </Button>
          </>
        )}
      </div>
    </>
  );
}
