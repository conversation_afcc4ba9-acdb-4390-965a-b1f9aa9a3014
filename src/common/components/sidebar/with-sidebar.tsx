import { RefObject, useRef } from "react";
import { useAtomValue } from "jotai";
import clsx from "clsx";
import { ScrollPanel } from "primereact/scrollpanel";
import { DivProps } from "@/common/types/html-elements-props";
import { TutorialSteps } from "@/features/tutorial/types/tutorial-types";
import { accountMenuAtom } from "@/common/store/sidebar";
import { useStoreToggle } from "@/common/hooks/toggles";
import { tutorialStepAtom } from "@/features/tutorial/store/tutorial-store";
import WithBottomShadow from "@/common/components/with-bottom-shadow";
import AccountMenu from "@/features/user/components/account-menu";
import IconClose from "@/common/components/icons/close";
import BackDrop from "@/common/components/backdrop";
import { SidebarProps } from "./main";
import Logo from "../logo";

type WithSidebarProps = DivProps &
  SidebarProps & {
    onClickLogo: VoidFunction;
    wrapperRef?: RefObject<HTMLDivElement>;
  };

export default function WithSidebar({
  children,
  className,
  isOpen,
  onClose,
  onClickLogo,
  wrapperRef,
}: WithSidebarProps) {
  const { turnOff: closeAccountMenu } = useStoreToggle(accountMenuAtom);
  const closeHandler: VoidFunction = () => {
    closeAccountMenu();
    onClose?.();
  };

  const tutorialStep = useAtomValue(tutorialStepAtom);
  const isBackdropDisplayed =
    tutorialStep === TutorialSteps.TRIPS ||
    tutorialStep === TutorialSteps.CREATE_TRIP;

  return (
    <>
      <div
        className={clsx(
          "absolute bg-gray-100 border-e border-neutral-250 h-full left-0 max-w-86 transition-transform -translate-x-full w-11/12 z-20",
          "xl:basis-86 xl:max-w-86 xl:shrink-0 xl:static xl:translate-x-0",
          {
            "translate-x-0": isOpen,
          },
          "dark:bg-gray-900 dark:border-gray-900",
          className
        )}
        ref={wrapperRef}
      >
        <WithBottomShadow className="relative z-10">
          <div className="flex h-15 items-center justify-between px-6 w-full">
            <button data-testid="sidebar-logo" onClick={onClickLogo}>
              <Logo className="h-5 w-max" />
            </button>
            <IconClose
              className="cursor-pointer xl:hidden"
              onClick={closeHandler}
            />
          </div>
        </WithBottomShadow>
        <ScrollPanel
          pt={{
            content: { className: "sidebar-content" },
            root: {
              className: clsx("h-[calc(100dvh-9.25rem)]", {
                "z-20 [&_.custom-scrollbar]:opacity-0": isBackdropDisplayed,
              }),
            },
          }}
        >
          <div className="px-6 pt-6">{children}</div>
        </ScrollPanel>
        <AccountMenu />
      </div>

      {isOpen && (
        <BackDrop
          className="xl:hidden"
          onClick={() =>
            tutorialStep !== TutorialSteps.ACCOUNT_MENU && closeHandler()
          }
        />
      )}
    </>
  );
}
