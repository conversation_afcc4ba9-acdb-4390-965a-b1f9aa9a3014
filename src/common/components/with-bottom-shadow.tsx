import clsx from "clsx";
import { PropsWithChildren } from "react";

type WithBottomShadowProps = PropsWithChildren<{
  className?: string;
}>;

export default function WithBottomShadow({
  children,
  className,
}: WithBottomShadowProps) {
  return (
    <div className={clsx("overflow-x-clip shrink-0", className)}>
      <div
        className="border-b border-neutral-250 flex items-center shadow-[0_0_8px_0_rgba(0,166,141,.2)] h-full /
      w-full dark:border-gray-900 dark:shadow-[0_0_8px_rgba(155,255,230,.15)]"
      >
        {children}
      </div>
    </div>
  );
}
