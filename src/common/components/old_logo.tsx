import { SVGProps } from "react";

export default function OldLogo(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="160"
      height="160"
      viewBox="0 0 160 160"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M115.729 30.3478C97.7301 15.4797 71.9509 13.0475 51.8011 25.0804C47.0587 27.9156 43.1277 30.972 40.0081 34.2497C37.5975 36.7878 35.2672 39.8537 33.0173 43.4475C32.9417 43.5707 32.8361 43.6608 32.7006 43.7177C32.3792 43.8599 32.0373 43.9326 31.675 43.9358C25.8801 43.9737 19.5968 43.9753 12.825 43.9405C12.6796 43.9405 12.5401 43.8826 12.4373 43.7794C12.3345 43.6763 12.2767 43.5364 12.2767 43.3906C12.2799 43.1978 12.335 42.9955 12.4422 42.7837C14.4463 38.9387 16.4693 34.8471 18.9177 31.4382C23.4033 25.199 28.8484 19.742 35.0356 15.0009C41.0889 10.3609 47.774 6.82088 55.0909 4.38079C62.852 1.79215 71.256 0.0047411 79.6364 0C93.8195 0 107.029 3.70281 119.265 11.1084C129.47 17.2813 138.29 26.1519 144.269 36.7578C145.495 38.9292 146.695 41.1544 147.871 43.4332C147.899 43.4877 147.913 43.5486 147.911 43.6101C147.909 43.6716 147.892 43.7316 147.86 43.7842C147.828 43.8367 147.783 43.8801 147.729 43.91C147.676 43.9399 147.615 43.9553 147.554 43.9548L127.697 43.95C127.462 43.9499 127.231 43.8903 127.026 43.7768C126.82 43.6632 126.646 43.4994 126.52 43.3005C125.994 42.4755 125.416 41.5668 124.786 40.5744C122.331 36.7151 119.312 33.3062 115.729 30.3478Z"
        fill="#7B7B7B"
      />
      <path
        d="M47.7078 78.8968C47.6605 79.766 47.6259 80.632 47.6038 81.4949C47.5786 82.519 47.4857 83.3772 47.325 84.0694C46.9279 85.7635 46.3528 87.4324 45.5997 89.076C44.3267 91.8448 42.7306 94.3291 40.8116 96.529C39.9041 97.5688 39.0596 98.4317 38.2781 99.1176C36.2031 100.938 32.7905 102.327 30.3988 103.053C25.8517 104.194 21.5268 104.222 17.4241 103.138C14.1154 102.266 10.9533 100.374 7.93767 97.463C5.70669 95.3105 3.83178 92.4121 2.31295 88.7678C1.22896 86.1633 0.720055 83.1543 0.786228 79.7407C0.924877 72.4805 3.98461 66.3914 9.96541 61.4732C12.8613 59.0932 16.2204 57.595 20.0426 56.9787C33.1733 54.8499 45.4532 63.455 47.3391 76.787C47.4431 77.5045 47.566 78.2078 47.7078 78.8968ZM28.1445 93.8644C29.9187 93.344 31.573 92.4772 33.0131 91.3136C34.4532 90.1499 35.6507 88.7122 36.5374 87.0825C37.4241 85.4528 37.9826 83.6629 38.181 81.8152C38.3793 79.9675 38.2136 78.0981 37.6934 76.3137C36.6428 72.71 34.2119 69.6713 30.9353 67.8663C27.6588 66.0612 23.8051 65.6376 20.2219 66.6886C18.4477 67.209 16.7934 68.0757 15.3533 69.2393C13.9133 70.403 12.7157 71.8407 11.829 73.4704C10.9423 75.1002 10.3838 76.89 10.1855 78.7377C9.98713 80.5854 10.1528 82.4548 10.673 84.2392C11.7236 87.843 14.1546 90.8816 17.4311 92.6866C20.7076 94.4917 24.5614 94.9154 28.1445 93.8644Z"
        fill="#7B7B7B"
      />
      <path
        d="M89.2504 66.0435L70.0318 66.0008C69.8554 66.0008 69.7703 66.0877 69.7766 66.2616C69.8427 68.4425 69.6773 70.5191 69.6773 72.7C69.671 82.9503 69.7183 93.2337 69.8191 103.55C69.8191 103.74 69.7262 103.833 69.5402 103.83L60.9472 103.645C60.8473 103.643 60.7523 103.601 60.6825 103.528C60.6127 103.455 60.5737 103.358 60.5738 103.256L60.5785 66.2426C60.5785 66.1478 60.5328 66.1004 60.4414 66.1004L49.4472 65.9866C49.4025 65.986 49.3584 65.9767 49.3173 65.9592C49.2763 65.9417 49.2391 65.9163 49.208 65.8846C49.1768 65.853 49.1523 65.8155 49.1357 65.7744C49.1192 65.7333 49.111 65.6895 49.1116 65.6452L49.1305 57.1207C49.1305 56.9817 49.1998 56.9121 49.3385 56.9121L110.1 56.8458C110.298 56.8458 110.398 56.9453 110.398 57.1444L110.454 65.5125C110.457 65.9076 110.26 66.1036 109.863 66.1004L98.562 65.9487C98.5417 65.948 98.5215 65.9515 98.5026 65.9588C98.4837 65.9662 98.4665 65.9772 98.4519 65.9914C98.4374 66.0056 98.4258 66.0225 98.4179 66.0412C98.41 66.0599 98.406 66.0801 98.406 66.1004C98.0814 78.7307 98.0593 91.1445 98.3398 103.342C98.3411 103.403 98.3301 103.464 98.3077 103.521C98.2852 103.578 98.2516 103.629 98.2089 103.673C98.1662 103.717 98.1152 103.752 98.059 103.775C98.0027 103.799 97.9423 103.811 97.8813 103.811H89.6191C89.5564 103.811 89.4963 103.786 89.452 103.74C89.4077 103.695 89.3828 103.633 89.3828 103.569L89.4206 66.2189C89.4206 66.102 89.3639 66.0435 89.2504 66.0435Z"
        fill="#7B7B7B"
      />
      <path
        d="M146.256 101.567C143.503 102.932 140.507 103.733 137.438 103.927C134.368 104.12 131.286 103.701 128.368 102.693C125.449 101.685 122.751 100.109 120.428 98.0539C118.105 95.9988 116.201 93.5053 114.827 90.7156C112.051 85.0815 111.597 78.5834 113.565 72.6507C115.533 66.7179 119.763 61.8366 125.322 59.0805C128.075 57.7158 131.072 56.914 134.141 56.7207C137.21 56.5275 140.292 56.9467 143.21 57.9543C146.129 58.962 148.827 60.5383 151.15 62.5934C153.474 64.6485 155.377 67.1421 156.752 69.9318C159.528 75.5658 159.981 82.064 158.013 87.9967C156.045 93.9294 151.816 98.8108 146.256 101.567ZM141.05 93.3902C151.964 88.9857 153.037 74.2977 143.012 68.1722C134.943 63.2462 122.413 67.9873 121.888 78.2613C121.573 84.3963 123.618 88.973 128.023 91.9915C131.795 94.5754 136.805 95.1017 141.05 93.3902Z"
        fill="#7B7B7B"
      />
      <path
        d="M33.9674 116.731C44.6638 134.462 64.3078 143.722 84.4245 142.072C89.7562 141.632 94.8389 140.533 99.6727 138.772C110.771 134.728 119.662 126.796 125.925 116.873C125.995 116.76 126.094 116.666 126.211 116.601C126.329 116.537 126.461 116.503 126.596 116.503H147.317C147.36 116.503 147.402 116.514 147.439 116.534C147.476 116.554 147.508 116.584 147.53 116.62C147.553 116.656 147.566 116.697 147.569 116.74C147.571 116.783 147.563 116.825 147.544 116.863C146.717 118.613 145.876 120.329 144.831 121.974C143.295 124.378 141.868 126.872 140.048 129.057C138.97 130.352 138.1 131.836 137.037 133.121C128.45 143.494 117.749 150.96 104.933 155.518C97.9806 157.992 89.8602 159.799 82.1368 159.979C66.9264 160.33 52.8205 156.281 39.819 147.832C29.6094 141.198 21.5394 132.733 15.609 122.439C14.5597 120.618 13.4915 118.689 12.4044 116.65C12.3969 116.635 12.3931 116.619 12.3935 116.603C12.3939 116.586 12.3984 116.57 12.4065 116.556C12.4146 116.542 12.4261 116.531 12.4398 116.523C12.4535 116.516 12.469 116.512 12.4847 116.513L33.5042 116.465C33.5977 116.465 33.6896 116.49 33.7708 116.536C33.8521 116.583 33.9198 116.65 33.9674 116.731Z"
        fill="#7B7B7B"
      />
      <path
        d="M115.72 30.3478C97.7257 15.4797 71.9529 13.0475 51.808 25.0804C47.0668 27.9156 43.1368 30.9721 40.0179 34.2497C37.6079 36.7878 35.2783 39.8537 33.0289 43.4475C32.9533 43.5707 32.8478 43.6608 32.7123 43.7177C32.391 43.86 32.0492 43.9327 31.6869 43.9358C25.8934 43.9737 19.6116 43.9753 12.8416 43.9406C12.6962 43.9406 12.5568 43.8826 12.454 43.7795C12.3512 43.6763 12.2934 43.5364 12.2934 43.3906C12.2966 43.1978 12.3517 42.9955 12.4588 42.7837C14.4624 38.9387 16.4849 34.8471 18.9328 31.4383C23.4173 25.199 28.861 19.742 35.0467 15.0009C41.0985 10.3609 47.7819 6.82088 55.097 4.38079C62.8563 1.79215 71.2582 0.0047411 79.6365 0C93.8162 0 107.022 3.70281 119.255 11.1084C129.457 17.2813 138.275 26.1519 144.253 36.7578C145.479 38.9292 146.679 41.1544 147.854 43.4333C147.882 43.4877 147.897 43.5486 147.895 43.6101C147.893 43.6716 147.875 43.7316 147.843 43.7842C147.811 43.8367 147.766 43.8801 147.713 43.91C147.659 43.9399 147.599 43.9553 147.537 43.9548L127.685 43.95C127.451 43.9499 127.22 43.8903 127.014 43.7768C126.808 43.6632 126.634 43.4994 126.509 43.3005C125.983 42.4756 125.405 41.5668 124.774 40.5744C122.32 36.7151 119.302 33.3063 115.72 30.3478Z"
        fill="url(#paint0_radial_912_5549)"
      />
      <path
        d="M47.7158 78.8968C47.6685 79.766 47.6339 80.6321 47.6118 81.4949C47.5866 82.519 47.4937 83.3772 47.333 84.0694C46.9361 85.7635 46.3612 87.4324 45.6082 89.076C44.3355 91.8448 42.7398 94.3291 40.8213 96.529C39.914 97.5689 39.0697 98.4317 38.2884 99.1176C36.2139 100.938 32.8021 102.327 30.411 103.053C25.8651 104.194 21.5413 104.222 17.4395 103.138C14.1317 102.266 10.9703 100.374 7.95544 97.463C5.72501 95.3105 3.85056 92.4121 2.3321 88.7678C1.24839 86.1633 0.739601 83.1543 0.805758 79.7407C0.944373 72.4805 4.00335 66.3914 9.98269 61.4733C12.8778 59.0932 16.2361 57.595 20.0574 56.9787C33.1849 54.8499 45.4617 63.455 47.3472 76.787C47.4512 77.5045 47.574 78.2078 47.7158 78.8968ZM28.1573 93.8644C29.931 93.344 31.585 92.4772 33.0247 91.3136C34.4644 90.1499 35.6617 88.7122 36.5482 87.0825C37.4346 85.4528 37.993 83.6629 38.1913 81.8152C38.3896 79.9675 38.224 78.0981 37.7039 76.3137C36.6535 72.71 34.2232 69.6714 30.9474 67.8663C27.6717 66.0612 23.8189 65.6376 20.2367 66.6886C18.4629 67.209 16.809 68.0757 15.3693 69.2394C13.9296 70.403 12.7323 71.8407 11.8458 73.4705C10.9593 75.1002 10.401 76.89 10.2027 78.7377C10.0044 80.5854 10.17 82.4548 10.6901 84.2392C11.7404 87.843 14.1708 90.8816 17.4465 92.6867C20.7223 94.4917 24.575 94.9154 28.1573 93.8644Z"
        fill="url(#paint1_radial_912_5549)"
      />
      <path
        d="M89.2482 66.0435L70.0343 66.0009C69.8579 66.0009 69.7728 66.0878 69.7791 66.2616C69.8453 68.4425 69.6799 70.5191 69.6799 72.7C69.6736 82.9503 69.7208 93.2337 69.8216 103.55C69.8216 103.74 69.7287 103.833 69.5428 103.83L60.9519 103.645C60.852 103.643 60.757 103.601 60.6873 103.528C60.6175 103.455 60.5785 103.358 60.5786 103.256L60.5833 66.2426C60.5833 66.1478 60.5376 66.1004 60.4463 66.1004L49.4548 65.9866C49.4101 65.986 49.366 65.9767 49.3249 65.9592C49.2839 65.9417 49.2467 65.9164 49.2156 65.8847C49.1844 65.853 49.1599 65.8155 49.1433 65.7744C49.1268 65.7334 49.1186 65.6895 49.1192 65.6453L49.1381 57.1208C49.1381 56.9817 49.2075 56.9122 49.3461 56.9122L110.092 56.8458C110.291 56.8458 110.39 56.9453 110.39 57.1445L110.447 65.5125C110.45 65.9076 110.253 66.1036 109.856 66.1004L98.5574 65.9487C98.5372 65.9481 98.517 65.9515 98.4981 65.9589C98.4792 65.9662 98.462 65.9773 98.4474 65.9914C98.4329 66.0056 98.4213 66.0225 98.4134 66.0412C98.4055 66.06 98.4015 66.0801 98.4015 66.1004C98.077 78.7307 98.0549 91.1445 98.3353 103.342C98.3366 103.403 98.3257 103.464 98.3032 103.521C98.2807 103.578 98.2471 103.629 98.2045 103.673C98.1618 103.717 98.1108 103.752 98.0546 103.775C97.9983 103.799 97.938 103.811 97.877 103.811H89.6168C89.5541 103.811 89.494 103.786 89.4497 103.74C89.4054 103.695 89.3805 103.633 89.3805 103.569L89.4183 66.2189C89.4183 66.102 89.3616 66.0435 89.2482 66.0435Z"
        fill="url(#paint2_radial_912_5549)"
      />
      <path
        d="M146.24 101.567C143.488 102.932 140.492 103.733 137.423 103.927C134.355 104.12 131.274 103.701 128.356 102.693C125.438 101.685 122.741 100.109 120.418 98.054C118.095 95.9989 116.192 93.5053 114.818 90.7156C112.043 85.0816 111.589 78.5834 113.557 72.6507C115.525 66.718 119.753 61.8366 125.311 59.0805C128.063 57.7159 131.059 56.914 134.128 56.7208C137.196 56.5275 140.277 56.9467 143.195 57.9543C146.113 58.962 148.81 60.5383 151.133 62.5934C153.456 64.6486 155.358 67.1421 156.733 69.9318C159.508 75.5659 159.962 82.064 157.994 87.9967C156.026 93.9294 151.798 98.8108 146.24 101.567ZM141.035 93.3902C151.946 88.9857 153.019 74.2978 142.996 68.1723C134.93 63.2463 122.402 67.9874 121.878 78.2613C121.563 84.3963 123.607 88.9731 128.011 91.9916C131.782 94.5755 136.791 95.1017 141.035 93.3902Z"
        fill="url(#paint3_radial_912_5549)"
      />
      <path
        d="M33.9788 116.731C44.6726 134.462 64.3117 143.722 84.4235 142.072C89.7538 141.633 94.8353 140.533 99.6679 138.772C110.763 134.728 119.652 126.796 125.913 116.873C125.984 116.76 126.083 116.666 126.2 116.601C126.318 116.537 126.45 116.503 126.584 116.503H147.301C147.343 116.503 147.385 116.514 147.423 116.534C147.46 116.555 147.491 116.584 147.514 116.62C147.537 116.656 147.55 116.697 147.552 116.74C147.555 116.783 147.546 116.825 147.528 116.863C146.701 118.613 145.86 120.329 144.815 121.974C143.28 124.378 141.852 126.872 140.033 129.058C138.956 130.352 138.086 131.836 137.023 133.121C128.438 143.494 117.74 150.96 104.927 155.518C97.9762 157.992 89.8578 159.799 82.1363 159.979C66.9297 160.33 52.8272 156.281 39.8289 147.832C29.6218 141.198 21.5539 132.733 15.6249 122.439C14.5759 120.618 13.5079 118.689 12.421 116.65C12.4136 116.635 12.4098 116.619 12.4102 116.603C12.4106 116.586 12.415 116.57 12.4231 116.556C12.4313 116.542 12.4428 116.531 12.4565 116.523C12.4702 116.516 12.4857 116.512 12.5014 116.513L33.5157 116.465C33.6092 116.465 33.7011 116.49 33.7823 116.536C33.8635 116.583 33.9312 116.65 33.9788 116.731Z"
        fill="url(#paint4_radial_912_5549)"
      />
      <defs>
        <radialGradient
          id="paint0_radial_912_5549"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(8.80005 23.2) rotate(40.5419) scale(167.384 165.71)"
        >
          <stop stopColor="#28C9D0" />
          <stop offset="1" stopColor="#1384BA" />
        </radialGradient>
        <radialGradient
          id="paint1_radial_912_5549"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(8.80005 23.2) rotate(40.5419) scale(167.384 165.71)"
        >
          <stop stopColor="#28C9D0" />
          <stop offset="1" stopColor="#1384BA" />
        </radialGradient>
        <radialGradient
          id="paint2_radial_912_5549"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(8.80005 23.2) rotate(40.5419) scale(167.384 165.71)"
        >
          <stop stopColor="#28C9D0" />
          <stop offset="1" stopColor="#1384BA" />
        </radialGradient>
        <radialGradient
          id="paint3_radial_912_5549"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(8.80005 23.2) rotate(40.5419) scale(167.384 165.71)"
        >
          <stop stopColor="#28C9D0" />
          <stop offset="1" stopColor="#1384BA" />
        </radialGradient>
        <radialGradient
          id="paint4_radial_912_5549"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(8.80005 23.2) rotate(40.5419) scale(167.384 165.71)"
        >
          <stop stopColor="#28C9D0" />
          <stop offset="1" stopColor="#1384BA" />
        </radialGradient>
      </defs>
    </svg>
  );
}
