"use client";

import { ReactNode, useState } from "react";
import { Card } from "primereact/card";
import clsx from "clsx";
import { DivProps } from "@/common/types/html-elements-props";
import IconDots from "@/common/components/icons/dots";

type AccordionPTOptions = {
  content?: {
    className?: string;
  };
  header?: {
    className?: string;
    render: ReactNode;
  };
  wrapper?: {
    className?: string;
  };
};

type AccordionProps = DivProps & {
  contentClassName?: string;
  contentOutside?: boolean;
  isOpen?: boolean;
  onToggle?: () => void;
  pt?: AccordionPTOptions;
};

export default function Accordion({
  children,
  className,
  contentOutside = false,
  isOpen: isOpenProp,
  onToggle,
  pt,
}: AccordionProps) {
  const [isOpenState, setIsOpenState] = useState<boolean>(!!isOpenProp);

  const { content, header, wrapper } = pt ?? {};
  const { className: contentPTClassName } = content ?? {};
  const { className: headerPTClassName, render: headerPTRender } = header ?? {};
  const { className: wrapperPTClassName } = wrapper ?? {};
  const isOpen = typeof isOpenProp !== "undefined" ? isOpenProp : isOpenState;

  const headerClassName = clsx(
    headerPTClassName,
    "cursor-pointer flex font-medium gap-x-3 items-center",
    {
      "border-b border-neutral-250 pb-2 dark:border-gray-900":
        isOpen && !contentOutside,
    }
  );

  const renderedContent = isOpen && (
    <div className={clsx("w-full pt-2", contentPTClassName)}>{children}</div>
  );

  const closeHandler = () =>
    typeof onToggle === "function" ? onToggle() : setIsOpenState(!isOpen);

  return (
    <div className={wrapperPTClassName}>
      <Card
        className={clsx(
          "border-0 px-4 py-3 shadow-md-12% dark:border dark:border-gray-900",
          className
        )}
      >
        <div className={headerClassName} onClick={closeHandler}>
          {headerPTRender}
          <IconDots className="ml-auto dark:fill-white shrink-0" />
        </div>
        {!contentOutside && renderedContent}
      </Card>
      {contentOutside && renderedContent}
    </div>
  );
}
