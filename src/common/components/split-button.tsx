import { RefObject } from "react";
import { twMerge } from "tailwind-merge";
import clsx from "clsx";
import {
  SplitButton as SplitButtonPR,
  SplitButtonProps as SplitButtonPropsPR,
} from "primereact/splitbutton";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

type SplitButtonProps = SplitButtonPropsPR & {
  active?: boolean;
  buttonRef?: RefObject<SplitButtonPR>;
};

export default function SplitButton({
  active = false,
  buttonRef,
  model = [],
  className,
  ...props
}: SplitButtonProps) {
  const visibleItems = model?.filter(
    ({ visible }) => visible == null || visible === true
  );

  return (
    <SplitButtonPR
      {...props}
      dropdownIcon={
        <FontAwesomeIcon
          icon={["far", "ellipsis"]}
          className="text-neutral-400"
        />
      }
      menuClassName={clsx(
        "bg-white shadow-[0_4px_22px_0_rgba(0,0,0,0.25)] dark:bg-gray-800 dark:shadow-md-light",
        "w-[15.75rem] !left-[3.75rem] rounded-lg",
        {
          "rounded-[0.813rem]": visibleItems?.length > 1,
        }
      )}
      className={twMerge(
        clsx({
          "bg-gradient-radial bg-[length:100%_200%] from-primary-450 to-primary-400 dark:text-white":
            active,
        }),
        className
      )}
      model={model}
      ref={buttonRef}
    />
  );
}
