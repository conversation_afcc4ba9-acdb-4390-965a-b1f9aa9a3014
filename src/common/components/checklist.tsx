import { HTMLAttributes, ReactNode } from "react";
import clsx from "clsx";
import IconCheckSolid from "@/common/components/icons/check-solid";
import arrayHasElements from "@/common/utils/array-has-elements";
import { twMerge } from "tailwind-merge";

type ChecklistPTOptions = {
  icon?: ReactNode;
  iconWrapper?: {
    className?: string;
  };
  li?: {
    className?: string;
  };
};

type ChecklistProps = HTMLAttributes<HTMLUListElement> & {
  items: {
    checked: boolean;
    text: string;
    icon?: ReactNode;
  }[];
  pt?: ChecklistPTOptions;
};

const defaultCheckIcon = <IconCheckSolid width="16" height="16" />;

export default function Checklist({
  items,
  pt,
  className,
  ...ulAttributes
}: ChecklistProps) {
  const {
    icon = defaultCheckIcon,
    iconWrapper,
    li,
  } = pt ?? ({} as ChecklistPTOptions);

  if (!arrayHasElements(items)) {
    return null;
  }

  return (
    <ul className={clsx("space-y-2", className)} {...ulAttributes}>
      {items
        .filter((item) => item.text && item.text.trim() !== "")
        .map((item, index) => (
          <li
            className={twMerge(
              "flex items-center gap-x-2 relative",
              li?.className
            )}
            key={index}
          >
            <div
              className={clsx(
                "flex items-center justify-center w-4 h-5.5 self-start shrink-0",
                {
                  "text-primary-500": item.checked,
                  "text-neutral-375": !item.checked,
                },
                iconWrapper?.className
              )}
            >
              {item.icon ?? (item.checked && icon)}
            </div>
            {item.text}
          </li>
        ))}
    </ul>
  );
}
