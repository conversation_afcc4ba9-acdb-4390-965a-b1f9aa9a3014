import { RefObject } from "react";
import clsx from "clsx";
import { Button } from "primereact/button";
import { Panel, PanelProps } from "primereact/panel";
import BackDrop from "@/common/components/backdrop";
import IconClose from "@/common/components/icons/close";
import { twMerge } from "tailwind-merge";
import { useViewportSize } from "../hooks/use-viewport-size";
import { SettingsHeader } from "./settings-header";
import { useClient } from "../hooks/use-client";

type PopupProps = PanelProps & {
  onClose?: VoidFunction;
  onBack?: VoidFunction;
  panelRef?: RefObject<Panel>;
  wrapperClassName?: string;
};

export default function Popup({
  children,
  className,
  onClose,
  onBack,
  panelRef,
  wrapperClassName,
  ...props
}: PopupProps) {
  const { isMobile, isDesktop } = useViewportSize();
  const isClient = useClient();

  if (!children || !isClient) {
    return null;
  }

  return (
    <div className={twMerge("fixed size-full z-50", wrapperClassName)}>
      <Panel
        {...props}
        className={clsx(
          "fixed z-30",
          {
            "w-screen h-screen left-0 top-0 rounded-none": isMobile,
            "left-1/2 top-10 -translate-x-1/2": isDesktop,
          },
          className
        )}
        header={
          isDesktop ? (
            <div className="flex items-center w-full">
              {props.header}
              {onClose && (
                <Button className="ml-auto p-0" onClick={onClose} plain>
                  <IconClose width={16} height={16} />
                </Button>
              )}
            </div>
          ) : undefined
        }
        pt={{
          toggleableContent: {
            className: "h-full",
          },
          content: {
            className: "h-full",
          },
          header: {
            className: "mb-0 pb-1 lg:pb-2",
          },
        }}
        ref={panelRef}
      >
        {isMobile && (
          <SettingsHeader
            onClose={onClose}
            title={props.header as string}
            onBack={onBack}
          />
        )}
        {children}
      </Panel>
      <BackDrop className="z-20" onClick={onClose} />
    </div>
  );
}
