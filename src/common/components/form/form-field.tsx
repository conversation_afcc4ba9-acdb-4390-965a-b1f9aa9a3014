"use client";

import { InputText } from "primereact/inputtext";
import { forwardRef, InputHTMLAttributes } from "react";
import { FieldError } from "react-hook-form";
import { FormFieldWrapper, commonInputStyles } from "./form-field-wrapper";
import clsx from "clsx";

interface FormFieldProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, "value"> {
  label: string;
  name: string;
  error?: FieldError;
  required?: boolean;
  className?: string;
  containerClassName?: string;
}

const FormField = forwardRef<HTMLInputElement, FormFieldProps>(
  (
    {
      className,
      containerClassName,
      error,
      label,
      name,
      required,
      type = "text",
      ...props
    },
    ref
  ) => {
    return (
      <FormFieldWrapper
        label={label}
        error={error}
        required={required}
        containerClassName={containerClassName}
        name={name}
      >
        <InputText
          ref={ref}
          name={name}
          className={clsx(commonInputStyles, "py-2 px-2.5", className)}
          invalid={!!error?.message}
          type={type}
          {...props}
        />
      </FormFieldWrapper>
    );
  }
);

FormField.displayName = "FormField";

export default FormField;
