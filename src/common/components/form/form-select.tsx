"use client";

import { Dropdown, DropdownProps } from "primereact/dropdown";
import { forwardRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Controller, Control } from "react-hook-form";
import { FormFieldWrapper, commonInputStyles } from "./form-field-wrapper";
import clsx from "clsx";

interface FormSelectProps extends Omit<DropdownProps, "value" | "onChange"> {
  label: string;
  name: string;
  error?: FieldError;
  required?: boolean;
  containerClassName?: string;
  control?: Control<any>;
}

const FormSelect = forwardRef<Dropdown, FormSelectProps>(
  (
    {
      className,
      containerClassName,
      error,
      label,
      name,
      required,
      control,
      ...props
    },
    ref
  ) => {
    const renderField = ({ field, fieldState }: any) => (
      <FormFieldWrapper
        label={label}
        error={error || fieldState.error}
        required={required}
        containerClassName={containerClassName}
        name={name}
      >
        <Dropdown
          ref={ref}
          name={field.name}
          value={field.value}
          onChange={field.onChange}
          onBlur={field.onBlur}
          className={clsx(
            commonInputStyles,
            {
              "border-red-500": error?.message || fieldState.error,
            },
            className
          )}
          pt={{ input: { className: "text-base leading-none font-normal" } }}
          optionLabel="text"
          optionValue="value"
          {...props}
        />
      </FormFieldWrapper>
    );

    if (control) {
      return (
        <Controller
          name={name}
          control={control}
          render={renderField}
          rules={{ required }}
        />
      );
    }

    return renderField({ field: {}, fieldState: {} });
  }
);

FormSelect.displayName = "FormSelect";

export default FormSelect;
