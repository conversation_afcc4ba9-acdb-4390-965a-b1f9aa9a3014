"use client";

import { InputMask, InputMaskProps } from "primereact/inputmask";
import { forwardRef } from "react";
import { FieldError } from "react-hook-form";
import { FormFieldWrapper, commonInputStyles } from "./form-field-wrapper";
import clsx from "clsx";

interface FormMaskFieldProps extends Omit<InputMaskProps, "value"> {
  label: string;
  name: string;
  error?: FieldError;
  required?: boolean;
  className?: string;
  containerClassName?: string;
}

const FormMaskField = forwardRef<InputMask, FormMaskFieldProps>(
  (
    { className, containerClassName, error, label, name, required, ...props },
    ref
  ) => {
    return (
      <FormFieldWrapper
        label={label}
        error={error}
        required={required}
        containerClassName={containerClassName}
        name={name}
      >
        <InputMask
          ref={ref}
          name={name}
          className={clsx(commonInputStyles, "py-2 px-2.5", className)}
          invalid={!!error}
          {...props}
        />
      </FormFieldWrapper>
    );
  }
);

FormMaskField.displayName = "FormMaskField";

export default FormMaskField;
