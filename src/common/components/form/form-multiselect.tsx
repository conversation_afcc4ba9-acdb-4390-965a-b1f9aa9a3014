"use client";

import { MultiSelect, MultiSelectProps } from "primereact/multiselect";
import { forwardRef } from "react";
import { FieldError, Controller, Control } from "react-hook-form";
import { FormFieldWrapper, commonInputStyles } from "./form-field-wrapper";
import clsx from "clsx";

interface FormMultiSelectProps
  extends Omit<MultiSelectProps, "value" | "onChange"> {
  label: string;
  name: string;
  error?: FieldError;
  required?: boolean;
  containerClassName?: string;
  control?: Control<any>;
}

const FormMultiSelect = forwardRef<MultiSelect, FormMultiSelectProps>(
  (
    {
      className,
      containerClassName,
      error,
      label,
      name,
      required,
      control,
      ...props
    },
    ref
  ) => {
    const renderField = ({ field, fieldState }: any) => (
      <FormFieldWrapper
        label={label}
        error={error || fieldState.error}
        required={required}
        containerClassName={containerClassName}
        name={name}
      >
        <MultiSelect
          ref={ref}
          name={field.name}
          value={field.value}
          onChange={field.onChange}
          onBlur={field.onBlur}
          className={clsx(
            commonInputStyles,
            {
              "border-red-500": error?.message || fieldState.error,
            },
            className
          )}
          pt={{ input: { className: "text-base leading-none font-normal" } }}
          {...props}
        />
      </FormFieldWrapper>
    );

    if (control) {
      return <Controller name={name} control={control} render={renderField} />;
    }

    return renderField({ field: {}, fieldState: {} });
  }
);

FormMultiSelect.displayName = "FormMultiSelect";

export default FormMultiSelect;
