import clsx from "clsx";
import { FieldError } from "react-hook-form";
import { ReactNode } from "react";

interface FormFieldWrapperProps {
  label: string;
  error?: FieldError;
  required?: boolean;
  containerClassName?: string;
  name: string;
  children: ReactNode;
}

export function FormFieldWrapper({
  label,
  error,
  required,
  containerClassName,
  name,
  children,
}: FormFieldWrapperProps) {
  const { message } = error ?? {};
  const hasError = !!message;

  return (
    <div className={clsx("block relative w-full", containerClassName)}>
      <label
        className={clsx("font-semibold leading-4 mb-1.5 text-xs", {
          "text-red-500": hasError,
        })}
        htmlFor={name}
      >
        {label}
        {!!required && <span className="text-red-500">&nbsp;*</span>}
      </label>
      {children}
      {hasError && (
        <p className="absolute bottom-0 first-letter:capitalize left-1 text-xs text-red-500 translate-y-full">
          {message}
        </p>
      )}
    </div>
  );
}

export const commonInputStyles = clsx(
  "w-full border border-neutral-250 h-10 rounded bg-transparent",
  "focus:outline-none dark:border-[#646464] dark:bg-gray-800 min-w-0",
  "text-base leading-none font-normal"
);
