"use client";

import { useEffect } from "react";
import IconClose from "./icons/close";
import { useToggle } from "../hooks/toggles";
import { <PERSON><PERSON> } from "primereact/button";
import { <PERSON><PERSON>and<PERSON> } from "primereact/utils";
import { isNative<PERSON>ridge<PERSON>tom } from "../store/nativebridge";
import { useAtomValue } from "jotai";
import {
  ANDROID_INTENT_URL,
  APP_STORE_URL,
  SMART_BANNER_TIME_KEY,
} from "../constants";
import dayjs from "dayjs";

export default function SmartAppBanner() {
  const isNativeBridge = useAtomValue(isNativeBridgeAtom);
  const { value: isVisible, turnOff, turnOn } = useToggle();

  const dismiss = () => {
    turnOff();
    localStorage.setItem(SMART_BANNER_TIME_KEY, dayjs().toISOString());
  };

  useEffect(() => {
    const storedSmartBannerTime = localStorage.getItem(SMART_BANNER_TIME_KEY);
    const isHiddenForADay =
      dayjs(storedSmartBannerTime).isValid() &&
      dayjs().diff(storedSmartBannerTime, "day") >= 1;

    if (storedSmartBannerTime && !isHiddenForADay) {
      return;
    }

    if (!isNativeBridge && (DomHandler.isIOS() || DomHandler.isAndroid())) {
      turnOn();
      localStorage.removeItem(SMART_BANNER_TIME_KEY);
    } else {
      turnOff();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isNativeBridge]);

  const openApp = () => {
    if (DomHandler.isIOS()) {
      document.location = APP_STORE_URL;
    } else if (DomHandler.isAndroid()) {
      document.location = ANDROID_INTENT_URL;
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div
      className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 flex /
    items-center justify-between shadow-lg z-50 gap-2 dark:bg-gray-800"
    >
      <div className="flex-1">
        <h3 className="text-lg font-semibold">Get our app</h3>
        <p className="text-sm text-gray-400">
          Continue with our mobile app for the best experience
        </p>
      </div>

      <div className="flex items-center gap-2">
        <Button onClick={openApp}>Open App</Button>

        <Button
          onClick={dismiss}
          className="p-1"
          aria-label="Close banner"
          rounded
          text
        >
          <IconClose width={14} height={14} />
        </Button>
      </div>
    </div>
  );
}
