import { PropsWith<PERSON>hildren, ReactNode, useEffect, useRef } from "react";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import clsx from "clsx";
import { TutorialSteps } from "@/features/tutorial/types/tutorial-types";
import { useStoreToggle } from "@/common/hooks/toggles";
import { showSidebarAtom } from "@/common/store/sidebar";
import { tutorialStepAtom } from "@/features/tutorial/store/tutorial-store";
import WithBottomShadow from "@/common/components/with-bottom-shadow";
import IconMenuToggle from "@/common/components/icons/menu-toggle";
import BackDrop from "./backdrop";
import { userSelectDebugAtom } from "@/features/chat/store/debugging";
import { useUserProfile } from "@/features/user/hooks/api";
import { UserRole } from "@/features/user/types/user";
import UserSelect from "@/features/admin/components/user-select";
import { chatHeader<PERSON>tom } from "@/features/chat/store/components";

type HeaderProps = PropsWithChildren<{
  className?: string;
  title?: ReactNode;
  onClearUserSelect?: VoidFunction;
  showUserSelect?: boolean;
  wrapperClassName?: string;
}>;

export default function Header({
  children,
  className,
  title,
  onClearUserSelect,
  showUserSelect,
  wrapperClassName,
}: HeaderProps) {
  const userSelectDebug = useAtomValue(userSelectDebugAtom);
  const tutorialStep = useAtomValue(tutorialStepAtom);
  const isSidebarStep = tutorialStep === TutorialSteps.SIDEBAR;
  const { toggle: toggleSidebar } = useStoreToggle(showSidebarAtom);
  const { role } = useUserProfile();

  const headerRef = useRef<HTMLDivElement>(null);
  const setChatHeaderRef = useSetAtom(chatHeaderAtom);

  useEffect(() => {
    setChatHeaderRef(headerRef);
  }, [setChatHeaderRef]);

  return (
    <WithBottomShadow className={wrapperClassName}>
      <div
        className={clsx(
          "bg-white flex font-medium gap-x-2 h-15 items-center leading-6 px-6 text-xl w-full",
          "xl:justify-start min-w-0",
          "dark:bg-gray-800",
          className
        )}
        ref={headerRef}
      >
        <>
          {!!isSidebarStep && <BackDrop className="z-20 xl:hidden" />}
          <div
            className={clsx(
              "basis-4.5 cursor-pointer grow-0 shrink-0 xl:hidden",
              {
                "tutorial-sidebar-toggle-highlighted": isSidebarStep,
              }
            )}
            data-tooltip-id={TutorialSteps.SIDEBAR}
            onClick={toggleSidebar}
          >
            <IconMenuToggle />
          </div>
          {title && (
            <h1
              className="flex gap-x-1 items-center min-w-0 flex-1 overflow-hidden /
            text-nowrap text-ellipsis"
            >
              <span className="truncate block w-full">{title}</span>
            </h1>
          )}
          {showUserSelect && role === UserRole.ADMIN && userSelectDebug && (
            <UserSelect onClear={onClearUserSelect} />
          )}
          {children}
        </>
      </div>
    </WithBottomShadow>
  );
}
