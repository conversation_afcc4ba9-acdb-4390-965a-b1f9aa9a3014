import { CSSProperties } from "react";
import colors from "@/common/constants/colors";

type LoadingSpinnerProps = {
  animation?: {
    duration?: number;
  };
  className?: string;
  fill?: string;
  fillActive?: string;
};

export default function LoadingSpinner({
  animation,
  className,
  fill = colors.neutral[300],
  fillActive = colors.neutral[600],
}: LoadingSpinnerProps) {
  const duration = animation?.duration ?? 1;
  const animationStyle = {
    animationDuration: `${duration}s`,
  };

  return (
    <svg
      className={className}
      height="9"
      viewBox="0 0 40 9"
      width="40"
      xmlns="http://www.w3.org/2000/svg"
      style={
        {
          "--fill": fill,
          "--fill-active": fillActive,
        } as CSSProperties
      }
    >
      <circle
        style={animationStyle}
        className="loading-dot"
        cx="4"
        cy="4.50024"
        fill={fillActive}
        r="4"
      />
      <circle
        className="loading-dot"
        style={
          {
            ...animationStyle,
            animationDelay: `${duration * 0.33}s`,
          } as CSSProperties
        }
        cx="20"
        cy="4.50024"
        fill={fill}
        r="4"
      />
      <circle
        className="loading-dot"
        style={
          {
            ...animationStyle,
            animationDelay: `${duration * 0.66}s`,
          } as CSSProperties
        }
        cx="36"
        cy="4.50024"
        fill={fill}
        r="4"
      />
    </svg>
  );
}
