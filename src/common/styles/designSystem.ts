import { PrimeReactPTOptions } from "primereact/api";
import buttonPTOptions from "./components/button";
import calendarPTOptions from "./components/calendar";
import cardPTOptions from "./components/card";
import chipPTOptions from "./components/chip";
import confirmPopupPTOptions from "./components/confirm-popup";
import { datatablePTOptions } from "./components/datatable";
import { dialogPTOptions } from "./components/dialog";
import dropdownPTOptions from "./components/dropdown";
import inputPTOptions from "./components/input";
import inputSwitchPTOptions from "./components/input-switch";
import inputTextAreaPTOptions from "./components/input-textarea";
import paginatorPTOptions from "./components/paginator";
import panelPTOptions from "./components/panel";
import scrollPanelPTOption from "./components/scroll-panel";
import selectButtonPTOptions from "./components/select-button";
import splitButtonPTOptions from "./components/split-button";
import tabPanelPTOptions from "./components/tab-panel";
import tabViewPTOptions from "./components/tab-view";
import { toastPTOptions } from "./components/toast";
import avatarPTOptions from "./components/avatar";
import { editorPTOptions } from "./components/editor";
import { inputOtpPTOptions } from "./components/inputotp";
import { tagPTOptions } from "./components/tag";
import { galleriaPTOptions } from "./components/galleria";
import { overlayPanelPTOptions } from "./components/overlaypanel";
import { menuPTOptions } from "./components/menu";
import { multiselectPTOptions } from "./components/multiselect";
import { tieredMenuPTOptions } from "./components/tiered-menu";
import { sidebarPTOptions } from "./components/sidebar";

const designSystem: PrimeReactPTOptions = {
  button: buttonPTOptions,
  calendar: calendarPTOptions,
  card: cardPTOptions,
  chip: chipPTOptions,
  confirmpopup: confirmPopupPTOptions,
  dropdown: dropdownPTOptions,
  inputswitch: inputSwitchPTOptions,
  inputtext: inputPTOptions,
  inputtextarea: inputTextAreaPTOptions,
  paginator: paginatorPTOptions,
  panel: panelPTOptions,
  scrollpanel: scrollPanelPTOption,
  selectbutton: selectButtonPTOptions,
  splitbutton: splitButtonPTOptions,
  tabview: tabViewPTOptions,
  tabpanel: tabPanelPTOptions,
  overlaypanel: overlayPanelPTOptions,
  toast: toastPTOptions,
  dialog: dialogPTOptions,
  datatable: datatablePTOptions,
  avatar: avatarPTOptions,
  editor: editorPTOptions,
  inputotp: inputOtpPTOptions,
  tag: tagPTOptions,
  galleria: galleriaPTOptions,
  menu: menuPTOptions,
  multiselect: multiselectPTOptions,
  tieredmenu: tieredMenuPTOptions,
  sidebar: sidebarPTOptions,
};

export default designSystem;
