import clsx from "clsx";
import { ButtonPassThroughOptions } from "primereact/button";

const buttonPTOptions: ButtonPassThroughOptions = {
  root(options) {
    return {
      className: clsx(
        "bg-gray-950 flex font-medium gap-x-2 items-center justify-center rounded-lg text-sm",
        "leading-4 text-white shrink-0 disabled:cursor-not-allowed",
        "dark:bg-white dark:font-normal dark:text-gray-900",
        {
          "text-red-700 dark:text-red-500":
            options?.props.severity === "danger",
        },
        // Disabled styles
        {
          "disabled:bg-neutral-100 disabled:border-neutral-100 disabled:text-neutral-400":
            options?.props.severity === null && !options?.props.link,
          "disabled:bg-gray-500": options?.props.severity === "secondary",
          "disabled:opacity-55 disabled:no-underline": options?.props.link,
        },
        {
          "bg-transparent border-0 font-normal h-max leading-4.5 p-0 text-blue-500 hover:underline dark:bg-transparent dark:text-blue-500":
            options?.props.link,
          "bg-transparent border-0 text-gray-900 py-1 font-normal dark:text-white dark:bg-transparent":
            options?.props.plain,
          "px-3 py-2.5": !options?.props.rounded,
          "rounded-full p-2.5": options?.props.rounded,
          "p-3.5": options?.props.size === "large",
        },
        // Outlined styles
        {
          "box-border h-9 border": options?.props.outlined,
          "bg-white border-neutral-600 text-gray-900 hover:bg-gray-100 dark:text-white dark:bg-gray-800 dark:hover:bg-white/10 dark:border-neutral-750":
            options?.props.severity === null && options?.props.outlined,
          "border-gray-400":
            options?.props.severity === "secondary" && options?.props.outlined,
        },
        {
          "bg-transparent dark:bg-transparent border-transparent":
            options?.props.text && !options?.props.plain,
          "text-gray-900 hover:bg-gray-100 dark:text-white dark:bg-gray-800":
            options?.props.text &&
            options?.props.severity === null &&
            !options?.props.plain,
        },
        options?.props.className
      ),
    };
  },
};

export default buttonPTOptions;
