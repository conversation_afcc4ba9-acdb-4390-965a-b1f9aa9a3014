import clsx from "clsx";
import { TabViewPassThroughOptions } from "primereact/tabview";
const tabViewPTOptions: TabViewPassThroughOptions = {
  panelContainer: (options) => ({
    className: clsx("mt-6", options?.props.panelContainerClassName),
  }),
  nav: {
    className: "flex items-center border-b border-neutral-250 dark:border-neutral-700",
  },
  inkbar: { className: "hidden" },
};

export default tabViewPTOptions;
