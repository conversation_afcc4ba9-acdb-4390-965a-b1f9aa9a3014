import clsx from "clsx";
import { GalleriaPassThroughOptions } from "primereact/galleria";

export const galleriaPTOptions: GalleriaPassThroughOptions = {
  root: {
    className: "flex flex-col h-full",
  },
  content: {
    className: "flex flex-col h-full",
  },
  itemWrapper: {
    className: "flex flex-col relative h-full",
  },
  itemContainer: {
    className: "relative flex h-full justify-between",
  },
  item: {
    className: "[&>img]:rounded-lg absolute inset-0",
  },
  thumbnailContainer: {
    className: "flex flex-row bg-black/90 p-4",
  },
  previousThumbnailButton: {
    className: clsx(
      "self-center flex shrink-0 justify-center items-center overflow-hidden relative",
      "m-2 bg-transparent text-white w-8 h-8 transition duration-200 ease-in-out rounded-full",
      "hover:bg-white/10 hover:text-white",
      "focus:outline-none focus:outline-offset-0 focus:shadow-[0_0_0_0.2rem_rgba(191,219,254,1)]"
    ),
  },
  thumbnailItemsContainer: {
    className: "overflow-hidden w-full",
  },
  thumbnailItems: {
    className: "flex",
  },
  thumbnailItem: {
    className: clsx(
      "overflow-auto flex items-center justify-center cursor-pointer opacity-50",
      "flex-1 grow-0 shrink-0 w-20",
      "hover:opacity-100 hover:transition-opacity hover:duration-300"
    ),
  },
  nextThumbnailButton: {
    className: clsx(
      "self-center flex shrink-0 justify-center items-center overflow-hidden relative",
      "m-2 bg-transparent text-white w-8 h-8 transition duration-200 ease-in-out rounded-full",
      "hover:bg-white/10 hover:text-white",
      "focus:outline-none focus:outline-offset-0 focus:shadow-[0_0_0_0.2rem_rgba(191,219,254,1)]"
    ),
  },
  indicators: {
    className: "flex items-center justify-center p-4",
  },
  indicator: {
    className: "mr-2",
  },
  mask: {
    className: clsx(
      "fixed top-0 left-0 w-full h-full",
      "flex items-center justify-center",
      "bg-black bg-opacity-90"
    ),
  },
  closeButton: {
    className: clsx(
      "absolute top-0 right-0 flex justify-center items-center overflow-hidden m-2",
      "text-white bg-transparent w-12 h-12 rounded-full transition duration-200 ease-in-out",
      "hover:text-white hover:bg-white/10",
      "focus:outline-none focus:outline-offset-0 focus:shadow-[0_0_0_0.2rem_rgba(191,219,254,1)]"
    ),
  },
  closeIcon: {
    className: "w-6 h-6",
  },
  previousItemButton: (options) => ({
    className: clsx(
      "inline-flex justify-center items-center overflow-hidden z-10 self-center",
      "bg-transparent text-white size-10.5 transition duration-200 ease-in-out rounded-md mx-2",
      "hover:bg-white/10 hover:text-white",
      { hidden: options?.props.value?.length === 1 }
    ),
  }),
  nextItemButton: (options) => ({
    className: clsx(
      "inline-flex justify-center items-center overflow-hidden z-10 self-center",
      "bg-transparent text-white size-10.5 transition duration-200 ease-in-out rounded-md mx-2",
      "hover:bg-white/10 hover:text-white",
      { hidden: options?.props.value?.length === 1 }
    ),
  }),
  previousItemIcon: {
    className: "size-8",
  },
  nextItemIcon: {
    className: "size-8",
  },
  caption: {
    className: "absolute bottom-0 left-0 w-full bg-black/50 text-white p-4",
  },
  transition: {
    enterFromClass: "opacity-0 scale-75",
    enterActiveClass: "transition-all duration-150 ease-in-out",
    leaveActiveClass: "transition-all duration-150 ease-in",
    leaveToClass: "opacity-0 scale-75",
    addEndListener: () => {},
  },
};
