import clsx from "clsx";
import { AvatarPassThroughOptions } from "primereact/avatar";

const avatarPTOptions: AvatarPassThroughOptions = {
  root: (options) => ({
    className: clsx(
      "flex items-center justify-center shrink-0",
      "bg-gray-300",
      {
        "rounded-lg": options?.props.shape === "square",
        "rounded-full *:rounded-full":
          options?.props.shape == null || options?.props.shape === "circle",
      },
      {
        "size-6":
          options?.props.size == null || options?.props.size === "normal",
        "size-10": options?.props.size === "large",
        "size-16": options?.props.size === "xlarge",
      },
      {
        "-ml-4 border-2 border-white": options?.state.isNestedInAvatarGroup,
      }
    ),
  }),
  image: { className: "h-full w-full", referrerPolicy: "no-referrer" },
};

export default avatarPTOptions;
