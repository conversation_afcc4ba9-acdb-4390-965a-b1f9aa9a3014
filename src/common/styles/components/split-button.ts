import { SplitButtonPassThroughOptions } from "primereact/splitbutton";

const splitButtonPTOptions: SplitButtonPassThroughOptions = {
  button: {
    root: {
      className: "!bg-transparent flex-1 justify-start p-0 text-base",
    },
  },
  menuButton: {
    label: {
      className: "hidden",
    },
    root: {
      className: "bg-transparent ml-auto p-0 dark:bg-transparent",
    },
  },
  root: {
    className: "flex items-center gap-x-3 px-4 relative rounded-lg",
  },
};

export default splitButtonPTOptions;
