import clsx from "clsx";
import { DataTablePassThroughOptions } from "primereact/datatable";

export const datatablePTOptions: DataTablePassThroughOptions = {
  root: (options) => ({
    className: clsx("relative", {
      "flex flex-col h-full":
        options?.props.scrollable && options?.props.scrollHeight === "flex",
    }),
  }),
  wrapper: (options) => ({
    className: clsx({
      relative: options?.props.scrollable,
      "flex flex-col grow h-full":
        options?.props.scrollable && options?.props.scrollHeight === "flex",
    }),
  }),
  header: (options) => ({
    className: clsx(
      "bg-slate-50 border-gray-300 font-bold p-4",
      options?.props.showGridlines
        ? "border-x border-t border-b-0"
        : "border-y border-x-0"
    ),
  }),
  table: { className: "w-full border-spacing-y-3 border-separate" },
  thead: (options) => ({
    className: clsx({
      "top-0 z-[1]": options?.context.scrollable,
    }),
  }),
  tbody: (options) => ({
    className: clsx({
      "sticky z-[1]": options?.props.frozenRow && options?.context.scrollable,
    }),
  }),
  column: {
    headerCell: (options) => ({
      className: clsx(
        "text-left font-semibold pb-3 pr-3 first:pl-3 border-b border-neutral-250",
        "transition duration-200",
        {
          "sticky z-[1]": options?.props.frozen, // Frozen Columns
          "border-x border-y": options?.context?.showGridlines,
          "overflow-hidden space-nowrap border-y relative bg-clip-padding":
            options?.context.resizable, // Resizable,
          "text-right": options?.props.align === "right",
          "cursor-pointer": options?.props.sortable,
          "bg-primary-300 text-primary-900": options?.context.sorted, // Sort
        },
        "dark:border-gray-900",
        options?.props.className
      ),
    }),
    headerContent: {
      className: "flex items-center",
    },
    headerTitle: {
      className: "uppercase text-sm leading-6",
    },
    bodyCell: (options) => ({
      className: clsx(
        "text-left first:rounded-l-lg last:rounded-r-lg bg-gray-100 py-3 pr-3 first:pl-3",
        {
          "sticky bg-inherit": options?.props?.frozen, // Frozen Columns
          "text-right": options?.props?.align === "right",
        },
        "dark:bg-gray-900"
      ),
    }),
    sortIcon: (options) => ({
      className: clsx("ml-2", {
        "text-primary-900": options?.context.sorted,
      }),
    }),
    sortBadge: {
      className: clsx(
        "flex items-center justify-center align-middle",
        "rounded-[50%] w-[1.143rem] leading-[1.143rem] ml-2",
        "bg-primary-300 text-primary-900"
      ),
    },
  },
};
