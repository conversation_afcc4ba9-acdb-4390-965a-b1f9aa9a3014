// @ts-nocheck

import clsx from "clsx";
import { PanelPassThroughOptions } from "primereact/panel";

const panelPTOptions: PanelPassThroughOptions = {
  header: (options) => ({
    className: clsx(
      "border-b mb-3 pb-1 dark:border-neutral-700 dark:text-white",
      options?.props.pt?.header?.className
    ),
  }),
  title: {
    className: "font-medium",
  },
  root: (options) => ({
    className: clsx(
      "bg-white border-1 border-neutral-300 p-6 rounded-lg shadow-md-12% dark:shadow-md-12%",
      "dark:bg-gray-800 dark:border-neutral-700",
      options?.props.className
    ),
  }),
};

export default panelPTOptions;
