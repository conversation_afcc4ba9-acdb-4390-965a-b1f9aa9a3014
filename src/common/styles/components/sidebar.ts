import clsx from "clsx";
import { SidebarPassThroughOptions } from "primereact/sidebar";

export const sidebarPTOptions: SidebarPassThroughOptions = {
  root: (options) => ({
    className: clsx(
      "flex flex-col pointer-events-auto relative transform translate-x-0 translate-z-0 relative",
      "transition-all bg-white text-gray-700 border-0 shadow-lg duration-300",
      {
        "!transition-none !transform-none !w-screen !h-screen !max-h-full !top-0 !left-0":
          options?.props.fullScreen,
        "h-full w-80":
          options?.props.position == "left" ||
          options?.props.position == "right",
        "h-fit w-full":
          options?.props.position == "top" ||
          options?.props.position == "bottom",
        "rounded-t-[0.813rem]": options?.props.position == "bottom",
        "animate-slide-down":
          !options?.props.visible && options?.props.position == "bottom",
        "translate-y-full animate-slide-up":
          options?.props.visible && options?.props.position == "bottom",
      },
      "dark:border dark:border-blue-900/40 dark:bg-gray-900 dark:text-white/80"
    ),
  }),
  header: (options) => ({
    className: clsx("flex items-center justify-end p-5", {
      hidden: !options?.props?.header,
    }),
  }),
  content: {
    className: "p-5 pt-0 h-full w-full grow overflow-y-auto",
  },
  mask: {
    className: clsx(
      "flex pointer-events-auto",
      "bg-neutral-500 bg-opacity-55 transition duration-200 z-20 transition-all"
    ),
  },
};
