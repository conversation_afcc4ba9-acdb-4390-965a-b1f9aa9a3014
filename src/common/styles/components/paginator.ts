import { PaginatorPassThroughOptions } from "primereact/paginator";

const paginatorPTOptions: PaginatorPassThroughOptions = {
  root: {
    className: "flex items-center justify-center",
  },
  RPPDropdown: {
    root: { className: "ml-4 h-9 w-20" },
    input: {
      className: "overflow-hidden text-nowrap text-ellipsis max-w-24",
    },
    item: {
      className: "outline-none",
    },
  },
  pages: { className: "flex mx-2" },
  pageButton: {
    className:
      "aria-[current=true]:bg-primary-300 flex h-10 items-center justify-center rounded-full w-10",
  },
  firstPageButton: {
    className: "p-2",
  },
  lastPageButton: {
    className: "p-2",
  },
  prevPageButton: {
    className: "p-2",
  },
  nextPageButton: {
    className: "p-2",
  },
  nextPageIcon: {
    className: "h-5 w-5",
  },
  prevPageIcon: {
    className: "h-5 w-5",
  },
  lastPageIcon: {
    className: "h-5 w-5",
  },
  firstPageIcon: {
    className: "h-5 w-5",
  },
};

export default paginatorPTOptions;
