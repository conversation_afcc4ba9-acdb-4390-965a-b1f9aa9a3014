import clsx from "clsx";
import {
  InputOtpPassThroughMethodOptions,
  InputOtpPassThroughOptions,
} from "primereact/inputotp";

export const inputOtpPTOptions: InputOtpPassThroughOptions = {
  root: { className: "flex items-center gap-2" },
  input: {
    root: (options: InputOtpPassThroughMethodOptions) => ({
      className: clsx(
        "box-border text-center min-w-10 w-10 h-11 p-3 text-slate-900 border border-gray-300",
        "rounded-lg transition-all duration-200",
        "focus:outline-0 focus:outline-offset-0",
        "dark:border-gray-900",
        {
          "opacity-60 select-none pointer-events-none cursor-default":
            options?.context.disabled,
          "border-red-500":
            options?.props.invalid && !options?.context.disabled,
          "border-red-500/50":
            options?.props.invalid && options?.context.disabled,
        }
      ),
    }),
  },
};
