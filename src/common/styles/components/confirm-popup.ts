import clsx from "clsx";
import { ConfirmPopupPassThroughOptions } from "primereact/confirmpopup";

const confirmPopupPTOptions: ConfirmPopupPassThroughOptions = {
  acceptButton: {
    root: {
      className: "dark:bg-transparent dark:border-white dark:text-white dark:!border",
    },
  },
  content: {
    className: "flex flex-col items-center gap-y-4",
  },
  message: { className: "w-full" },
  footer: {
    className: "flex gap-x-4 items-center justify-end mt-4",
  },
  rejectButton: {
    root: {
      className:
        "bg-transparent border border-neutral-600 text-gray-900 dark:bg-transparent dark:border-white dark:text-white",
    },
  },
  root: {
    className: clsx(
      "bg-white border border-gray-200 !fixed !left-1/2 max-w-120 p-6 rounded-lg shadow-md !top-1/2 -translate-x-1/2",
      "dark:bg-gray-800 dark:border-gray-900 dark:shadow-md-light",
      "-translate-y-1/2 w-[calc(100%-2rem)] z-20"
    ),
  },
};

export default confirmPopupPTOptions;
