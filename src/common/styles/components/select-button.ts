import clsx from "clsx";
import { SelectButtonPassThroughOptions } from "primereact/selectbutton";

const selectButtonPTOptions: SelectButtonPassThroughOptions = {
  button: {
    className:
      "aria-pressed:bg-primary-500 aria-pressed:text-white border border-neutral-600 [&:not(:first-child)]:border-l-0 [&:not(:last-child)]:border-r-0 first:rounded-l-lg last:rounded-r-lg font-normal px-2 py-1 text-base uppercase dark:border-neutral-700",
  },
  root(options) {
    const { props } = options ?? {};

    return {
      className: clsx("flex items-center overflow-hidden", props?.className),
    };
  },
};

export default selectButtonPTOptions;
