import clsx from "clsx";
import { TagPassThroughOptions } from "primereact/tag";
import { twMerge } from "tailwind-merge";

export const tagPTOptions: TagPassThroughOptions = {
  root: (options) => ({
    className: twMerge(
      clsx(
        "inline-flex items-center justify-center w-fit shrink-0 grow-0",
        "bg-primary-600 text-blue-600 text-2xs leading-5 px-2",
        {
          "bg-gray-500 ": options?.props.severity == "secondary",
          "bg-green-500 ": options?.props.severity == "success",
          "bg-sky-850 text-white": options?.props.severity == "info",
          "bg-orange-500 ": options?.props.severity == "warning",
          "bg-red-800 text-white": options?.props.severity == "danger",
        },
        {
          "rounded-md": !options?.props.rounded,
          "rounded-full": options?.props.rounded,
        }
      ),
      options?.props.className
    ),
  }),
  icon: {
    className: "mr-1 text-sm",
  },
};
