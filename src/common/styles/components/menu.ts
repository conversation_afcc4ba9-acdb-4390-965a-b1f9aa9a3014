import clsx from "clsx";
import { MenuPassThroughOptions } from "primereact/menu";

const TRANSITIONS = {
  overlay: {
    timeout: 150,
    classNames: {
      enter: "opacity-0 scale-95 -translate-y-2",
      enterActive:
        "opacity-100 !scale-100 !translate-y-0 transition-all duration-150 ease-out",
      exit: "opacity-100 scale-100 translate-y-0",
      exitActive:
        "!opacity-0 !scale-95 !-translate-y-2 transition-all duration-150 ease-in",
    },
  },
};

export const menuPTOptions: MenuPassThroughOptions = {
  root: (options) => ({
    className: clsx(
      "dark:text-white w-60 font-medium",
      options?.props.className
    ),
  }),
  menu: (options) => ({
    className: clsx("m-0 p-0 list-none outline-none", options?.props.className),
  }),
  content: (options) => ({
    className: clsx("transition-shadow duration-200 rounded-lg", {
      "bg-gray-100 text-black": options?.context.item.expanded,
    }),
  }),
  action: (options) => ({
    className: clsx(
      "py-3 px-4 select-none",
      "cursor-pointer flex items-center gap-4 no-underline overflow-hidden relative",
      // @ts-ignore
      options?.props?.pt?.action?.className
    ),
  }),
  transition: TRANSITIONS.overlay,
};
