import { CalendarPassThroughOptions } from "primereact/calendar";

const calendarPTOptions: CalendarPassThroughOptions = {
  container: { className: "mt-4" },
  day: {
    className:
      "cursor-pointer data-[p-other-month=true]:opacity-50 data-[p-other-month=true]:pointer-events-none data-[p-other-month=true]:cursor-not-allowed p-0.5 md:p-1 hover:[&>span]:bg-primary-300",
  },
  dayLabel: {
    className:
      "data-[p-highlight=true]:bg-primary-300 data-[p-highlight=true]:text-primary-500 data-[p-highlight=true]:font-bold flex items-center justify-center h-9 rounded-full w-9 md:h-10 md:w-10",
  },
  header: { className: "flex gap-x-4 items-center justify-between md:px-4" },
  panel: { className: "bg-white dark:bg-gray-800" },
  table: { className: "w-full" },
};

export default calendarPTOptions;
