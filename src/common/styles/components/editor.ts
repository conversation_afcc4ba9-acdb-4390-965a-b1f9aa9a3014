import clsx from "clsx";
import { EditorPassThroughOptions } from "primereact/editor";

export const editorPTOptions: EditorPassThroughOptions = {
  content: (options) => ({
    className: clsx(
      "w-full [&_.ql-editor]:outline-none overflow-y-auto *:!p-0 [&_*]:!text-base text-gray-900 !border-none",
      "[&_.ql-blank]:before:!left-0 [&_.ql-blank]:before:!content-[attr(data-placeholder)]",
      "[&_.ql-blank]:before:!not-italic [&_.ql-blank]:before:!text-neutral-500",
      "dark:text-white [&_.ql-editor]:focus-visible:outline-none [&_.ql-editor>p:last-child]:hidden",
      "[&_.ql-blank>p]:hidden",
      options?.props.className
    ),
  }),
};
