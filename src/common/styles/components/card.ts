import clsx from "clsx";
import { CardPassThroughOptions } from "primereact/card";

const cardPTOptions: CardPassThroughOptions = {
  root: (options) => ({
    className: clsx(
      "p-4 border rounded-lg border-gray-200 bg-white shadow-md",
      "dark:bg-gray-800 dark:border-gray-600",
      options?.props.className
    ),
  }),
  body: { className: "h-full" },
  content: { className: "h-full" },
};

export default cardPTOptions;
