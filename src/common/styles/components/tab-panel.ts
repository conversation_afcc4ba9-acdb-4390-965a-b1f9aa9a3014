import clsx from "clsx";
import { TabPanelPassThroughOptions } from "primereact/tabview";

const tabPanelPTOptions: TabPanelPassThroughOptions = {
  header: (options) => ({
    className: clsx("mr-0", {
      "cursor-default pointer-events-none select-none user-select-none opacity-60":
        options?.props?.disabled,
    }),
  }),
  headerAction: {
    className: clsx(
      "block box-content cursor-pointer flex font-medium h-8 lg:h-13 items-center justify-center px-4 lg:px-8 text-center text-neutral-500 text-nowrap",
      "aria-selected:border-b-2 aria-selected:border-neutral-900 aria-selected:font-semibold aria-selected:-mb-px aria-selected:text-gray-900",
      "dark:aria-selected:border-white dark:aria-selected:text-white"
    ),
  },
  content: (options) => ({
    className: clsx(options?.props?.contentClassName),
  }),
};

export default tabPanelPTOptions;
