// @ts-nocheck

import clsx from "clsx";
import { ChipPassThroughOptions } from "primereact/chip";

const chipPTOptions: ChipPassThroughOptions = {
  label: { className: "block font-semibold leading-5 text-sm" },
  root: ({ props, context }) => ({
    className: clsx(
      "inline-block bg-teal-100 px-4 py-1 rounded-full w-fit text-gray-900",
      props.className
    ),
  }),
};

export default chipPTOptions;
