import clsx from "clsx";
import { TieredMenuPassThroughOptions } from "primereact/tieredmenu";

export const tieredMenuPTOptions: TieredMenuPassThroughOptions = {
  root: (options) => ({
    className: clsx("dark:text-white", options?.props.className),
  }),
  menuitem: {
    className: "cursor-pointer",
  },
  action: {
    className: clsx(
      "py-2 px-4 select-none",
      "cursor-pointer flex items-center gap-4 no-underline overflow-hidden relative"
    ),
  },
};
