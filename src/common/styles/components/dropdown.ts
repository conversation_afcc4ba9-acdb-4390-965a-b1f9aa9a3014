import clsx from "clsx";
import { DropdownPassThroughOptions } from "primereact/dropdown";

const dropdownPTOptions: DropdownPassThroughOptions = {
  root: (options) => ({
    className: clsx(
      "border border-neutral-600 flex gap-x-1 justify-between rounded-lg px-3 py-2.5 text-gray-900",
      "hover:bg-gray-100 hover:cursor-pointer",
      "dark:text-white dark:hover:bg-white/10 dark:border-neutral-700",
      options?.props?.className
    ),
  }),
  panel: {
    className:
      "border border-gray-200 drop-shadow-md bg-white rounded-lg dark:bg-gray-800 dark:shadow-md-light dark:border-gray-900",
  },
  input: {
    className: "font-medium text-sm text-center leading-4 truncate",
  },
  item: {
    className: clsx(
      "flex font-medium gap-x-1 items-center leading-3.5 pl-1 pr-6 py-1 text-sm hover:bg-gray-100 hover:cursor-pointer",
      "focus:outline-none dark:hover:bg-white/10"
    ),
  },
  itemGroup: {
    className: "py-1 px-2 font-bold text-primary-900",
  },
  trigger: {
    className: "flex items-center justify-center",
  },
  wrapper: {
    className: "overflow-y-auto py-2 safari-dropdown-fix translate-y-2",
  },
  filterContainer: { className: "relative" },
  filterInput: {
    className: clsx(
      "p-2 pl-7 -mr-7 w-full rounded-t-lg focus:outline-none focus:outline-offset-0",
      "border-b border-gray-200 transition duration-200 appearance-none",
      "dark:bg-gray-800"
    ),
  },
  filterIcon: { className: "ml-2 -mt-2 absolute top-1/2" },
  emptyMessage: {
    className: "px-2",
  },
};

export default dropdownPTOptions;
