import clsx from "clsx";
import { OverlayPanelPassThroughOptions } from "primereact/overlaypanel";

const TRANSITIONS = {
  overlay: {
    classNames: {
      enter: "opacity-0 scale-75",
      enterActive:
        "opacity-100 !scale-100 transition-transform transition-opacity duration-150 ease-in",
      exit: "opacity-100",
      exitActive: "!opacity-0 transition-opacity duration-150 ease-linear",
    },
    addEndListener: () => {},
  },
};

export const overlayPanelPTOptions: OverlayPanelPassThroughOptions = {
  root: (options) => {
    return {
      className: clsx(
        "bg-white text-gray-700 border-0 rounded-md shadow-lg max-w-120 w-[80vw] sm:w-120",
        "z-40 transform origin-center",
        "absolute left-0 top-0 mt-3",
        "before:absolute before:w-0 before:-top-3 before:h-0 before:border-transparent before:border-solid",
        "before:ml-6 before:border-x-[0.75rem] before:border-b-[0.75rem] before:border-t-0 before:border-b-white",
        "dark:bg-gray-800 dark:text-white",
        options?.props?.className
      ),
    };
  },
  closeButton: {
    className:
      "flex items-center justify-center overflow-hidden absolute top-0 right-0 w-6 h-6",
  },
  content: {
    className: "p-5 items-center flex",
  },
  transition: TRANSITIONS.overlay,
};
