import { ScrollPanelPassThroughOptions } from "primereact/scrollpanel";

const scrollPanelPTOption: ScrollPanelPassThroughOptions = {
  wrapper: {
    className: "size-full relative overflow-hidden z-10 float-left",
    onTouchStart(event) {
      event.stopPropagation();
      event.currentTarget.classList.add("show-custom-scrollbar");
    },
    onTouchEnd(event) {
      event.stopPropagation();
      event.currentTarget.classList.remove("show-custom-scrollbar");
    },
    onTouchCancel(event) {
      event.stopPropagation();
      event.currentTarget.classList.remove("show-custom-scrollbar");
    },
  },
  content: {
    className: "box-border relative w-full h-full overflow-auto hide-scrollbar",
  },
  root: {
    className: "group/scroll-panel relative w-full",
  },
  barY: {
    className:
      "bg-primary-500 relative custom-scrollbar z-20 w-1 hidden group-hover/scroll-panel:block rounded-sm",
  },
};

export default scrollPanelPTOption;
