import clsx from "clsx";
import { MultiSelectPassThroughOptions } from "primereact/multiselect";
import { twMerge } from "tailwind-merge";

export const multiselectPTOptions: MultiSelectPassThroughOptions = {
  root: (options) => ({
    className: clsx(
      "border border-neutral-600 flex gap-x-1 justify-between rounded px-3 text-gray-900 h-10",
      "hover:bg-gray-100 hover:cursor-pointer",
      "dark:text-white dark:hover:bg-white/10 dark:border-neutral-700",
      options?.props.className
    ),
  }),
  labelContainer: {
    className: "overflow-hidden flex flex-auto cursor-pointer",
  },
  label: {
    className: "font-medium text-sm text-center leading-4 truncate my-auto",
  },
  token: {
    className: clsx(
      "py-1 px-2 mr-2 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-white/80 rounded-full",
      "cursor-default inline-flex items-center"
    ),
  },
  removeTokenIcon: {
    className: "ml-2",
  },
  clearIcon: {
    className: "my-auto shrink-0",
  },
  trigger: {
    className: "flex items-center justify-center",
  },
  panel: {
    className:
      "border border-gray-200 drop-shadow-md bg-white rounded-lg dark:bg-gray-800 dark:shadow-md-light dark:border-gray-900",
  },
  closeButton: {
    className: "hidden",
  },
  wrapper: {
    className: "overflow-y-auto py-2 safari-dropdown-fix translate-y-2",
  },
  item: {
    className:
      "flex font-medium gap-x-1 items-center leading-3.5 pl-1 pr-6 py-1 text-sm hover:bg-gray-100 hover:cursor-pointer dark:hover:bg-white/10",
  },
  checkbox: {
    input: {
      className: "hidden",
    },
    box: {
      className: "size-3.5",
    },
  },
  itemGroup: {
    className: "py-1 px-2 font-bold text-primary-900",
  },
  filterContainer: {
    className: twMerge(
      "relative [&_input]:p-2 [&_input]:pl-7 [&_input]:w-full [&_input]:rounded-none [&_input]:rounded-t-lg",
      "[&_input]:border-0 [&_input]:border-b [&_input]:border-gray-200"
    ),
  },
  filterIcon: { className: "ml-2 -mt-2 absolute top-1/2" },
  emptyMessage: {
    className: "px-2",
  },
};
