import clsx from "clsx";
import { InputSwitchPassThroughOptions } from "primereact/inputswitch";

const inputSwitchPTOptions: InputSwitchPassThroughOptions = {
  input: {
    className: clsx(
      [
        "absolute",
        "appearance-none",
        "bg-neutral-300",
        "cursor-pointer",
        "duration-200",
        "ease-in-out",
        "left-0",
        "size-full",
        "top-0",
        "transition-[background-color]",
        "checked:bg-primary-500",
        "checked:before:left-5.5",
        "dark:bg-gray-900",
        "dark:checked:bg-primary-500",
      ],
      [
        "before:absolute",
        "before:bg-white",
        "before:content-['']",
        "before:duration-300",
        "before:ease-in-out",
        "before:size-[1.688rem]",
        "before:left-0.5",
        "before:rounded-full",
        "before:top-0.5",
        "before:transition-[left]",
        "before:dark:bg-gray-800",
      ]
    ),
  },
  root: {
    className:
      "h-[1.938rem] overflow-hidden relative rounded-full w-[3.188rem]",
  },
};

export default inputSwitchPTOptions;
