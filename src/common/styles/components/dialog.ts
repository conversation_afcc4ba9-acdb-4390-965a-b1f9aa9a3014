import clsx from "clsx";
import { DialogPassThroughOptions } from "primereact/dialog";

export const dialogPTOptions: DialogPassThroughOptions = {
  root: (options) => ({
    className: clsx(
      "rounded-2xl shadow-lg border-0 p-6 bg-white",
      "max-h-[90%] transform scale-100",
      "m-6 w-full max-w-lg",
      {
        "transition-none transform-none h-screen w-screen max-h-full top-0 left-0 m-0 rounded-none":
          options?.state.maximized,
      },
      "dark:bg-gray-800"
    ),
  }),
  header: {
    className: "flex justify-between shrink-0 h-6 border-t-0",
  },
  headerTitle: { className: "font-bold text-lg" },
  closeButton: {
    className: clsx(
      "flex items-center justify-center overflow-hidden relative mr-2 last:mr-0",
      "size-4 text-gray-500 border-0 bg-transparent rounded-full transition duration-200 ease-in-out",
      "focus:outline-none"
    ),
  },
  closeButtonIcon: {
    className: "size-4 inline-block text-neutral-600 dark:text-white",
  },
  content: (options) => ({
    className: clsx("overflow-y-auto pt-6 flex gap-6 flex-col items-center", {
      grow: options?.state.maximized,
    }),
  }),
  mask: (options) => ({
    className: clsx("transition duration-200", {
      "bg-black/35 backdrop-blur-sm": options?.state.containerVisible,
    }),
  }),
};
