import clsx from "clsx";
import { InputTextPassThroughOptions } from "primereact/inputtext";

const inputPTOptions: InputTextPassThroughOptions = {
  root: (options) => ({
    className: clsx(
      "bg-white border border-neutral-600 h-10 px-3 py-2 rounded-lg text-gray-900 min-w-56 w-max",
      "placeholder:text-neutral-450 focus-visible:outline-none",
      {
        "opacity-60 select-none pointer-events-none cursor-default":
          options?.context.disabled,
        "border-red-500": options?.props.invalid && !options?.context.disabled,
        "border-red-500/50":
          options?.props.invalid && options?.context.disabled,
      },
      "dark:bg-gray-800 dark:text-white dark:border-neutral-700",
      options?.props.className
    ),
  }),
};

export default inputPTOptions;
