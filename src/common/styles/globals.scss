@tailwind base;
@tailwind components;
@tailwind utilities;

@import "./components/_button.scss";
@import "./components/_validation.scss";

:root {
  --foreground-rgb: 29, 28, 29;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

.loading-dot {
  animation: loading-dot ease-in-out infinite forwards;
}

@keyframes loading-dot {
  0% {
    fill: var(--fill, #dadada);
  }
  20% {
    fill: var(--fill, #dadada);
  }
  40% {
    fill: var(--fill-active, #555);
  }
  60% {
    fill: var(--fill-active, #555);
  }
  80% {
    fill: var(--fill, #dadada);
  }
  100% {
    fill: var(--fill, #dadada);
  }
}

.bg-skeleton {
  @apply animate-skeleton-bg bg-skeleton-bg bg-[length:300%_100%] dark:bg-skeleton-bg-dark;
}

pre {
  text-wrap: wrap;
}

.safari-dropdown-fix {
  &,
  ul {
    -webkit-overflow-scrolling: touch;
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
  }
}

.hide-scrollbar {
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.show-custom-scrollbar {
  & ~ .custom-scrollbar {
    display: block !important;
  }
}

.tutorial-sidebar-toggle-highlighted {
  @apply block pointer-events-none relative z-30 before:absolute before:block before:bg-white before:content-[''] before:h-[180%] before:left-[-40%] before:rounded-full before:top-[-40%] before:w-[180%];

  svg {
    @apply relative z-10;
  }
}

.tutorial-trips-highlighted {
  @apply pointer-events-none relative z-10;
  @apply before:absolute before:block before:bg-neutral-100 before:content-[''] before:h-[calc(100%+1rem)] before:-left-2 before:rounded-lg before:-top-2 before:w-[calc(100%+1rem)];
  @apply dark:before:bg-gray-900;
}

.fade-in {
  opacity: 0;
  animation: 0.2s 0.1s fade-in ease-in-out both;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.dot-spinner {
  @apply bg-primary-500 h-3 relative rounded-full w-3;

  div {
    @apply absolute bg-inherit h-full left-0 rounded-full top-0 w-full;
    animation: dot-spinner 1.4s ease-out infinite;
  }
}

@keyframes dot-spinner {
  from {
    opacity: 0.75;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(2.4);
  }
}

.text-fade-out {
  p:last-child {
    @apply overflow-x-hidden relative;

    &::after {
      @apply absolute bg-gradient-r from-transparent h-4 inline-block -ml-60 pointer-events-none relative to-[rgba(255,255,255,.7)] top-1 w-60 dark:to-[rgba(45,44,45,.8)];
      content: "";
    }
  }
}

.sample-cards-animation {
  animation: sample-card 0.3s ease-in-out 0s 1 normal both;

  .sample-card-description {
    animation: fade-in 0.2s ease-in-out 0.1s 1 reverse both;
  }
}

@keyframes sample-card {
  from {
    @apply gap-4 h-[138px] md:h-30;
  }
  to {
    @apply gap-2 h-[54px] md:gap-4;
  }
}
