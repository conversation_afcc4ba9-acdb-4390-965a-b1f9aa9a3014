import { FetcherExtraArgs } from "@/common/types/api";
import { TimeoutThreshold } from "@/common/constants";
import { getBaseUrl } from "@/common/utils";
import arrayHasElements from "@/common/utils/array-has-elements";
import addToastToLSQueue from "@/common/utils/local-storage-toasts";

const handleResponse = (res: Response) => {
  const contentType = res.headers.get("Content-Type");
  if (!contentType) {
    return;
  }

  if (!res.ok) {
    const status = res.status;
    return res.json().then((errorBody) => {
      throw {
        ...errorBody,
        detail: arrayHasElements(errorBody?.detail)
          ? errorBody?.detail[0]?.msg
          : errorBody?.detail,
        status,
      };
    });
  }

  if (contentType?.startsWith("text")) {
    return res.text();
  }
  return res.json();
};

export const fetcher = async (
  pathname: string,
  args?: { arg: FetcherExtraArgs | undefined }
) => {
  const { arg: extraArgs } = args ?? {};
  const { options, urlEnding } = extraArgs ?? {};
  const headers = new Headers({ "Content-Type": "application/json" });
  // Add timezone if running in a browser
  try {
    if (typeof window !== "undefined") {
      const tz = Intl.DateTimeFormat().resolvedOptions().timeZone;
      if (tz) {
        headers.set("X-Timezone", tz);
      }
    }
  } catch (e) {
    // silently fail and skip setting timezone
  }

  const url = `${getBaseUrl()}${pathname}${urlEnding ?? ""}`;
  const requestOptions: RequestInit = {
    credentials: "include",
    headers,
    signal: AbortSignal.timeout(TimeoutThreshold),
    ...options,
  };

  return fetch(url, requestOptions)
    .then((res) => handleResponse(res))
    .catch((error) => {
      if (error?.status === 401) {
        addToastToLSQueue({
          severity: "error",
          content: "401: Invalid credentials",
        });
        window.location.replace("/login");
      }

      throw error;
    });
};
