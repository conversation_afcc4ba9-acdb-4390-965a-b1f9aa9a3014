import useSWR from "swr";
import { ROUTES } from "@/common/constants";

export const useSession = () => {
  const { data, isLoading, mutate } = useSWR(
    ROUTES.ACCESS,
    (url) => fetch(url).then((res) => res.json()),
    { dedupingInterval: 0 }
  );

  return {
    accessToken: data?.accessToken ?? null,
    refreshToken: data?.refreshToken ?? null,
    isAuthenticated: !!data?.accessToken,
    isLoadingSession: isLoading,
    mutateSession: mutate,
  };
};
