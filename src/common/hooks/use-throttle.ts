import { useCallback, useRef } from "react";

/**
 * Hook used to throttle a function call.
 *
 * @param callback Throttled callback function.
 * @param threshold Time threshold from subsequent calls in milliseconds (default: 500).
 */
export default function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  threshold?: number
) {
  let clearFlag = useRef(true);

  return useCallback(
    (...args: Parameters<T>) => {
      if (clearFlag.current) {
        callback(...args);
        clearFlag.current = false;
        setTimeout(() => {
          clearFlag.current = true;
        }, threshold ?? 500);
      }
    },
    [callback, threshold]
  );
}
