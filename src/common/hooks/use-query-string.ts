import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback } from "react";

type QueryStringObject = { [key: string]: string | number | null | undefined };

export function useCreateQueryString() {
  const searchParams = useSearchParams();

  const createQueryString = useCallback(
    (input: QueryStringObject) => {
      const params = new URLSearchParams(searchParams.toString());

      Object.entries(input).forEach(([key, value]) => {
        if (value != null) {
          params.set(key, value.toString());
        } else {
          params.delete(key);
        }
      });

      return params.toString();
    },
    [searchParams]
  );

  return createQueryString;
}

export function useUpdateQueryString() {
  const router = useRouter();
  const pathname = usePathname();
  const createQueryString = useCreateQueryString();

  return (input: QueryStringObject) => {
    router.push(pathname + "?" + createQueryString(input));
  };
}
