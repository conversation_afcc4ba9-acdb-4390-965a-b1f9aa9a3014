import { ReactNode } from "react";
import { ConfirmPopup } from "primereact/confirmpopup";
import { useToggle } from "./toggles";

type ConfirmPopupOptions = {
  message?: ReactNode;
  onConfirm: VoidFunction;
};

export default function useConfirmPopup({
  message,
  onConfirm,
}: ConfirmPopupOptions) {
  const { turnOff, turnOn, value } = useToggle();

  const Popup = (
    <ConfirmPopup
      accept={() => {
        onConfirm?.();
        turnOff();
      }}
      message={message ?? "Are you sure you want to proceed?"}
      visible={value}
      onHide={turnOff}
    />
  );

  return {
    close: turnOff,
    isOpen: value,
    open: turnOn,
    Popup,
  };
}
