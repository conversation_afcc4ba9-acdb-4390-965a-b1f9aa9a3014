import { useEffect, RefObject } from "react";

/**
 * Hook that handles click/touch outside of a specified element
 *
 * @param ref - The ref of the element to detect clicks outside of
 * @param handler - The callback function to run when a click outside is detected
 * @param touchOnly - If true, only detects touch events (for mobile devices)
 */
export const useOnClickOutside = <T extends HTMLElement = HTMLElement>(
  ref: RefObject<T>,
  handler: (event: MouseEvent | TouchEvent) => void,
  touchOnly: boolean = false
) => {
  useEffect(() => {
    const listener = (event: MouseEvent | TouchEvent) => {
      // Do nothing if the ref is not set or if clicking ref's element or descendant elements
      if (!ref.current || ref.current.contains(event.target as Node)) {
        return;
      }
      handler(event);
    };

    // Only use touch events if touchOnly is true, otherwise use both mouse and touch events
    if (!touchOnly) {
      document.addEventListener("mousedown", listener);
    }
    document.addEventListener("touchstart", listener);

    return () => {
      if (!touchOnly) {
        document.removeEventListener("mousedown", listener);
      }
      document.removeEventListener("touchstart", listener);
    };
  }, [ref, handler, touchOnly]);
};
