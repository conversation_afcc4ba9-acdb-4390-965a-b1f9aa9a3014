import { useEffect, useRef } from "react";
import { useDetectKeyboardOpen } from "./use-detect-keyboard-open";

/**
 * Hook to handle scroll-into-view behavior for form inputs when keyboard opens
 * This is specifically designed for forms and provides better control over scroll behavior
 */
export const useFormKeyboardScroll = () => {
  const lastFocusedInput = useRef<
    HTMLInputElement | HTMLTextAreaElement | null
  >(null);
  const isKeyboardOpen = useDetectKeyboardOpen();

  useEffect(() => {
    const handleFocus = (event: FocusEvent) => {
      const target = event.target as HTMLInputElement | HTMLTextAreaElement;

      // Only track form inputs
      if (
        target &&
        (target.tagName === "INPUT" || target.tagName === "TEXTAREA")
      ) {
        lastFocusedInput.current = target;
      }
    };

    const handleBlur = (event: FocusEvent) => {
      const target = event.target as HTMLInputElement | HTMLTextAreaElement;

      // Clear the reference if the blurred element was the last focused one
      if (lastFocusedInput.current === target) {
        lastFocusedInput.current = null;
      }
    };

    document.addEventListener("focusin", handleFocus);
    document.addEventListener("focusout", handleBlur);

    return () => {
      document.removeEventListener("focusin", handleFocus);
      document.removeEventListener("focusout", handleBlur);
    };
  }, []);

  useEffect(() => {
    if (isKeyboardOpen && lastFocusedInput.current) {
      const input = lastFocusedInput.current;
      // Check if the input is still focused and visible
      if (document.activeElement === input && input.offsetParent !== null) {
        input.scrollIntoView({
          block: "start",
        });
      }
    }
  }, [isKeyboardOpen]);

  return { isKeyboardOpen };
};
