"use client";

import { useCallback, useRef } from "react";
import { useSet<PERSON>tom } from "jotai";
import { Toast, ToastMessage } from "primereact/toast";
import { toastsQueueAtom } from "@/common/store/toast";
import { TimeoutErrorName } from "@/common/constants";

const getErrorToastContent = (error: any, message?: string) => {
  const { detail, status } = error ?? {};
  const text = !!message && <p>{message}</p>;

  if (!detail || typeof detail !== "string" || !status) {
    return text;
  }

  return (
    <div>
      {text}
      <p>{`${status}: ${detail}`}</p>
    </div>
  );
};

export default function useToast() {
  const ref = useRef<Toast>(null);
  const setQueue = useSetAtom(toastsQueueAtom);

  const showToast = useCallback(
    (message: ToastMessage) => setQueue((queue) => [...queue, message]),
    [setQueue]
  );
  const showErrorToast = useCallback(
    (error: any, message?: string) => {
      const errorData =
        error?.name === TimeoutErrorName
          ? { status: 408, detail: "Request timeout" }
          : error;

      showToast({
        content: getErrorToastContent(errorData, message),
        severity: "error",
      });
    },
    [showToast]
  );

  return {
    ref,
    showErrorToast,
    showToast,
  };
}
