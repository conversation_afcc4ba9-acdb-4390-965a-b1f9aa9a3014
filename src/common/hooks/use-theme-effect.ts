import { useEffect } from "react";
import { useAtomValue } from "jotai";
import { darkModeAtom } from "@/common/store/dark-mode";
import { DarkModeOptions } from "@/common/constants/theme";

export function useThemeEffect() {
  const darkMode = useAtomValue(darkModeAtom);

  useEffect(() => {
    const darkModeQuery = window.matchMedia("(prefers-color-scheme: dark)");

    const handleChange = () => {
      document.documentElement.classList.toggle(
        "dark",
        darkMode === DarkModeOptions.DARK ||
          (darkMode === DarkModeOptions.DEVICE_DEFAULT && darkModeQuery.matches)
      );
    };

    // Initial check
    handleChange();

    if (darkMode === DarkModeOptions.DEVICE_DEFAULT) {
      darkModeQuery.addEventListener("change", handleChange);
    }

    return () => {
      darkModeQuery.removeEventListener("change", handleChange);
    };
  }, [darkMode]);
}
