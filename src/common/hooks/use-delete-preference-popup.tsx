import { useState } from "react";
import { useDeletePreference } from "@/features/user/hooks/api";
import useConfirmPopup from "./use-confirm-popup";

export default function useDeletePreferencePopup() {
  const [preferenceToDelete, setPreferenceToDelete] = useState<{
    key: string;
    value: string;
  } | null>(null);

  const { trigger: deletePreference } = useDeletePreference();
  const message = (
    <>
      <h2 className="font-medium pb-2 border-b border-neutral-250 dark:border-gray-900">
        Delete this preference
      </h2>
      <p className="leading-5 mt-6">
        Are you sure you want to delete the preference? This action cannot be
        undone.
      </p>
    </>
  );

  const { open, close, isOpen, Popup } = useConfirmPopup({
    message,
    onConfirm: () =>
      !!preferenceToDelete &&
      deletePreference(preferenceToDelete?.key, preferenceToDelete?.value),
  });

  return {
    close,
    isOpen,
    open: (preference: { key: string; value: string }) => {
      setPreferenceToDelete(preference);
      open();
    },
    Popup,
  };
}
