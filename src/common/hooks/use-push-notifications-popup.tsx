import { useStoreToggle } from "./toggles";
import PermissionPopup from "../components/permission-popup";
import * as nativebridge from "@nrk/nativebridge";
import { OutgoingJSBridgeEvents } from "../types/js-bridge-events";
import { showPushNotificationsPopupAtom } from "../store/show-push-notifications-popup";

export default function usePushNotificationsPopup() {
  const { turnOff, turnOn, value } = useStoreToggle(
    showPushNotificationsPopupAtom
  );

  const onConfirm = () => {
    try {
      nativebridge.emit(
        OutgoingJSBridgeEvents.NOTIFICATIONS_SETTINGS_BUTTON_PRESSED
      );
    } catch (e) {}
    turnOff();
  };

  const PushNotificationsPopup = (
    <PermissionPopup
      title="Enable push notifications"
      description="Please change your Settings so I can send you timely notifications."
      onHide={turnOff}
      onConfirm={onConfirm}
      visible={value}
    />
  );

  return {
    openPushNotificationsPopup: turnOn,
    PushNotificationsPopup,
  };
}
