import { useCallback, useRef } from "react";

/**
 * Hook used to debounce a function call.
 *
 * @param callback Debounced callback function.
 * @param threshold Time threshold from subsequent calls in milliseconds (default: 500).
 */
export default function useDebounce<T extends (...args: any) => any>(
  callback: T,
  threshold: number = 500
) {
  const timer = useRef<ReturnType<typeof setTimeout> | undefined>(undefined);

  return useCallback(
    (...args: Parameters<T>) => {
      clearTimeout(timer.current);
      timer.current = setTimeout(() => callback(...args), threshold);
    },
    [callback, threshold]
  );
}
