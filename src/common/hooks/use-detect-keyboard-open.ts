import { useState, useEffect } from "react";
import useDebounce from "./use-debounce";

/**
 * React hook to detect keyboard opening and closing on mobile devices
 * Works best on iOS using visualViewport API
 *
 * @param {number} minKeyboardHeight - Minimum height difference to consider keyboard open (default: 150px)
 * @param {function} onResize - Callback function to execute on resize (default: () => {})
 * @returns {Object} - Contains isKeyboardOpen state and keyboard height
 */
export const useDetectKeyboardOpen = ({
  minKeyboardHeight = 150,
  onResize = () => {},
} = {}) => {
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);

  const debouncedResizeHandler = useDebounce(() => {
    const newViewportHeight = window.visualViewport
      ? window.visualViewport.height
      : window.innerHeight;

    // Update keyboard status based on height change
    if (window.screen.height - minKeyboardHeight > newViewportHeight) {
      setIsKeyboardOpen(true);
      document.body.style.height = `${newViewportHeight}px`;
      document.body.scrollIntoView({ block: "start" });
    } else {
      setIsKeyboardOpen(false);
      document.body.style.height = "100dvh";
    }

    onResize();
  }, 50);

  useEffect(() => {
    if (window.visualViewport) {
      window.visualViewport.addEventListener("resize", debouncedResizeHandler);
      window.visualViewport.addEventListener("scroll", debouncedResizeHandler);
    } else {
      window.addEventListener("resize", debouncedResizeHandler);
    }

    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener(
          "resize",
          debouncedResizeHandler
        );
        window.visualViewport.removeEventListener(
          "scroll",
          debouncedResizeHandler
        );
      } else {
        window.removeEventListener("resize", debouncedResizeHandler);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [minKeyboardHeight, onResize]);

  return isKeyboardOpen;
};
