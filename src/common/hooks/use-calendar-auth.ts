import { useCallback, useEffect, useRef, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ReadyState } from "react-use-websocket";
import { GOOGLE_CALENDAR_SCOPE } from "@/common/constants";
import { useAccess } from "./use-access";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import useWebSocket from "@/features/chat/hooks/use-web-socket";

interface UseCalendarAuthProps {
  redirectPath?: string;
  redirectEnabled?: boolean;
}

interface UseCalendarAuthReturn {
  isLoading: boolean;
}

export const useCalendarAuth = ({ redirectPath, redirectEnabled }: UseCalendarAuthProps): UseCalendarAuthReturn => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const isCalendarMessageSent = useRef(false);
  const [isLoading, setIsLoading] = useState(true);

  const calendarPermissionGranted = searchParams.get("calendar_granted");
  const googleCode = searchParams.get("code");
  const googleScope = searchParams.get("scope");
  const googleCalendarError = searchParams.get("error");

  const hasCalendarParams = !!(
    calendarPermissionGranted || 
    googleCode || 
    googleScope || 
    googleCalendarError
  );

  const { send, readyState } = useWebSocket(
    process.env.NEXT_PUBLIC_WS_URL as string
  );

  const { calendarTokenRequest } = useAccess();

  const cleanup = useCallback(() => {
    isCalendarMessageSent.current = true;
    setIsLoading(false);
    if (redirectEnabled && redirectPath) {
      router.replace(redirectPath);
    }
  }, [router, redirectPath]);

  const sendSilentMessage = useCallback(
    (message?: string) => {
      send({
        text: message,
        type: OutgoingMessageTypes.SILENT_PROMPT,
      });
      cleanup();
    },
    [cleanup, send]
  );

  const checkMicrosoftPermission = useCallback(() => {
    if (calendarPermissionGranted) {
      sendSilentMessage(
        `Calendar permission has ${
          calendarPermissionGranted === "true" ? "" : "not "
        }been granted`
      );
    }
  }, [calendarPermissionGranted, sendSilentMessage]);

  const checkGooglePermission = useCallback(() => {
    if (googleCode && googleScope) {
      if (googleScope.includes(GOOGLE_CALENDAR_SCOPE)) {
        const redirectUri = window.location.origin + redirectPath;

        calendarTokenRequest({ code: googleCode, redirect_uri: redirectUri });
        cleanup();
      } else {
        sendSilentMessage("Calendar scope has not been granted.");
      }
    }
    if (googleCalendarError) {
      sendSilentMessage(
        `Google calendar request error: ${googleCalendarError}`
      );
    }
  }, [
    calendarTokenRequest,
    cleanup,
    googleCalendarError,
    googleCode,
    googleScope,
    redirectPath,
    sendSilentMessage,
  ]);

  useEffect(() => {
    if (isCalendarMessageSent.current) {
      return;
    }
    
    if (!hasCalendarParams) {
      setIsLoading(false);
      return;
    }
    
    if (readyState !== ReadyState.OPEN) {
      return;
    }
    
    checkMicrosoftPermission();
    checkGooglePermission();
  }, [checkGooglePermission, checkMicrosoftPermission, readyState, hasCalendarParams]);

  return { isLoading };
};
