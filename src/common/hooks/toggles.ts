import { useCallback, useState } from "react";
import { PrimitiveAtom, useAtom } from "jotai";

type ToggleReturnType = {
  turnOff: VoidFunction;
  turnOn: VoidFunction;
  toggle: VoidFunction;
  value: boolean;
};

export function useToggle(initialValue: boolean = false): ToggleReturnType {
  const [value, setValue] = useState<boolean>(initialValue);

  return {
    turnOff: useCallback(() => setValue(false), [setValue]),
    turnOn: useCallback(() => setValue(true), [setValue]),
    toggle: useCallback(() => setValue((value) => !value), [setValue]),
    value,
  };
}

export function useStoreToggle(atom: PrimitiveAtom<boolean>): ToggleReturnType {
  const [value, setValue] = useAtom(atom);

  return {
    turnOff: useCallback(() => setValue(false), [setValue]),
    turnOn: useCallback(() => setValue(true), [setValue]),
    toggle: useCallback(() => setValue((value) => !value), [setValue]),
    value,
  };
}
