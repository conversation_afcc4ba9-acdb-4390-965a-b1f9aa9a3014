import { useSearchParams } from "next/navigation";
import { useCallback, useMemo } from "react";
import { useUpdateQueryString } from "./use-query-string";

export type QueryParamConverter<T> = {
  encode: (value: T) => string | number | null | undefined;
  decode: (value: string | null) => T;
};

export const StringParam: QueryParamConverter<string | null> = {
  encode: (value) => value || null,
  decode: (value) => value || "",
};

export const NumberParam: QueryParamConverter<number> = {
  encode: (value) => (value != null ? value : null),
  decode: (value) => {
    if (value == null) return 0;
    const parsed = Number(value);
    return isNaN(parsed) ? 0 : parsed;
  },
};

export const BooleanParam: QueryParamConverter<boolean> = {
  encode: (value) => (value ? "true" : null),
  decode: (value) => value === "true",
};

export const ArrayParam: QueryParamConverter<string[]> = {
  encode: (value) => (value && value.length > 0 ? value.join(",") : null),
  decode: (value) => (value ? value.split(",").filter(Boolean) : []),
};

/**
 * Hook for managing a single query parameter with type conversion
 * @param key - The query parameter key
 * @param converter - Object with encode/decode functions for type conversion
 * @returns [value, setValue] tuple
 */
export function useQueryParam<T>(
  key: string,
  converter: QueryParamConverter<T>
): [T, (value: T) => void] {
  const searchParams = useSearchParams();
  const updateQueryString = useUpdateQueryString();

  const value = useMemo(() => {
    const rawValue = searchParams.get(key);
    return converter.decode(rawValue);
  }, [searchParams, key, converter]);

  const setValue = useCallback(
    (newValue: T) => {
      const encodedValue = converter.encode(newValue);
      updateQueryString({ [key]: encodedValue });
    },
    [updateQueryString, key, converter]
  );

  return [value, setValue];
}
