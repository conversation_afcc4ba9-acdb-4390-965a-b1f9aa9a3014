import { <PERSON><PERSON><PERSON><PERSON> } from "primereact/utils";
import { useEffect, useState } from "react";

// <PERSON> needed to check the device type on the client side
export const useTouchDevice = ({
  onDetect,
}: {
  onDetect?: (isTouchDevice: boolean) => void;
} = {}) => {
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    const isTouch = DomHandler.isTouchDevice();
    setIsTouchDevice(isTouch);
    onDetect?.(isTouch);
  }, [onDetect]);

  return { isTouchDevice };
};
