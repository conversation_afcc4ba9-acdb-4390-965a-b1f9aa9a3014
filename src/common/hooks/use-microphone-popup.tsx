import { useToggle } from "./toggles";
import PermissionPopup from "../components/permission-popup";
import * as nativebridge from "@nrk/nativebridge";
import { OutgoingJSBridgeEvents } from "../types/js-bridge-events";

export default function useMicrophonePopup() {
  const { turnOff, turnOn, value } = useToggle();

  const onConfirm = () => {
    try {
      nativebridge.emit(
        OutgoingJSBridgeEvents.MICROPHONE_SETTINGS_BUTTON_PRESSED
      );
    } catch (e) {}
    turnOff();
  };

  const MicrophonePopup = (
    <PermissionPopup
      title="Microphone access required"
      description="Please change your Settings to allow me to access your microphone."
      onHide={turnOff}
      onConfirm={onConfirm}
      visible={value}
    />
  );

  return {
    openMicrophonePopup: turnOn,
    MicrophonePopup,
  };
}
