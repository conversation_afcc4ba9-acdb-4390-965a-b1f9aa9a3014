import useSWRMutation, { SWRMutationConfiguration } from "swr/mutation";
import { fetcher } from "../api/fetcher";
import { ApiRestMethod } from "../constants";

function postRequest<T, R>(url: string, { arg }: { arg: T }): Promise<R> {
  return fetcher(url, {
    arg: {
      options: { method: ApiRestMethod.POST, body: JSON.stringify(arg) },
    },
  });
}

export function usePostRequest<T, R = any>(
  url: string,
  options?: SWRMutationConfiguration<R, any, any, T>
) {
  return useSWRMutation(url, postRequest<T, R>, options);
}
