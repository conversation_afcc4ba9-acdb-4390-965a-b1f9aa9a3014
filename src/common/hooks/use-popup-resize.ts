import { useEffect, useRef } from "react";
import { Panel } from "primereact/panel";
import useDebounce from "./use-debounce";
import { useViewportSize } from "./use-viewport-size";

export const usePopupResize = () => {
  const { isDesktop } = useViewportSize();
  const popupRef = useRef<Panel>(null);

  const debouncedResizeHandler = useDebounce(() => {
    const popup = popupRef.current?.getElement();
    if (!popup) {
      return;
    }

    if (isDesktop) {
      popup.style.maxHeight = "revert-layer";
      return;
    }

    const newViewportHeight = window.visualViewport
      ? window.visualViewport.height
      : window.innerHeight;

    popup.style.maxHeight = `${newViewportHeight}px`;
    popup.scrollIntoView({ block: "start" });
  }, 50);

  useEffect(() => {
    if (window.visualViewport) {
      window.visualViewport.addEventListener("resize", debouncedResizeHandler);
    } else {
      window.addEventListener("resize", debouncedResizeHandler);
    }

    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener(
          "resize",
          debouncedResizeHandler
        );
      } else {
        window.removeEventListener("resize", debouncedResizeHandler);
      }
    };
  }, [debouncedResizeHandler]);

  return popupRef;
};
