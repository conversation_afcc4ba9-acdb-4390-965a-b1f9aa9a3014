import { useSearchParams } from "next/navigation";
import { useUserProfile } from "@/features/user/hooks/api";

/**
 * Hook to check if the application is in trying mode
 * @returns boolean indicating if trying mode is enabled
 */
export function useIsTryingMode(): boolean {
  const searchParams = useSearchParams();
  const shouldDisableTryingMode = searchParams.get("tryingMode") === "0";
  const { sampleTrips } = useUserProfile();

  if (sampleTrips.shouldShowSampleTrips === false) {
    return false;
  }

  return !shouldDisableTryingMode;
}
