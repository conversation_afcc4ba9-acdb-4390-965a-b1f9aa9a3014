import { useCallback } from "react";
import { useRouter } from "next/navigation";
import useS<PERSON>, { Key } from "swr";
import useSWRMutation from "swr/mutation";
import { useAtomValue, useSetAtom } from "jotai";
import { FetcherExtraArgs } from "@/common/types/api";
import { TripsList, Trip as TripType } from "@/features/chat/types/trip";
import { ApiPaths, ApiRestMethod, ROUTES } from "@/common/constants";
import { fetcher } from "@/common/api/fetcher";
import { useStoreToggle } from "@/common/hooks/toggles";
import { isUserSelectedAtom } from "@/features/admin/store/foreign-user";
import {
  currentTripAtom,
  tripDetailsAtom,
} from "@/features/chat/store/current-trip";
import { showSidebarAtom } from "@/common/store/sidebar";
import useToast from "./use-toast";
import { chatHistoryAtom } from "@/features/chat/store/chat";
import { useIsTryingMode } from "@/common/hooks/use-trying-mode";

export function useTripsList() {
  const { showErrorToast } = useToast();

  return useSWR<TripsList>(ApiPaths.TRIPS_LIST, fetcher, {
    onError: (error) => showErrorToast(error, "Trips list request failed."),
    revalidateOnFocus: false,
    revalidateOnMount: false,
  });
}

export function useGetTripDetails() {
  const { showErrorToast } = useToast();
  const setTripDetails = useSetAtom(tripDetailsAtom);
  const { data: tripData } = useTripsList();
  const currentTrip = useAtomValue(currentTripAtom);

  const isOwnTrip = [
    ...(tripData?.booked ?? []),
    ...(tripData?.planned ?? []),
  ].some((trip) => trip.id === currentTrip);
  const isUserSelected = useAtomValue(isUserSelectedAtom);
  const basePath = isUserSelected
    ? ApiPaths.ADMIN_GET_TRIP_DATA
    : ApiPaths.TRIPS;

  const { error, trigger } = useSWRMutation(basePath, fetcher);

  const getTripDetails = useCallback(
    (tripId: number) => {
      setTripDetails((currentValue) => ({ ...currentValue, isLoading: true }));

      if (!isOwnTrip && !isUserSelected) {
        // Let's check tripData again with tripId, as itinerary page needs to get the details of
        // multiple trips, not just the current one.
        const isAccessibleTrip = [
          ...(tripData?.booked ?? []),
          ...(tripData?.planned ?? []),
        ].some((trip) => trip.id === tripId);
        if (!isAccessibleTrip) {
          // cannot access trip details that doesn't belong to the user or not as admin
          setTripDetails((currentValue) => ({
            ...currentValue,
            isLoading: false,
          }));
          return;
        }
      }

      trigger({ urlEnding: `/${tripId}` })
        .then((data) => {
          setTripDetails((oldValue) => ({
            ...oldValue,
            [tripId]: data,
            isLoading: false,
          }));
        })
        .catch((error) => {
          showErrorToast(error, "Trip details request failed.");
          setTripDetails((currentValue) => ({
            ...currentValue,
            isLoading: false,
          }));
        });
    },
    [
      isOwnTrip,
      tripData,
      isUserSelected,
      trigger,
      setTripDetails,
      showErrorToast,
    ]
  );

  return {
    error,
    getTripDetails,
  };
}

export function useCreateTrip() {
  const router = useRouter();
  const { mutate: mutateTripsList } = useTripsList();
  const { showErrorToast } = useToast();
  const tryingModeEnabled = useIsTryingMode();
  
  const { trigger: triggerCreateTrip, isMutating } = useSWRMutation<
    TripType,
    any,
    Key,
    FetcherExtraArgs
  >(ApiPaths.NEW_TRIP, fetcher, {
    onSuccess: ({ id }) => {
      mutateTripsList();
      const redirectTripUrl = `${ROUTES.TRIPS}/${id}`;
      router.push(redirectTripUrl);
    },
  });

  function createTrip() {
    triggerCreateTrip({
      options: {
        method: ApiRestMethod.POST,
        body: tryingModeEnabled
          ? JSON.stringify({ tryingModeEnabled })
          : undefined,
      },
    }).catch((error) => showErrorToast(error, "Trip creation failed."));
  }

  return {
    createTrip,
    isMutating,
  };
}

export function useDeleteTrip(id: number) {
  const currentTrip = useAtomValue(currentTripAtom);
  const setChatHistory = useSetAtom(chatHistoryAtom);
  const { showErrorToast } = useToast();
  const { data: trips, mutate: mutateTripsList } = useTripsList();
  const { createTrip } = useCreateTrip();
  const { turnOff: closeSidebar } = useStoreToggle(showSidebarAtom);

  const swrReturn = useSWRMutation(`${ApiPaths.TRIPS}/${id}`, fetcher, {
    onSuccess: () => {
      const hasTripsLeft =
        (trips?.booked?.length as number) > 1 ||
        (trips?.planned?.length as number) > 1;
      // Note: the trips array are being checked before the deletion
      if (hasTripsLeft) {
        if (id === currentTrip) {
          setChatHistory([]);
          createTrip();
          closeSidebar();
        } else {
          mutateTripsList();
        }
      } else {
        createTrip();
      }
    },
    revalidate: false,
  });

  return {
    ...swrReturn,
    trigger: () =>
      swrReturn
        .trigger({ options: { method: ApiRestMethod.DELETE } })
        .catch((error) => showErrorToast(error, "Trip deletion failed.")),
  };
}
