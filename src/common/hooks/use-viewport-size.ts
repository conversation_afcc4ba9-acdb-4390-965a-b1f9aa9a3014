import { useState, useEffect } from "react";
import { isClient } from "../utils";

const MOBILE_MD_THRESHOLD = 768;
/**
 * Hook that returns whether the current viewport is mobile or desktop
 * Uses 768px (Tailwind's md breakpoint) as the threshold
 */
export function useViewportSize({
  onDetect,
}: {
  onDetect?: (isMobile: boolean) => void;
} = {}) {
  const [isMobile, setIsMobile] = useState(
    isClient() && window.innerWidth < MOBILE_MD_THRESHOLD
  );

  useEffect(() => {
    const updateViewportSize = () => {
      const isMobile = window.innerWidth < MOBILE_MD_THRESHOLD;
      setIsMobile(isMobile);
      return isMobile;
    };

    // Initial check
    onDetect?.(updateViewportSize());

    window.addEventListener("resize", updateViewportSize);

    return () => window.removeEventListener("resize", updateViewportSize);
  }, [onDetect]);

  return { isMobile, isDesktop: !isMobile };
}
