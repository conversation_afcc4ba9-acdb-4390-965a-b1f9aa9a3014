import useWebSocket from "@/features/chat/hooks/use-web-socket";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import { ApiPaths, ApiRestMethod } from "../constants";
import useSWRMutation from "swr/mutation";
import { fetcher } from "../api/fetcher";
import { isNativeBridgeAtom } from "../store/nativebridge";
import { useAtomValue } from "jotai";
import { OutgoingJSBridgeEvents } from "../types/js-bridge-events";
import * as nativebridge from "@nrk/nativebridge";
import { NativeCalendarResponse } from "../types/native-calendar-response";
import { useRouter } from "next/navigation";

export function useAccess() {
  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);
  const isNativeBridge = useAtomValue(isNativeBridgeAtom);
  const router = useRouter();

  const tokenExchangeRequest = (
    url: string,
    { arg }: { arg: NativeCalendarResponse }
  ) =>
    fetcher(url, {
      arg: {
        options: { method: ApiRestMethod.POST, body: JSON.stringify(arg) },
      },
    });

  const { trigger: calendarTokenRequest } = useSWRMutation(
    isNativeBridge
      ? ApiPaths.NATIVE_GOOGLE_CALENDAR_TOKEN
      : ApiPaths.GOOGLE_CALENDAR_TOKEN,
    tokenExchangeRequest,
    {
      onSuccess: () => {
        sendMessage("Calendar permission has been granted");
      },
      onError: () => {
        sendMessage(
          "An error occurred while updating the token with the calendar permission."
        );
      },
    }
  );

  const sendMessage = (message?: string) => {
    send({
      text: message,
      type: OutgoingMessageTypes.SILENT_PROMPT,
    });
  };

  const requestGoogleCalendarAccess = (accessFlowUrl?: string) => {
    if (!isNativeBridge && accessFlowUrl) {
      router.push(accessFlowUrl);
      return;
    }

    try {
      nativebridge.emit(OutgoingJSBridgeEvents.REQUEST_CALENDAR_ACCESS);
    } catch (e) {}
  };

  const onNativeCalendarResponse = ({
    code,
    refreshToken,
  }: NativeCalendarResponse) => {
    if (code || refreshToken) {
      calendarTokenRequest({ code, refreshToken });
    } else {
      sendMessage("Calendar scope has not been granted.");
    }
  };

  return {
    requestGoogleCalendarAccess,
    onNativeCalendarResponse,
    calendarTokenRequest,
  };
}
