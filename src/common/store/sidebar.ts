import { atom } from "jotai";
import {
  OnboardingChecklist,
  OnboardingChecklistItems,
} from "@/common/types/onboarding-checklist";
import {
  BOOKED_TRIPS_EXPANDED_ALL_KEY,
  BOOKED_TRIPS_EXPANDED_KEY,
  PLANNING_TRIPS_EXPANDED_ALL_KEY,
  PLANNING_TRIPS_EXPANDED_KEY,
  SAVED_TRIPS_EXPANDED_ALL_KEY,
  SAVED_TRIPS_EXPANDED_KEY,
} from "../constants";
import { atomWithStorage } from "jotai/utils";

export const showSidebarAtom = atom<boolean>(false);

export const onboardingChecklistAtom = atom<OnboardingChecklist>({
  [OnboardingChecklistItems.CREATE_TRIPS]: false,
  [OnboardingChecklistItems.TRAVEL_PREFERENCES]: false,
  [OnboardingChecklistItems.TRAVEL_POLICY]: false,
});

export const accountMenuAtom = atom<boolean>(false);

export const planningTripsExpandedAtom = atomWithStorage(
  PLANNING_TRIPS_EXPANDED_KEY,
  true
);
export const planningTripsExpandedAllAtom = atomWithStorage(
  PLANNING_TRIPS_EXPANDED_ALL_KEY,
  false
);
export const bookedTripsExpandedAtom = atomWithStorage(
  BOOKED_TRIPS_EXPANDED_KEY,
  true
);
export const bookedTripsExpandedAllAtom = atomWithStorage(
  BOOKED_TRIPS_EXPANDED_ALL_KEY,
  false
);

export const savedTripsExpandedAtom = atomWithStorage(
  SAVED_TRIPS_EXPANDED_KEY,
  true
);
export const savedTripsExpandedAllAtom = atomWithStorage(
  SAVED_TRIPS_EXPANDED_ALL_KEY,
  false
);
