"use client";

import Divider from "@/common/components/divider";
import { HttpStatusCode } from "@/common/constants";
import regex from "@/common/constants/regex";
import { useIsTryingMode } from "@/common/hooks/use-trying-mode";
import { useOtpLogin } from "@/features/user/hooks/api";
import { Button } from "primereact/button";
import { InputOtp, InputOtpChangeEvent } from "primereact/inputotp";
import { InputText } from "primereact/inputtext";
import type { KeyboardEventHandler } from "react";
import { useMemo, useState } from "react";

const RESEND_COUNT_DOWN_SECONDS = 30;

export function OneTimePassword() {
  const [email, setEmail] = useState("");
  const [code, setCode] = useState("");
  const [canSendAgain, setCanSendAgain] = useState(true);
  const [isCodeInvalid, setIsCodeInvalid] = useState(false);
  const [resendCountdown, setResendCountdown] = useState(
    RESEND_COUNT_DOWN_SECONDS
  );
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [showEmailError, setShowEmailError] = useState(false);
  const tryingModeEnabled = useIsTryingMode();

  const {
    generateOtp,
    validateOtp,
    isLoadingGenerateOtp,
    isLoadingValidateOtp,
    codeValidationError,
  } = useOtpLogin(tryingModeEnabled);

  const errorMessage = useMemo(() => {
    if (isCodeInvalid) {
      return "Enter a 6-digit code";
    }
    if (codeValidationError?.status === HttpStatusCode.BAD_REQUEST) {
      return "Invalid code, please try again";
    }
    return codeValidationError?.detail;
  }, [codeValidationError?.detail, codeValidationError?.status, isCodeInvalid]);

  const handleKeyDownEmail: KeyboardEventHandler<HTMLInputElement> = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      onSendCode();
    }
  };

  const onChangeCode = (e: InputOtpChangeEvent) => {
    const value = e.value?.toString() ?? "";
    setCode(value);
    if (value.length === 6) {
      onSubmitCode(value);
    }
  };

  const onSendCode = () => {
    if (isLoadingGenerateOtp) {
      return;
    }
    if (!regex.email.test(email)) {
      setShowEmailError(true);
      return;
    }
    setShowEmailError(false);

    generateOtp({ email }, { onSuccess: () => setIsCodeSent(true) });
  };

  const onSendAgain = () => {
    if (isLoadingGenerateOtp || !canSendAgain) {
      return;
    }

    generateOtp(
      { email },
      {
        onSuccess: () => {
          setCanSendAgain(false);
          const interval = setInterval(() => {
            setResendCountdown((prev) => prev - 1);
          }, 1000);
          setTimeout(() => {
            clearInterval(interval);
            setCanSendAgain(true);
            setResendCountdown(RESEND_COUNT_DOWN_SECONDS);
          }, RESEND_COUNT_DOWN_SECONDS * 1000);
        },
      }
    );
  };

  const onSubmitCode = (code: string) => {
    if (isLoadingValidateOtp || isLoadingGenerateOtp) {
      return;
    }
    if (!regex.otp.test(code)) {
      setIsCodeInvalid(true);
      return;
    }
    setIsCodeInvalid(false);
    validateOtp({ email, code });
  };

  if (!isCodeSent) {
    return (
      <>
        <Divider
          className="text-white my-2 md:my-5"
          lineClassName="border-white"
        >
          Or continue with email
        </Divider>
        <div className="w-full">
          <InputText
            className="max-w-80 w-full dark:bg-white dark:text-gray-900"
            keyfilter="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            onKeyDown={handleKeyDownEmail}
            invalid={showEmailError}
            disabled={isLoadingGenerateOtp}
            name="email"
          />
          {showEmailError && (
            <p className="text-red-600 mt-2 leading-5.5">Enter valid email</p>
          )}
        </div>
        <Button
          label="Send code"
          className="w-full mb-2 tall:mt-2 dark:bg-gray-950 dark:text-white dark:border-gray-400 /
          dark:disabled:bg-gray-500"
          outlined
          severity="secondary"
          onClick={onSendCode}
          disabled={!email || isLoadingGenerateOtp}
          loading={isLoadingGenerateOtp}
        />
      </>
    );
  }

  return (
    <div className="flex flex-col gap-1 items-center tall:mt-2">
      <p className="text-center text-gray-350">
        Please enter the code sent to your email.
      </p>
      <div className="w-fit">
        <InputOtp
          value={code}
          onChange={onChangeCode}
          disabled={isLoadingValidateOtp}
          length={6}
          integerOnly
          autoFocus
        />
        {errorMessage && (
          <p className="text-red-600 mt-1 leading-5.5 w-full">{errorMessage}</p>
        )}
      </div>
      <Button
        label="Submit code"
        outlined
        severity="secondary"
        disabled={isLoadingValidateOtp}
        loading={isLoadingValidateOtp}
        onClick={() => onSubmitCode(code)}
        className="w-full mt-3 max-w-60"
      />
      <Button
        label={
          canSendAgain
            ? "Send code again"
            : `Resend in ${resendCountdown} seconds`
        }
        link
        disabled={!canSendAgain || isLoadingGenerateOtp}
        onClick={onSendAgain}
        className="text-base"
      />
    </div>
  );
}
