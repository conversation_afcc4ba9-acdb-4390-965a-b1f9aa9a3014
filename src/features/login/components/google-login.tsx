import IconGoogle from "@/common/components/icons/google";
import useThrottle from "@/common/hooks/use-throttle";
import { useGoogleOneTapLogin } from "@react-oauth/google";
import { But<PERSON> } from "primereact/button";
import { useCallback } from "react";
import useGoogleLogin from "@/features/user/hooks/use-google-login";
import { useRouter } from "next/navigation";
import { useAtomValue } from "jotai";
import { isNativeBridgeAtom } from "@/common/store/nativebridge";
import * as nativebridge from "@nrk/nativebridge";
import { OutgoingJSBridgeEvents } from "@/common/types/js-bridge-events";
import { jwtDecode } from "jwt-decode";
import useToast from "@/common/hooks/use-toast";
import { LAST_LOGGED_IN_GMAIL_KEY } from "@/common/constants/local-storage-keys";

export const GoogleLogin = ({ className }: { className?: string }) => {
  const router = useRouter();
  const isNativeBridge = useAtomValue(isNativeBridgeAtom);

  const { showErrorToast } = useToast();

  const { generateAuthUrl } = useGoogleLogin({
    ux_mode: "redirect",
    flow: "auth-code",
    prompt: "consent",
  });

  useGoogleOneTapLogin({
    use_fedcm_for_prompt: true,
    cancel_on_tap_outside: false,
    onSuccess: ({ credential }) => {
      if (credential) {
        try {
          const { email } = jwtDecode<{ email: string }>(credential);
          localStorage.setItem(LAST_LOGGED_IN_GMAIL_KEY, email);
          const url = generateAuthUrl({ loginHint: email });
          router.push(url);
        } catch (e) {
          showErrorToast("Google login failed");
        }
      }
    },
    onError: () => showErrorToast("Google login failed"),
  });

  const handleOnGoogleLogin = useCallback(() => {
    if (!isNativeBridge) {
      const lastLoggedInEmail = localStorage.getItem(LAST_LOGGED_IN_GMAIL_KEY);
      const url = generateAuthUrl(
        !!lastLoggedInEmail ? { loginHint: lastLoggedInEmail } : {}
      );
      router.push(url);
      return;
    }

    try {
      nativebridge.emit(OutgoingJSBridgeEvents.SIGN_IN);
    } catch (e) {}
  }, [generateAuthUrl, isNativeBridge, router]);

  const throttledGoogleLoginHandler = useThrottle(handleOnGoogleLogin);
  return (
    <Button onClick={throttledGoogleLoginHandler} className={className}>
      <IconGoogle className="size-6" />
      Sign in with Google
    </Button>
  );
};
