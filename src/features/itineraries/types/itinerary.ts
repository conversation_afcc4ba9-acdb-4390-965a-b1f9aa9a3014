import { Flight } from "@/features/chat/types/api";
import { HotelRoomData } from "@/features/chat/types/hotels";
import { RecommendationReasons } from "@/features/chat/types/recommendationReasons";
import { Trip } from "@/features/chat/types/trip";

export enum BookingStatuses {
  BOOKED = "booked",
  CANCELLED = "cancelled",
  UNBOOKED = "unbooked",
}

export type Accommodation = {
  amenities: string[];
  highlight: string;
  hotel: string;
  id: string;
  img: {
    alt: string;
    src: string;
  };
  checkIn?: string;
  checkOut?: string;
  check_in_time?: string;
  check_out_time?: string;
  check_in_date?: string;
  check_out_date?: string;
  city?: string;
  mapMarker?: {
    address?: string;
    coordinates: {
      lng: string;
      lat: string;
    };
    text: string;
  };
  order_number?: string;
  phone?: string;
  pincode_number?: string;
  price: string | null;
  property_id?: number;
  rating: string;
  recommendationReasons: RecommendationReasons;
  reservation_number?: string;
  room: HotelRoomData;
  status: BookingStatuses;
  website?: string;
  manageBookingURL?: string;
  change_accommodation_action?: string;
  cancel_accommodation_action?: string;
};

// TODO: change flight and accommodations to optional
type Itinerary = {
  flight: {
    airline_confirmation_number: string;
    confirmation_id: string;
    /** @deprecated Soon to be removed. Use old_legs first. */
    oldOutbound?: Flight;
    /** @deprecated Soon to be removed. Use old_legs first. */
    oldReturn?: Flight;
    /** @deprecated Soon to be removed. Use legs first. */
    outbound?: Flight;
    /** @deprecated Soon to be removed. Use legs first. */
    return?: Flight | null;
    legs?: Flight[];
    old_legs?: Flight[];
    status?: BookingStatuses;
    change_flight_leg_action?: string;
    cancel_flight_leg_action?: string;

    // Mock data from here
    price?: string;
    price_summary?: {
      base: {
        amount: number;
        currency: string;
      };
      tax: {
        amount: number;
        currency: string;
      };
      total: {
        amount: number;
        currency: string;
      };
      total_seat_price?: {
        amount: number;
        currency: string;
      };
    };
  };
  accommodations: Accommodation[];
};

export enum ItineraryTab {
  Flights,
  Hotels,
  TravelContext,
}

export type FlightFormattedTimestamp = {
  date: string;
  time: string;
};

export type TripEvent = {
  tripData: Trip;
  accommodation?: Accommodation;
  flight?: Flight;
  isPast?: boolean;
};

export default Itinerary;
