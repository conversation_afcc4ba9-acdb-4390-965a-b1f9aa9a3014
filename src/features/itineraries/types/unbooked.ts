import { Flight } from "@/features/chat/types/api";
import { CancellationType } from "@/features/chat/types/hotels";

export type UnbookedFlightType = {
  outbound: Flight;
  return: Flight | null;
  legs?: Flight[];
  price_summary?: {
    base: {
      amount: number;
      currency: string;
    };
    tax: {
      amount: number;
      currency: string;
    };
    total: {
      amount: number;
      currency: string;
    };
  };
};

export type UnbookedHotelType = {
  accommodations: [
    {
      amenities: string[];
      check_in_date: string;
      check_in_time: string;
      check_out_date: string;
      check_out_time: string;
      highlight: string;
      hotel: string;
      hotel_class: string;
      id: string;
      img: {
        alt: string;
        src: string;
      };
      mapMarker: {
        coordinates: {
          lat: string;
          lng: string;
        };
        address: string;
        text: string;
      };
      phone?: string;
      photos: string[];
      price: null;
      rating: string;
      rating_description: string;
      recommendationReasons: {
        choice_characteristic: string;
        preference_alignment_reason: string;
        distinction_reason_one: string;
        distinction_reason_two: string;
      };
      rooms: {
        id: number;
        image: {
          alt: string;
          src: string;
        };
        room_photo: string;
        price: number;
        pricePerNight: number;
        taxAndFees: number;
        priceExcludingFees: number;
        no_nights: number;
        option_title: string;
        options: [];
        cancellation_policy: {
          type: CancellationType;
          display_policy: string;
        };
        payment_policy: {
          supported_timings: string[];
          display_label: string;
          policy: string;
        };
        within_policy?: boolean;
        within_or_out_policy_reason?: string;
        amenities: string[];
        action: string;
        recommendation_reason: string;
      }[];
      within_policy: boolean | null;
      within_or_out_policy_reason: boolean | null;
    }
  ];
};
