import { atom } from "jotai";
import { ItineraryTab } from "../types/itinerary";
import { FlightStop } from "@/features/chat/types/api";

export const activeTabAtom = atom(ItineraryTab.Flights);
// Value can have accommodation id or flight segment origin code
export const focusedTripEventIdAtom = atom<string>();

export const updatedFlightPopupAtom = atom<
  | {
      flight: FlightStop[];
      oldFlight?: FlightStop[];
    }
  | undefined
>(undefined);
