import dayjs from "dayjs";
import { Accommodation, BookingStatuses } from "../types/itinerary";
import { UnbookedHotelType } from "../types/unbooked";

export const mapUnbookedHotelToItinerary = (
  hotelData: UnbookedHotelType
): Accommodation[] =>
  hotelData.accommodations.map((item) => ({
    ...item,
    checkIn: dayjs(`${item.check_in_date} ${item.check_in_time}`).format(
      "M/D [after] ha"
    ),
    checkOut: dayjs(`${item.check_out_date} ${item.check_out_time}`).format(
      "M/D [before] ha"
    ),
    room: {
      ...item?.rooms?.[0],
      id: item?.rooms?.[0]?.id.toString(),
    },

    status: BookingStatuses.UNBOOKED,
  }));
