import { Accommodation } from "../types/itinerary";
import dayjs from "dayjs";
import { TripEvent } from "../types/itinerary";
import { Flight } from "@/features/chat/types/api";
import { dateWithTimezone } from "@/common/utils";

const getAccommodationDate = ({
  check_in_date,
  check_in_time,
}: Accommodation) => {
  return dayjs(`${check_in_date} ${check_in_time}`);
};

const getFlightDate = ({ flight_segments }: Flight) => {
  const firstSegment = flight_segments[0].flight_stops[0];
  return dateWithTimezone(
    firstSegment.departure,
    firstSegment.departure_timezone
  );
};

export const sortByDate = (a: TripEvent, b: TripEvent) => {
  let aDate = dayjs();
  let bDate = dayjs();
  if (a.accommodation) {
    aDate = getAccommodationDate(a.accommodation);
  }
  if (b.accommodation) {
    bDate = getAccommodationDate(b.accommodation);
  }
  if (a.flight) {
    aDate = getFlightDate(a.flight);
  }
  if (b.flight) {
    bDate = getFlightDate(b.flight);
  }
  return aDate.isBefore(bDate) ? -1 : aDate.isAfter(bDate) ? 1 : 0;
};
