import Link from "next/link";
import { TripEvent } from "../../types/itinerary";
import { ROUTES } from "@/common/constants/router.constants";
import { WithEllipsis } from "@/common/components/with-ellipsis";
import { airportCityMapping } from "../../utils/airport-city-mapping";

export function TripEventDestinationBody({
  tripData,
  accommodation,
  flight,
}: TripEvent) {
  if (!accommodation && !flight) {
    return null;
  }
  const eventId =
    accommodation?.id ||
    `${flight?.flight_segments[0].destination_code}-${flight?.id}`;
  let title = tripData.title;
  const lastSegment =
    flight?.flight_segments?.[flight.flight_segments.length - 1];
  const flightDestinationCode = lastSegment?.destination_code;
  if (!!flightDestinationCode) {
    title =
      airportCityMapping?.[flightDestinationCode] || flightDestinationCode;
  }

  if (!!accommodation?.city) {
    title = accommodation.city;
  }

  return (
    <Link
      href={`${ROUTES.TRIPS}/${tripData.id}`}
      className="max-w-48 text-blue-500 hover:underline"
      onClick={(event) => event.stopPropagation()}
    >
      <WithEllipsis id={eventId}>{title}</WithEllipsis>
    </Link>
  );
}
