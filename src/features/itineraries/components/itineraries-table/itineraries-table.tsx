import ItinerariesSectionTable from "./itineraries-section-table";
import React, { useEffect } from "react";
import { TabView } from "primereact/tabview";
import { TabPanel } from "primereact/tabview";
import { useTrips } from "@/features/itineraries/hooks/use-trips";
import { ChangeReservationContact } from "../change-reservation-contact";
import arrayHasElements from "@/common/utils/array-has-elements";
import { useGetTripDetails } from "@/common/hooks/api";
import { useAtomValue } from "jotai";
import { tripDetailsAtom } from "@/features/chat/store/current-trip";
import clsx from "clsx";
import { isNativeBridgeAtom } from "@/common/store/nativebridge";

export default function ItinerariesTable() {
  const tripDetails = useAtomValue(tripDetailsAtom);
  const { getTripDetails } = useGetTripDetails();
  const { bookedTripsEvents, cancelledTripsEvents, pastTripsEvents } =
    useTrips();

  const isNativeBridge = useAtomValue(isNativeBridgeAtom);

  // Keep track of trips to prefetch in the background.
  // Right now we only prefetch the first 3 trips that have flight booked.
  useEffect(() => {
    if (!bookedTripsEvents) return;

    // Get unique trip IDs that have flights and haven't been prefetched
    const prefetchTripIds = Array.from(
      new Set(
        bookedTripsEvents.reduce((acc, event) => {
          const tripId = event.tripData.id;
          const hasFlightBooking =
            event.flight && event.tripData.itinerary?.flight;
          if (tripId && !tripDetails?.[tripId] && hasFlightBooking) {
            acc.push(event.tripData.id);
          }
          return acc;
        }, [] as number[])
      )
    ).slice(0, 3); // Only prefetch first 3 trips

    prefetchTripIds.forEach(getTripDetails);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bookedTripsEvents, getTripDetails]);

  const tabPanelContentClassName = clsx(
    "h-[calc(100vh-8rem)] overflow-y-auto",
    {
      "h-[calc(100vh-10rem)]": isNativeBridge,
    }
  );

  return (
    <div className="flex flex-col gap-4 mx-4 lg:mx-6 lg:mt-2">
      <TabView>
        <TabPanel header="Booked" contentClassName={tabPanelContentClassName}>
          <ItinerariesSectionTable tripsEvents={bookedTripsEvents} />
          {arrayHasElements(bookedTripsEvents) && (
            <ChangeReservationContact
              className="text-xs leading-tight space-y-3 italic mt-3 lg:text-sm /
            lg:leading-none lg:space-y-2"
            />
          )}
        </TabPanel>
        <TabPanel header="Past" contentClassName={tabPanelContentClassName}>
          <ItinerariesSectionTable tripsEvents={pastTripsEvents} />
        </TabPanel>
        <TabPanel
          header="Cancelled"
          contentClassName={tabPanelContentClassName}
        >
          <ItinerariesSectionTable tripsEvents={cancelledTripsEvents} />
        </TabPanel>
      </TabView>
    </div>
  );
}
