import {
  formatFlightNumbers,
  formatFlightTimes,
  getArrivalDays,
} from "@/common/utils/flight";
import { TripEvent } from "../../types/itinerary";
import { useMemo } from "react";

export function TripEventDetailsBody({ accommodation, flight }: TripEvent) {
  const flightSegment = flight?.flight_segments[0];

  const { route, flightNumbers, timeDisplay } = useMemo(() => {
    if (!flightSegment) {
      return { route: "", flightNumbers: "", timeDisplay: null };
    }

    const route = flightSegment.flight_stops
      .map(({ origin_code, destination_code }, index) =>
        index === 0 ? `${origin_code} - ${destination_code}` : destination_code
      )
      .join(" - ");

    const flightNumbers = formatFlightNumbers(flight);
    const { departureDate, arrivalDate, departureTime, arrivalTime } =
      formatFlightTimes(flight);

    let arrivalDays = "";
    const daysDiff = getArrivalDays(departureDate, arrivalDate);
    if (daysDiff > 0) {
      arrivalDays = ` (+${daysDiff})`;
    }

    const timeDisplay = (
      <span>
        {`${departureTime} - ${arrivalTime}`}
        {arrivalDays && (
          <sup className="text-red-650 text-2xs font-medium leading-6">
            {arrivalDays}
          </sup>
        )}
      </span>
    );

    return { route, flightNumbers, timeDisplay };
  }, [flight, flightSegment]);

  if (!accommodation && !flight) {
    return null;
  }

  if (accommodation) {
    return accommodation.hotel;
  }

  if (!flightSegment) {
    return null;
  }

  return (
    <div>
      <span className="flex lg:hidden items-center gap-x-2 flex-wrap">
        {route} <span>•</span> {flightNumbers} <span>•</span>
        {timeDisplay}
      </span>
      <span className="hidden lg:inline">
        {`${route} | ${flightNumbers} | `}
        {timeDisplay}
      </span>
    </div>
  );
}
