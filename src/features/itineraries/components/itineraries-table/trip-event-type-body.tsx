import clsx from "clsx";
import { TripEvent } from "../../types/itinerary";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

export function TripEventTypeBody({ accommodation, flight }: TripEvent) {
  if (!accommodation && !flight) {
    return null;
  }
  return (
    <div className="size-6 rounded-md p-1.5 bg-gray-475 flex items-center justify-center">
      <FontAwesomeIcon
        icon={accommodation ? "bed" : "plane"}
        className={clsx("text-2xs lg:text-xs text-gray-900/70", {
          "-rotate-45": flight,
        })}
      />
    </div>
  );
}
