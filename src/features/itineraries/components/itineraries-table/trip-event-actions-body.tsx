import PopupMenu from "@/common/components/popup-menu";
import { BookingStatuses, TripEvent } from "../../types/itinerary";
import { Button } from "primereact/button";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { MouseEventHandler, useMemo, useState } from "react";
import { ROUTES } from "@/common/constants";
import { useRouter } from "next/navigation";
import { Sidebar } from "primereact/sidebar";
import { Menu } from "primereact/menu";
import clsx from "clsx";
import { isNativeBridgeAtom } from "@/common/store/nativebridge";
import { useAtomValue } from "jotai";
import { useWebSocketContext } from "@/features/chat/context/websocket-context";

export function TripEventActionsBody({
  flight,
  accommodation,
  tripData,
  isPast,
}: TripEvent) {
  const router = useRouter();
  const { id } = tripData;
  const isNativeBridge = useAtomValue(isNativeBridgeAtom);
  const { sendMessageToTrip } = useWebSocketContext();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const isCancelled = useMemo(() => {
    if (accommodation) {
      return accommodation.status === BookingStatuses.CANCELLED;
    }
    if (flight) {
      return flight.cancelled;
    }
    return false;
  }, [accommodation, flight]);

  const changeTripAction = useMemo(() => {
    if (accommodation) {
      return accommodation.change_accommodation_action;
    }
    if (flight) {
      return flight.change_flight_leg_action;
    }
    return null;
  }, [accommodation, flight]);

  const cancelTripAction = useMemo(() => {
    if (accommodation) {
      return accommodation.cancel_accommodation_action;
    }
    if (flight) {
      return flight.cancel_flight_leg_action;
    }
    return null;
  }, [accommodation, flight]);

  const menuItems = useMemo(
    () => [
      {
        icon: <FontAwesomeIcon icon={["far", "receipt"]} className="w-6" />,
        label: "View receipt",
        visible: !isCancelled,
      },
      {
        icon: <FontAwesomeIcon icon={["far", "message"]} className="w-6" />,
        label: "View conversation",
        command: () => router.push(`${ROUTES.TRIPS}/${id}`),
      },
      {
        icon: <FontAwesomeIcon icon={["far", "pen"]} className="w-6" />,
        label: "Change trip",
        visible: !isCancelled && !isPast && !!changeTripAction,
        command: () =>
          changeTripAction && sendMessageToTrip(id, changeTripAction),
      },
      {
        icon: <FontAwesomeIcon icon={["far", "ban"]} className="w-6" />,
        label: "Cancel trip",
        className: "text-red-500",
        visible: !isCancelled && !isPast && !!cancelTripAction,
        command: () =>
          cancelTripAction && sendMessageToTrip(id, cancelTripAction),
      },
    ],
    [
      isCancelled,
      isPast,
      changeTripAction,
      cancelTripAction,
      router,
      id,
      sendMessageToTrip,
    ]
  );

  const openMobileMenu: MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    setIsMobileMenuOpen(true);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      <PopupMenu
        containerClassName="w-fit mx-auto hidden md:block"
        model={menuItems}
        onHide={() => setIsMobileMenuOpen(false)}
      >
        <Button text rounded className="p-0 size-6">
          <FontAwesomeIcon icon={["far", "ellipsis-vertical"]} height={24} />
        </Button>
      </PopupMenu>

      <div className="md:hidden">
        <Button text rounded className="p-0 size-6" onClick={openMobileMenu}>
          <FontAwesomeIcon icon={["far", "ellipsis-vertical"]} height={24} />
        </Button>
        <Sidebar
          visible={isMobileMenuOpen}
          position="bottom"
          onHide={closeMobileMenu}
          showCloseIcon={false}
          pt={{
            content: {
              className: clsx("p-0", {
                "pb-6.5": isNativeBridge,
              }),
            },
          }}
        >
          <Menu model={menuItems} />
        </Sidebar>
      </div>
    </>
  );
}
