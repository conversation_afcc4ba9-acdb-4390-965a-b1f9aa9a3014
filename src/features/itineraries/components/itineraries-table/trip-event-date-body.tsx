import dayjs from "dayjs";
import { TripEvent } from "../../types/itinerary";
import { WithEllipsis } from "@/common/components/with-ellipsis";
import { dateWithTimezone } from "@/common/utils";

const CURRENT_YEAR = dayjs().year();

const getDisplayDate = (startDate: dayjs.Dayjs, endDate?: dayjs.Dayjs) => {
  if (!startDate.isValid() || (endDate && !endDate.isValid())) {
    return "";
  }

  const spansYears = endDate && startDate.year() !== endDate.year();
  if (spansYears) {
    return `${startDate.format("MMM D, YYYY")} - ${endDate.format(
      "MMM D, YYYY"
    )}`;
  }

  const isCurrentYear =
    startDate.year() === CURRENT_YEAR &&
    (!endDate || endDate.year() === CURRENT_YEAR);
  const spansMonths = endDate && startDate.month() !== endDate.month();

  if (isCurrentYear) {
    if (spansMonths) {
      return `${startDate.format("MMM D")} - ${endDate.format("MMM D")}`;
    }
    return `${startDate.format("MMM D")}${
      endDate ? `-${endDate.format("D")}` : ""
    }`;
  }

  return startDate.format("MMM D");
};

const parseDateTimeofCheckInOutString = (input: string): Date | null => {
  const regex =
    /(\d{1,2})\/(\d{1,2})\s+(after|before)\s+(\d{1,2})(?::(\d{2}))?(AM|PM)/i;
  const match = input.match(regex);

  if (!match) return null;

  const [, monthStr, dayStr, direction, hourStr, minuteStr, meridian] = match;
  const month = parseInt(monthStr, 10) - 1; // JS Date months are 0-indexed
  const day = parseInt(dayStr, 10);
  let hour = parseInt(hourStr, 10);
  const minutes = minuteStr ? parseInt(minuteStr, 10) : 0;

  if (meridian.toUpperCase() === "PM" && hour !== 12) hour += 12;
  if (meridian.toUpperCase() === "AM" && hour === 12) hour = 0;

  const now = new Date();
  const year = now.getFullYear();

  let date = new Date(year, month, day, hour, minutes);

  return date;
};

export function TripEventDateBody({ accommodation, flight }: TripEvent) {
  if (!accommodation && !flight) {
    return null;
  }

  if (accommodation) {
    let startDate = dayjs.utc(accommodation.check_in_date);
    let endDate = dayjs.utc(accommodation.check_out_date);
    if ((!startDate || !startDate.isValid()) && accommodation.checkIn) {
      const parsedDate = parseDateTimeofCheckInOutString(accommodation.checkIn);
      if (parsedDate) {
        startDate = dayjs.utc(parsedDate);
      }
    }
    if ((!endDate || !endDate.isValid()) && accommodation.checkOut) {
      const parsedDate = parseDateTimeofCheckInOutString(
        accommodation.checkOut
      );
      if (parsedDate) {
        endDate = dayjs.utc(parsedDate);
      }
    }
    const displayDate = getDisplayDate(startDate, endDate);

    return displayDate ? (
      <WithEllipsis id={accommodation.id}>{displayDate}</WithEllipsis>
    ) : null;
  }

  if (flight) {
    const firstSegment = flight.flight_segments[0].flight_stops[0];
    const startDate = dateWithTimezone(
      firstSegment.departure,
      firstSegment.departure_timezone
    );
    const displayDate = getDisplayDate(startDate);

    return displayDate ? (
      <WithEllipsis id={flight.id}>{displayDate}</WithEllipsis>
    ) : null;
  }

  return null;
}
