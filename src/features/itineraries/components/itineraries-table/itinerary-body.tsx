import { But<PERSON> } from "primereact/button";
import { TripEvent } from "../../types/itinerary";
import { useOpenTripDetails } from "../../hooks/use-open-trip-details";

export function ItineraryBody({ accommodation, flight, tripData }: TripEvent) {
  const openDetails = useOpenTripDetails();

  if (!accommodation && !flight) {
    return null;
  }

  return (
    <Button
      label="View"
      link
      onClick={() => openDetails({ accommodation, flight, tripData })}
      className="p-0 text-base ml-auto"
    />
  );
}
