import { But<PERSON> } from "primereact/button";
import { TripEvent } from "../../types/itinerary";
import { MouseEventHandler, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

export function ConfirmationCodeDisplay({ code }: { code: string }) {
  const [showCheckmark, setShowCheckmark] = useState(false);

  if (!code) return null;

  const handleCopy: MouseEventHandler<HTMLButtonElement> = (event) => {
    event.stopPropagation();
    navigator.clipboard.writeText(code);
    setShowCheckmark(true);
    setTimeout(() => setShowCheckmark(false), 3000);
  };

  return (
    <div className="inline-flex items-center gap-2 lg:gap-1">
      {code}
      <Button
        link
        rounded
        onClick={handleCopy}
        className="p-0 lg:p-1 hover:no-underline gap-1"
      >
        <span className="inline lg:hidden text-xs leading-6 font-medium">
          {showCheckmark ? "Copied" : "Copy"}
        </span>
        <FontAwesomeIcon icon={showCheckmark ? "check" : ["far", "copy"]} />
      </Button>
    </div>
  );
}

export function TripEventConfirmationCodeBody({
  accommodation,
  flight,
  tripData,
}: TripEvent) {
  if (!accommodation && !flight) {
    return null;
  }

  if (accommodation?.reservation_number) {
    return <ConfirmationCodeDisplay code={accommodation.reservation_number} />;
  }

  const flightConfirmationId =
    tripData.itinerary?.flight?.airline_confirmation_number;
  if (!flightConfirmationId) return null;

  return <ConfirmationCodeDisplay code={flightConfirmationId} />;
}
