import { Column } from "primereact/column";
import { DataTable } from "primereact/datatable";
import React from "react";
import { TripEventDateBody } from "./trip-event-date-body";
import { ItineraryBody } from "./itinerary-body";
import { TripEvent } from "../../types/itinerary";
import { TripEventTypeBody } from "./trip-event-type-body";
import { TripEventDestinationBody } from "./trip-event-destination-body";
import { TripEventDetailsBody } from "./trip-event-details-body";
import { TripEventConfirmationCodeBody } from "./trip-event-confirmation-code-body";
import { ItinerariesCards } from "../itineraries-cards";
import arrayHasElements from "@/common/utils/array-has-elements";
import { EmptyMessage } from "../empty-message";
import { TripEventActionsBody } from "./trip-event-actions-body";

type ItinerariesSectionTableProps = {
  tripsEvents: TripEvent[];
};

export default function ItinerariesSectionTable({
  tripsEvents,
}: ItinerariesSectionTableProps) {
  if (!arrayHasElements(tripsEvents)) {
    return <EmptyMessage />;
  }

  return (
    <>
      <div className="lg:hidden">
        <ItinerariesCards tripsEvents={tripsEvents} />
      </div>
      <div className="hidden lg:block max-w-6xl">
        <DataTable
          value={tripsEvents}
          scrollable
          scrollHeight="calc(100vh - 13rem)"
        >
          <Column body={TripEventTypeBody} className="w-12" />
          <Column header="Date" body={TripEventDateBody} className="max-w-36" />
          <Column
            header="Destination"
            body={TripEventDestinationBody}
            className="max-w-48"
          />
          <Column header="Trip details" body={TripEventDetailsBody} />
          <Column
            header="Confirmation code"
            body={TripEventConfirmationCodeBody}
            className="w-44"
          />
          <Column header="Itinerary" body={ItineraryBody} align="right" />
          <Column header="Actions" body={TripEventActionsBody} align="center" />
        </DataTable>
      </div>
    </>
  );
}
