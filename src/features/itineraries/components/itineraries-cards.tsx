import React from "react";
import { TripEvent } from "../types/itinerary";
import { TripEventTypeBody } from "./itineraries-table/trip-event-type-body";
import { TripEventDateBody } from "./itineraries-table/trip-event-date-body";
import { TripEventDestinationBody } from "./itineraries-table/trip-event-destination-body";
import { TripEventDetailsBody } from "./itineraries-table/trip-event-details-body";
import { TripEventConfirmationCodeBody } from "./itineraries-table/trip-event-confirmation-code-body";
import { useOpenTripDetails } from "../hooks/use-open-trip-details";
import { TripEventActionsBody } from "./itineraries-table/trip-event-actions-body";

type ItinerariesCardsProps = {
  tripsEvents: TripEvent[];
};

export function ItinerariesCards({ tripsEvents }: ItinerariesCardsProps) {
  const openDetails = useOpenTripDetails();

  return (
    <div className="flex flex-col gap-3">
      {tripsEvents.map((tripEvent, index) => (
        <div
          key={index}
          className="flex bg-gray-100 rounded-lg py-3 pl-3 dark:bg-white/5"
        >
          <div
            className="flex gap-x-2 items-center flex-wrap text-sm leading-6 flex-1 cursor-pointer"
            onClick={() => openDetails(tripEvent)}
          >
            <TripEventTypeBody {...tripEvent} />
            <div className="font-medium">
              <TripEventDateBody {...tripEvent} />
            </div>
            <span>•</span>
            <div className="font-medium">
              <TripEventDestinationBody {...tripEvent} />
            </div>
            <div className="text-neutral-500 w-full">
              <TripEventDetailsBody {...tripEvent} />
            </div>
            <div className="text-neutral-500">
              <TripEventConfirmationCodeBody {...tripEvent} />
            </div>
          </div>
          <TripEventActionsBody {...tripEvent} />
        </div>
      ))}
    </div>
  );
}
