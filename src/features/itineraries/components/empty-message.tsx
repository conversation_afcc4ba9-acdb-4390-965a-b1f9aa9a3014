import { useCreateTrip } from "@/common/hooks/api";
import { Button } from "primereact/button";

export function EmptyMessage() {
  const { createTrip } = useCreateTrip();

  return (
    <div className="flex flex-col gap-4 items-center font-semibold pt-5.5 lg:pt-10">
      No upcoming reservations yet.
      <Button
        label="Create new trip?"
        link
        className="font-semibold text-base"
        onClick={() => createTrip()}
      />
    </div>
  );
}
