import { useMemo } from "react";
import { BookingStatuses } from "@/features/itineraries/types/itinerary";
import { useTripsList } from "@/common/hooks/api";
import { TripEvent } from "@/features/itineraries/types/itinerary";
import { Trip } from "@/features/chat/types/trip";
import { foreignTripsAtom } from "@/features/admin/store/trips";
import { useAtomValue } from "jotai";
import { isUserSelectedAtom } from "@/features/admin/store/foreign-user";
import { sortByDate } from "../utils/date";
import arrayHasElements from "@/common/utils/array-has-elements";

export function useTrips() {
  const isUserSelected = useAtomValue(isUserSelectedAtom);
  const foreignTrips = useAtomValue(foreignTripsAtom);

  const { data, error, mutate, isLoading } = useTripsList();
  const tripsData = isUserSelected ? foreignTrips : data;

  const processedTrips = useMemo(() => {
    const bookedTripsEvents: TripEvent[] = [];
    const pastTripsEvents: TripEvent[] = [];
    const cancelledTripsEvents: TripEvent[] = [];

    if (!tripsData)
      return { bookedTripsEvents, cancelledTripsEvents, pastTripsEvents };

    const parseTrip = (trip: Trip, isPast = false) => {
      const { itinerary } = trip;
      const { accommodations, flight } = itinerary || {};
      if (accommodations?.length) {
        accommodations.forEach((accommodation) => {
          const accommodationEvent = {
            tripData: trip,
            accommodation: accommodation,
            isPast,
          };

          if (accommodation.status === BookingStatuses.CANCELLED) {
            cancelledTripsEvents.push(accommodationEvent);
          } else if (isPast) {
            pastTripsEvents.push(accommodationEvent);
          } else {
            bookedTripsEvents.push(accommodationEvent);
          }
        });
      }

      if (arrayHasElements(flight?.legs)) {
        flight.legs.forEach((leg) => {
          const flightData = {
            tripData: trip,
            flight: leg,
            isPast,
          };

          if (leg.cancelled) {
            cancelledTripsEvents.push(flightData);
          } else if (isPast) {
            pastTripsEvents.push(flightData);
          } else {
            bookedTripsEvents.push(flightData);
          }
        });
        return;
      }

      if (flight?.outbound) {
        const flightData = {
          tripData: trip,
          flight: flight.outbound,
          isPast,
        };
        if (flight.outbound.cancelled) {
          cancelledTripsEvents.push(flightData);
        } else if (isPast) {
          pastTripsEvents.push(flightData);
        } else {
          bookedTripsEvents.push(flightData);
        }
      }

      if (flight?.return) {
        const flightData = {
          tripData: trip,
          flight: flight.return,
          isPast,
        };
        if (flight.return.cancelled) {
          cancelledTripsEvents.push(flightData);
        } else if (isPast) {
          pastTripsEvents.push(flightData);
        } else {
          bookedTripsEvents.push(flightData);
        }
      }
    };

    tripsData.booked.forEach((trip) => parseTrip(trip));
    tripsData.past.forEach((trip) => parseTrip(trip, true));

    return {
      bookedTripsEvents: bookedTripsEvents.sort(sortByDate),
      cancelledTripsEvents: cancelledTripsEvents.sort(sortByDate).reverse(),
      pastTripsEvents: pastTripsEvents.sort(sortByDate).reverse(),
    };
  }, [tripsData]);

  return {
    error,
    mutate,
    isLoading,
    ...processedTrips,
  };
}
