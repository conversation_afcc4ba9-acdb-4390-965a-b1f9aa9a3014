import { useAtom, useSet<PERSON>tom } from "jotai";
import {
  currentTripAtom,
  tripDetailsAtom,
} from "@/features/chat/store/current-trip";
import { activeTabAtom, focusedTripEventIdAtom } from "../store/itinerary";
import { ItineraryTab, TripEvent } from "../types/itinerary";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import RightSidebars from "@/features/chat/types/right-sidebars";
import { TripDetails } from "@/features/chat/types/trip";
import { useGetTripDetails } from "@/common/hooks/api";
import { useCallback } from "react";

export function useOpenTripDetails() {
  const setCurrentTrip = useSetAtom(currentTripAtom);
  const setActiveTab = useSetAtom(activeTabAtom);
  const setFocusedTripEventId = useSetAtom(focusedTripEventIdAtom);
  const [tripDetails, setTripDetails] = useAtom(tripDetailsAtom);
  const { getTripDetails } = useGetTripDetails();
  const { switchTo } = useRightSidebarSwitch();

  const openDetails = useCallback(
    (tripEvent: TripEvent) => {
      if (tripEvent.accommodation) {
        setActiveTab(ItineraryTab.Hotels);
        setFocusedTripEventId(tripEvent.accommodation.id);
      } else {
        setActiveTab(ItineraryTab.Flights);
        setFocusedTripEventId(
          tripEvent.flight?.flight_segments[0].flight_stops[0].origin_code
        );
      }
      // Set the current trip so itinerary sidebar can show the correct trip
      setCurrentTrip(tripEvent.tripData.id);
      if (!tripDetails?.[tripEvent.tripData.id]) {
        // Use what we have for now from /itineraries endpoint first...
        setTripDetails({
          ...tripDetails,
          [tripEvent.tripData.id]: tripEvent.tripData,
        } as TripDetails);
        // ... and fetch the rest of the data from /trips endpoint
        getTripDetails(tripEvent.tripData.id);
      }
      switchTo(RightSidebars.ITINERARY);
    },
    [
      getTripDetails,
      setActiveTab,
      setCurrentTrip,
      setFocusedTripEventId,
      setTripDetails,
      switchTo,
      tripDetails,
    ]
  );

  return openDetails;
}
