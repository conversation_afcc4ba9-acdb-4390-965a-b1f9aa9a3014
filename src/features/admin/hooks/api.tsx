import { useCallback } from "react";
import useS<PERSON>, { Key } from "swr";
import useSWRMutation from "swr/mutation";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { foreignTripsAtom } from "@/features/admin/store/trips";
import { chat<PERSON><PERSON><PERSON><PERSON><PERSON>, otto<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/features/chat/store/chat";
import useToast from "@/common/hooks/use-toast";
import { RangeObject } from "../types/trips";
import dayjs from "dayjs";
import { MessageSeverity } from "primereact/api";
import arrayHasElements from "@/common/utils/array-has-elements";
import {
  BookingStatsResponse,
  UserAllowEmailResponse,
  UserAllowListResponse,
  UserResponse,
  BookingListResponse,
} from "../types/api";
import { ApiPaths, ApiRestMethod } from "@/common/constants";
import { TripsList } from "@/features/chat/types/trip";
import { FetcherExtraArgs } from "@/common/types/api";
import { fetcher } from "@/common/api/fetcher";
import { downloadAsCSV } from "@/common/utils/csv-export";
import { SortOrder } from "primereact/datatable";
import { UserPreferences } from "@/features/user/types/user";
import { foreignUserAtom } from "../store/foreign-user";
import { TravelPolicyData } from "@/features/travel-policy/types/travel-policy";

export function useUsersList() {
  const { showErrorToast } = useToast();
  return useSWR<UserResponse[]>(ApiPaths.ADMIN_GET_USERS, {
    onError: (error) => showErrorToast(error, "Users list request failed."),
    revalidateOnFocus: false,
    revalidateOnMount: false,
  });
}

export function useGetUserTrips(options: {
  onSuccess?: (data: TripsList) => void;
}) {
  const { showErrorToast } = useToast();
  const setForeignTrips = useSetAtom(foreignTripsAtom);

  const { trigger } = useSWRMutation<
    TripsList,
    Error,
    Key,
    FetcherExtraArgs | undefined
  >(ApiPaths.ADMIN_GET_USERS_TRIPS, fetcher, {
    ...options,
    onSuccess: (data) => {
      setForeignTrips(data);
      options?.onSuccess?.(data);
    },
    onError: (error) => showErrorToast(error, "User trips request failed."),
  });

  return trigger;
}

export function useGetThreadHistory(basePath: string) {
  const setHistory = useSetAtom(chatHistoryAtom);
  const setOttoReplying = useSetAtom(ottoReplyingAtom);

  const { error, trigger } = useSWRMutation(basePath, fetcher, {
    onSuccess(data) {
      setHistory(data);
      setOttoReplying(false);
    },
    onError() {
      setOttoReplying(false);
    },
  });

  const getThreadHistory = useCallback(
    (queryString?: string) => trigger({ urlEnding: queryString }),
    [trigger]
  );

  return {
    error,
    getThreadHistory,
  };
}

export function useUsersAllowList({
  page,
  limit = 10,
  sortField,
  sortOrder,
}: {
  page: number;
  limit?: number;
  sortField?: string;
  sortOrder?: SortOrder;
}) {
  const { showErrorToast } = useToast();

  const commonQueryParams =
    sortField && sortOrder
      ? `&sortField=${sortField}&sortOrder=${sortOrder}`
      : "";

  const { data, isLoading } = useSWR<UserAllowListResponse>(
    `${ApiPaths.ADMIN_GET_USERS_ALLOW_LIST}?page=${page}&limit=${limit}${commonQueryParams}`,
    fetcher,
    {
      keepPreviousData: true,
      onError: (error) =>
        showErrorToast(error, "Users allow list request failed."),
    }
  );

  const { isMutating: isExporting, trigger: getAllUsers } = useSWRMutation(
    `${ApiPaths.ADMIN_GET_USERS_ALLOW_LIST}?page=1&limit=${data?.total}`,
    fetcher
  );

  const exportUsers = async () => {
    const { data } = await getAllUsers();
    downloadAsCSV(data, "otto-users.csv");
  };

  return { data, isLoading, isExporting, exportUsers };
}

function useShowAllowRequestWarnings() {
  const { showToast } = useToast();

  return (response: UserAllowEmailResponse) => {
    const { warnings } = response ?? {};
    const { already_exists, invalid_format } = warnings ?? {};

    if (arrayHasElements(already_exists) || arrayHasElements(invalid_format)) {
      showToast({
        life: 5000,
        content: "Some email addresses could not be added.",
        severity: MessageSeverity.WARN,
      });

      already_exists?.length &&
        showToast({
          life: 5000,
          content: (
            <>
              Existing:<p>{already_exists.join(",\n")}</p>
            </>
          ),
          severity: MessageSeverity.INFO,
        });

      invalid_format?.length &&
        showToast({
          life: 5000,
          content: (
            <>
              Invalid:<p>{invalid_format.join(",\n")}</p>
            </>
          ),
          severity: MessageSeverity.INFO,
        });
    }
  };
}

export function useAllowEmail({
  onSuccess,
}: {
  onSuccess?: (response: UserAllowEmailResponse) => void;
} = {}) {
  const { showToast } = useToast();
  const swr = useSWRMutation<UserAllowEmailResponse>(
    ApiPaths.ADMIN_ADD_TO_ALLOW_LIST,
    fetcher
  );
  const showWarnings = useShowAllowRequestWarnings();

  return {
    ...swr,
    trigger: (...args: any) =>
      swr
        .trigger(...args)
        .then((response) => {
          onSuccess?.(response);
          showWarnings(response);
        })
        .catch((error) => {
          showToast({
            life: 1000,
            content: "Allow request failed.",
            severity: MessageSeverity.ERROR,
          });
          showWarnings(error);
        }),
  };
}

const defaultDateRange = {
  end: dayjs().format("DD-MM-YYYY"),
  start: dayjs().subtract(30, "days").format("DD-MM-YYYY"),
};

export function useBookingsList(searchParams: Record<string, string>) {
  const { showErrorToast } = useToast();
  const queryString = new URLSearchParams(searchParams).toString();

  return useSWR<BookingListResponse>(
    `${ApiPaths.ADMIN_GET_TRIP_DATA}?${queryString}`,
    fetcher,
    {
      keepPreviousData: true,
      onError: (error) =>
        showErrorToast(error, "Bookings list request failed."),
    }
  );
}

export function useBookingsStatistics(
  dateRange: RangeObject = defaultDateRange
) {
  const { showErrorToast } = useToast();
  const { end, start } = dateRange;

  return useSWR<BookingStatsResponse>(
    `${ApiPaths.ADMIN_GET_TRIPS_STATS}?start=${start}&end=${end}`,
    fetcher,
    {
      keepPreviousData: true,
      onError: (error) =>
        showErrorToast(error, "Bookings stats request failed."),
    }
  );
}



export function useGetTripOwner(trip_id: number | string) {
  const { showErrorToast } = useToast();

  return useSWR<UserResponse>(
    `${ApiPaths.ADMIN_GET_THREAD_OWNER}?trip_id=${trip_id}`,
    fetcher,
    {
      onError: (error) =>
        showErrorToast(error, "Get trip owner request failed."),
    }
  );
}

export function useSelectedUserSettings() {
  const { showErrorToast } = useToast();
  const foreignUser = useAtomValue(foreignUserAtom);

  const { data } = useSWR<{
    userPreferences: UserPreferences;
    userCompanyTravelPolicy: TravelPolicyData;
  }>(
    foreignUser
      ? `${ApiPaths.ADMIN_USER_PROFILE}?user_id=${foreignUser.id}`
      : null,
    {
      onError: showErrorToast,
    }
  );

  return {
    preferences: data?.userPreferences,
    travelPolicy: data?.userCompanyTravelPolicy,
  };
}

export function useImportThread() {
  const { showErrorToast } = useToast();

  const importThreadRequest = (
    url: string,
    { arg }: { arg: { source_thread_id: number } }
  ) =>
    fetcher(url, {
      arg: {
        options: { method: ApiRestMethod.POST, body: JSON.stringify(arg) },
      },
    });

  const { error, isMutating, trigger } = useSWRMutation<
    { message: string; thread_id: number }, // Response type
    Error,
    Key,
    { source_thread_id: number } // Payload type
  >(ApiPaths.ADMIN_IMPORT_THREAD, importThreadRequest);

  const importThread = useCallback(
    async (params: { source_thread_id: number }) => {
      try {
        return await trigger(params);
      } catch (error) {
        showErrorToast(error, "Failed to import thread");
        throw error;
      }
    },
    [showErrorToast, trigger]
  );

  return { error, isMutating, importThread };
}

export function useFlightSearchEvaluation(searchParams: Record<string, string>) {
  const { showErrorToast } = useToast();
  const queryString = new URLSearchParams(searchParams).toString();

  return useSWR<{
    results: Array<{
      _id: string;
      trip_id: number;
      user_email: string;
      environment: string;
      created_at: string;
      human_messages: Array<{
        id: number;
        created_date: string;
        content: string;
        additional_kwargs: Record<string, any>;
      }>;
      travel_context: Record<string, any> | null;
      results: {
        main_results: { raw: any; method: string };
        two_stage_results: { raw: any; method: string };
        ranked_flights: { raw: any; method: string };
      };
      metadata: {
        travel_context_str: string;
        airline_codes: string[];
        flight_option_csv: string;
      };
    }>;
    total: number;
    page: number;
    total_pages: number;
  }>(
    `${ApiPaths.ADMIN_FLIGHT_SEARCH_EVALUATION}?${queryString}`,
    fetcher,
    {
      keepPreviousData: true,
      onError: (error) =>
        showErrorToast(error, "Flight search evaluation request failed."),
    }
  );
}
