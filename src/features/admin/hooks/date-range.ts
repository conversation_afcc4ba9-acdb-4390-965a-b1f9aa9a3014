import { usePathname, useSearchParams } from "next/navigation";

export default function useQueryParamsDateRange() {
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());

  if (!params.get("end") || !params.get("start")) {
    return null;
  }

  const rangeEndString = params.get("end") as string;
  const rangeStartString = params.get("start") as string;

  const [endDay, endMonth, endYear] = rangeEndString.split("-");
  const [startDay, startMonth, startYear] = rangeStartString.split("-");

  const rangeEnd = new Date([endMonth, endDay, endYear].join("-"));
  const rangeStart = new Date([startMonth, startDay, startYear].join("-"));

  return {
    calendarValues: [rangeStart, rangeEnd],
    queryParams: { end: rangeEndString, start: rangeStartString },
  };
}
