import { BookingStatuses } from "@/features/itineraries/types/itinerary";
import { UserRole } from "@/features/user/types/user";

export type UserResponse = {
  created_date: string;
  email: string;
  first_name: string;
  id: number;
  last_name: string;
  preferred_name: string;
  profile_picture: string;
  role: UserRole;
  tutorial_completed?: boolean | null;
  is_onboarding_completed?: boolean | null;
  has_company_travel_policy: boolean;
  has_user_preferences: boolean;
};

export type BookingRecord = {
  booking_type: string;
  booking_link: string;
  confirmation_number: string;
  end_date: string;
  name: string;
  thread_id: string;
  start_date: string;
  status: BookingStatuses;
  thread_title: string;
  title: string;
  user_email: string;
  user_id: number;
};

export type BookingListResponse = {
  data: BookingRecord[];
  total: number;
  page: number;
  total_pages: number;
};

export type UserAllowListResponse = {
  data: TableViewUser[];
  total: number;
  page: number;
  total_pages: number;
};

export type UserAllowEmailResponse = {
  success: boolean;
  valid_emails_added: string[];
  warnings: {
    invalid_format: string[];
    already_exists: string[];
  };
};
export type TableViewUser = {
  email: string;
  name: string;
  profile_picture: string;
  identity_provider: string;
  // Activation stage
  is_tutorial_completed: boolean;
  is_onboarding_completed: boolean;
  is_company_policy_uploaded: boolean;
  is_calendar_connected: boolean;
  has_preferences?: boolean;
  // Activities
  sign_up_at: string;
  first_trip_created_at: string;
  last_trip_created_at: string;
  first_hotel_searched_at: string;
  last_hotel_searched_at: string;
  first_flight_searched_at: string;
  last_flight_searched_at: string;
};

export type BookingData = {
  booking_link: {
    label: string;
    src: string;
  };
  booking_type: string;
  confirmation_number: string;
  user_email: string;
  user_id: number;
  name: string;
  status: string;
  thread_title: string;
};

export type BookingsResponse = {
  data: BookingData[];
  page: number;
  total: number;
  total_page: number;
};

export enum BookingTypes {
  ACCOMODATION = "accommodations",
  FLIGHT = "flight",
}

export type BookingStatsResponse = {
  total_flights_booked: number;
  total_hotels_booked: number;
  total_flights_amount: number;
  total_hotels_amount: number;
  date_range: {
    start: string | null;
    end: string | null;
  };
  currency: "USD";
};

export type FeatureFlagRequest = {
  enabled: boolean;
  feature_flag: string;
};
