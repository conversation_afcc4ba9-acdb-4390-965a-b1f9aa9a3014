import dayjs from "dayjs";
import { RangeObject, StatsDateRanges } from "../types/trips";

/** Utility function to convert a date range option into an actual pair of start and end dates */
export default function dateRangeFromOption(
  input: StatsDateRanges
): RangeObject {
  const range: RangeObject = {
    end: dayjs().format("DD-MM-YYYY"),
    start: dayjs().subtract(30, "day").format("DD-MM-YYYY"),
  };

  switch (input) {
    case StatsDateRanges["30DAYS"]:
      range.start = dayjs().subtract(30, "days").format("DD-MM-YYYY");
      break;
    case StatsDateRanges["6MONTHS"]:
      range.start = dayjs().subtract(6, "months").format("DD-MM-YYYY");
      break;
    case StatsDateRanges.YEAR:
      range.start = dayjs().subtract(1, "years").format("DD-MM-YYYY");
      break;
    default:
      break;
  }

  return range;
}
