import { useCallback } from "react";
import { use<PERSON>tom, useSet<PERSON>tom } from "jotai";
import { Dropdown, DropdownChangeEvent } from "primereact/dropdown";
import clsx from "clsx";
import { TripsList } from "@/features/chat/types/trip";
import { UserResponse } from "@/features/admin/types/api";
import { initialForeignTrips } from "@/features/admin/constants/foreign-trips";
import { useUserProfile } from "@/features/user/hooks/api";
import { useGetUserTrips, useUsersList } from "@/features/admin/hooks/api";
import { ottoReplyingAtom } from "@/features/chat/store/chat";
import { foreignTripsAtom } from "@/features/admin/store/trips";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import LoadingSpinner from "@/common/components/loading-spinner";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/common/constants";
import arrayHasElements from "@/common/utils/array-has-elements";
import { useReset } from "@/features/chat/hooks/use-reset";

type UserSelectProps = {
  className?: string;
  onAfterChange?: (data: TripsList) => void;
  onClear?: VoidFunction;
};

export default function UserSelect({
  className,
  onAfterChange,
  onClear,
}: UserSelectProps) {
  const router = useRouter();

  const { resetChatHistory } = useReset();
  const [ottoReplying, setOttoReplying] = useAtom(ottoReplyingAtom);
  const { profile } = useUserProfile();
  const { data: users, isLoading } = useUsersList();
  const [foreignUser, setForeignUser] = useAtom<UserResponse | null>(
    foreignUserAtom
  );
  const setForeignTrips = useSetAtom(foreignTripsAtom);

  const usersWithName = users?.map((user) => ({
    ...user,
    user_name:
      user.preferred_name || `${user.first_name} ${user.last_name}`.trim(),
  }));
  const options = usersWithName?.map((user) => ({
    label: `${user.user_name || user.email} (${user.id})`,
    value: user,
  }));

  const getUserTrips = useGetUserTrips({ onSuccess: onAfterChange });

  const changeHandler = useCallback(
    async (e: DropdownChangeEvent) => {
      resetChatHistory();
      setOttoReplying(true);

      if (!e.value || profile?.id === e.value?.id) {
        setForeignTrips(initialForeignTrips);
        setForeignUser(null);
        onClear?.();
        return;
      }

      if (!e.value.is_onboarding_completed) {
        router.push(ROUTES.ONBOARDING);
        setForeignUser(e.value);
        return;
      }

      const { booked, planned } = await getUserTrips({
        urlEnding: `?user_id=${e.value?.id}`,
      });

      if (!arrayHasElements(booked) && !arrayHasElements(planned)) {
        router.push(ROUTES.TRIPS);
        setOttoReplying(false);
      } else {
        const lastTrip = arrayHasElements(planned)
          ? planned[planned.length - 1]
          : booked[booked.length - 1];
        router.push(`${ROUTES.TRIPS}/${lastTrip.id}`);
      }

      setForeignUser(e.value);
    },
    [
      getUserTrips,
      onClear,
      profile?.id,
      router,
      setForeignTrips,
      setForeignUser,
      resetChatHistory,
      setOttoReplying,
    ]
  );

  return isLoading ? (
    <LoadingSpinner />
  ) : (
    <Dropdown
      checkmark
      disabled={ottoReplying}
      value={foreignUser}
      onChange={changeHandler}
      options={options}
      placeholder="View as"
      pt={{
        root: { className: clsx("h-9", className) },
        input: {
          className:
            "overflow-hidden text-nowrap text-ellipsis max-w-24 sm:max-w-full",
        },
      }}
      showClear
    />
  );
}
