import React, { useState } from "react";
import dayjs from "dayjs";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

interface HumanMessage {
  id: number;
  created_date: string;
  content: string;
  additional_kwargs: Record<string, any>;
}

interface HumanMessagesListProps {
  messages: HumanMessage[];
  className?: string;
}

export default function HumanMessagesList({ messages, className = "" }: HumanMessagesListProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!messages || messages.length === 0) {
    return (
      <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
        <div
          className="flex items-center cursor-pointer"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <FontAwesomeIcon
            icon={isExpanded ? "chevron-right" : "chevron-down"}
            className="text-blue-800 mr-2 text-sm"
          />
          <h4 className="font-medium text-blue-800 mb-2">Human Messages</h4>
        </div>
        {isExpanded && (
          <p className="text-blue-600 text-sm italic">No human messages found for this thread.</p>
        )}
      </div>
    );
  }

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div
        className="flex items-center cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <FontAwesomeIcon
          icon={isExpanded ? "chevron-right" : "chevron-down"}
          className="text-blue-800 mr-2 text-sm"
        />
        <h4 className="font-medium text-blue-800">Human Messages ({messages.length})</h4>
      </div>
      {isExpanded && (
        <div className="mt-2 space-y-3">
          {messages.map((message) => (
            <div key={message.id} className="bg-white rounded-lg p-3 border border-blue-100">
              <div className="flex justify-between items-start mb-2">
                <span className="text-xs text-blue-600 font-medium">
                  Message #{message.id}
                </span>
                <span className="text-xs text-gray-500">
                  {dayjs(message.created_date).format("MMM DD, YYYY [at] HH:mm:ss")}
                </span>
              </div>
              <div className="text-sm text-gray-800 leading-relaxed whitespace-pre-wrap">
                {message.content}
              </div>
              {Object.keys(message.additional_kwargs).length > 0 && (
                <details className="mt-2">
                  <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                    Additional Data
                  </summary>
                  <pre className="mt-1 text-xs bg-gray-50 p-2 rounded border overflow-x-auto">
                    {JSON.stringify(message.additional_kwargs, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
