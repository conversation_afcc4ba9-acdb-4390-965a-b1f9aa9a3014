import { ChangeEvent<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ttributes } from "react";
import clsx from "clsx";
import { InputText } from "primereact/inputtext";
import useDebounce from "@/common/hooks/use-debounce";
import { useUpdateQueryString } from "@/common/hooks/use-query-string";
import IconSearch from "@/common/components/icons/icon-search";

export default function SearchField({
  className,
  ...restProps
}: HTMLAttributes<HTMLDivElement>) {
  const updateQueryString = useUpdateQueryString();
  const changeHandler = useDebounce<ChangeEventHandler<HTMLInputElement>>(
    (event) => {
      updateQueryString({ page: null, search: event.target.value });
    }
  );

  return (
    <div className={clsx("relative", className)} {...restProps}>
      <IconSearch className="absolute left-2 top-2.5" />
      <InputText className="pl-8 w-full sm:w-max" onChange={changeH<PERSON><PERSON>} />
    </div>
  );
}
