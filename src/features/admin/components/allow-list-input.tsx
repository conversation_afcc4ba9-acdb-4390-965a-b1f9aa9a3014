import { useState } from "react";
import { But<PERSON> } from "primereact/button";
import { InputTextarea } from "primereact/inputtextarea";
import { useAllowEmail } from "../hooks/api";
import arrayHasElements from "@/common/utils/array-has-elements";
import regex from "@/common/constants/regex";
import clsx from "clsx";
import { ApiRestMethod } from "@/common/constants";

export default function AllowListInput() {
  const [value, setValue] = useState<string>("");
  const [error, setError] = useState<string>("");

  const { trigger: addToAllowList } = useAllowEmail({
    onSuccess: () => setValue(""),
  });

  const submitHandler = () => {
    const addresses = value.replaceAll(" ", "").replaceAll("\n", "").split(",");
    if (arrayHasElements(addresses)) {
      if (addresses.some((address) => !regex.email.test(address))) {
        setError("Invalid email addresses");
      } else {
        addToAllowList({
          options: { method: ApiRestMethod.POST },
          urlEnding: `?emails=${addresses.join(",")}`,
        });
      }
    }
  };

  return (
    <div className="flex items-start space-x-4 pb-6 relative">
      <div className="flex-1">
        <InputTextarea
          autoResize
          pt={{
            root: {
              className: clsx({ "border-red-400 outline-red-400": !!error }),
            },
          }}
          style={{ width: "100%" }}
          cols={40}
          rows={2}
          onChange={(e) => {
            !!error && setError("");
            setValue(e.target.value);
          }}
          placeholder="Add email addresses to the allow list"
          value={value}
        />
        {!!error && (
          <div className="absolute bottom-2 text-red-500 text-sm">{error}</div>
        )}
      </div>
      <Button disabled={!value} onClick={submitHandler} className="self-start">
        Add to beta!
      </Button>
    </div>
  );
}
