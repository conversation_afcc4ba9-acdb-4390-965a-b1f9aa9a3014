import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

interface TravelContextDisplayProps {
  travelContext: Record<string, any> | null;
  className?: string;
}

export default function TravelContextDisplay({ travelContext, className = "" }: TravelContextDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!travelContext) {
    return (
      <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>
        <div 
          className="flex items-center cursor-pointer"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <FontAwesomeIcon 
            icon={isExpanded ? "chevron-right" : "chevron-down"} 
            className="text-green-800 mr-2 text-sm"
          />
          <h4 className="font-medium text-green-800">Travel Context (JSON)</h4>
        </div>
        {isExpanded && (
          <p className="text-green-600 text-sm italic mt-2">No travel context found for this thread.</p>
        )}
      </div>
    );
  }

  return (
    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>
      <div 
        className="flex items-center cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <FontAwesomeIcon 
          icon={isExpanded ? "chevron-right" : "chevron-down"} 
          className="text-green-800 mr-2 text-sm"
        />
        <h4 className="font-medium text-green-800">Travel Context (JSON)</h4>
      </div>
      {isExpanded && (
        <div className="bg-white rounded border border-green-100 p-3 mt-3">
          <pre className="text-xs text-gray-800 overflow-x-auto whitespace-pre-wrap">
            {JSON.stringify(travelContext, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
