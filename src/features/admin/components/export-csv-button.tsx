import clsx from "clsx";
import { Button } from "primereact/button";
import React from "react";

type ExportCSVButtonProps = {
  onExport: VoidFunction;
  loading?: boolean;
  className?: string;
};

export default function ExportCSVButton({
  onExport,
  loading,
  className,
}: ExportCSVButtonProps) {
  return (
    <Button
      onClick={onExport}
      loading={loading}
      className={clsx("btn-gradient", className)}
      icon={
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth="1.5"
          stroke="currentColor"
          className="size-4"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M9 8.25H7.5a2.25 2.25 0 0 0-2.25 2.25v9a2.25 2.25 0 0 0 2.25 2.25h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25H15M9 12l3 3m0 0 3-3m-3 3V2.25"
          />
        </svg>
      }
    >
      Download CSV
    </Button>
  );
}
