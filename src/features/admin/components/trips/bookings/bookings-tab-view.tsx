import { useState } from "react";
import { BookingsTabs } from "@/features/admin/types/trips";
import { TabPanel, TabView } from "primereact/tabview";
import FlightsTab from "./flights-tab";
import HotelsTab from "./hotels-tab";
import { useUpdateQueryString } from "@/common/hooks/use-query-string";

export default function BookingsTabView() {
  const [activeTab, setActiveTab] = useState<BookingsTabs>(
    BookingsTabs.FLIGHTS
  );
  const updateQueryString = useUpdateQueryString();

  return (
    <TabView
      activeIndex={activeTab}
      className="mt-8"
      onTabChange={(e) => {
        setActiveTab(e.index);
        updateQueryString({ page: null });
      }}
    >
      <TabPanel header="Flights">
        <FlightsTab />
      </TabPanel>
      <TabPanel header="Hotels">
        <HotelsTab />
      </TabPanel>
    </TabView>
  );
}
