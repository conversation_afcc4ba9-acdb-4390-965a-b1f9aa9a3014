import Link from "next/link";
import Paginator from "@/common/components/paginator";
import { ADMIN_PAGE_TRIP_LIST_ROWS } from "@/common/constants";
import { ApiPaths } from "@/common/constants";
import { useBookingsList, useUsersList } from "@/features/admin/hooks/api";
import useQueryParamsDateRange from "@/features/admin/hooks/date-range";
import { BookingRecord, BookingTypes } from "@/features/admin/types/api";
import { downloadAsCSV } from "@/common/utils/csv-export";
import { fetchAllPages } from "@/common/utils/fetch-all-pages";
import ExportCSVButton from "../../../components/export-csv-button";
import clsx from "clsx";
import { useSearchParams } from "next/navigation";
import { Column } from "primereact/column";
import { DataTable } from "primereact/datatable";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

export default function FlightsTab() {
  const searchParams = useSearchParams();
  const currentPage = parseInt(searchParams.get("page") ?? "1", 10);
  const search = searchParams.get("search") ?? "";
  const { queryParams: dateRange } = useQueryParamsDateRange() ?? {};
  const { start, end } = dateRange ?? { end: "", start: "" };
  const dateStr = start && end ? `-${start}-${end}` : "";

  const { data, isLoading } = useBookingsList({
    booking_type: BookingTypes.FLIGHT,
    end,
    limit: ADMIN_PAGE_TRIP_LIST_ROWS.toString(),
    page: currentPage.toString(),
    search,
    start,
  });
  const { data: flights, total } = data ?? { data: [] as BookingRecord[] };

  return (
    <>
      <div className="relative">
        <ExportCSVButton
          className="absolute -top-16 right-0"
          onExport={async () => {
            const baseUrl = `${ApiPaths.ADMIN_GET_TRIP_DATA}?booking_type=${BookingTypes.FLIGHT}&start=${start}&end=${end}&limit=100&search=${search}`;
            const allData = await fetchAllPages(baseUrl);
            await downloadAsCSV(allData, `otto-flight-bookings${dateStr}.csv`);
          }}
          loading={isLoading}
        />
      </div>

      <DataTable
        pt={{
          wrapper: {
            className: "hide-scrollbar !text-sm",
          },
        }}
        className={clsx({ "opacity-50": isLoading })}
        value={flights}
        tableStyle={{ minWidth: "80rem" }}
      >
        <Column
          body={({ thread_id }) => (
            <Link href={`/admin/trips/${thread_id}`}>
              <FontAwesomeIcon icon="eye" />
            </Link>
          )}
          className="min-w-10 overflow-hidden text-nowrap text-ellipsis"
          field="id"
        ></Column>
        <Column
          className="min-w-44 overflow-hidden text-nowrap text-ellipsis"
          field="name"
          header="User"
        ></Column>
        <Column
          className="min-w-44 overflow-hidden text-nowrap text-ellipsis"
          field="user_email"
          header="Email"
          bodyClassName={"text-xs"}
        ></Column>
        <Column
          className="min-w-32 overflow-hidden text-nowrap text-ellipsis"
          field="title"
          header="Title"
        ></Column>
        <Column
          className="min-w-32 overflow-hidden text-nowrap text-ellipsis"
          field="confirmation_number"
          header="PNR"
        ></Column>
        <Column
          className="capitalize min-w-28 overflow-hidden text-nowrap text-ellipsis"
          field="status"
          header="Status"
        ></Column>
        <Column
          className="capitalize min-w-28 overflow-hidden text-nowrap text-ellipsis"
          field="start_date"
          header="From"
        ></Column>
        <Column
          className="capitalize min-w-28 overflow-hidden text-nowrap text-ellipsis"
          field="end_date"
          header="To"
        ></Column>
        <Column
          className="p-0 overflow-hidden text-nowrap text-ellipsis"
          header="Link"
          body={({ booking_link }) => {
            if (!!booking_link)
              return (
                <a
                  href={booking_link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-button"
                >
                  🌐 Spotnana
                </a>
              );
            return <p>🚫 Link not available</p>;
          }}
        ></Column>
      </DataTable>

      <Paginator totalRecords={total ?? 0} rows={ADMIN_PAGE_TRIP_LIST_ROWS} />
    </>
  );
}
