import { useEffect, useState } from "react";
import IconCaret from "@/common/components/icons/caret";
import IconClose from "@/common/components/icons/close";
import { StatsDateRanges } from "@/features/admin/types/trips";
import { Calendar } from "primereact/calendar";
import { <PERSON><PERSON> } from "primereact/button";
import IconCheck from "@/common/components/icons/check";
import { Nullable } from "primereact/ts-helpers";
import { Panel } from "primereact/panel";
import arrayHasElements from "@/common/utils/array-has-elements";
import {
  statsDateRangesLabels,
  statsDateRangesOptions,
} from "@/features/admin/constants/trips";
import clsx from "clsx";
import dateRangeFromOption from "@/features/admin/utils/date-range-from-option";
import { useUpdateQueryString } from "@/common/hooks/use-query-string";
import dayjs from "dayjs";
import useQueryParamsDateRange from "@/features/admin/hooks/date-range";
import { useAtom } from "jotai";
import { adminDateRangeOptionAtom } from "@/features/admin/store/date-range";

export default function TripsStatsDateRange() {
  const { calendarValues, queryParams } = useQueryParamsDateRange() ?? {};
  const [dates, setDates] = useState<Nullable<(Date | null)[]>>(calendarValues);

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [option, setOption] = useState<StatsDateRanges | null>(
    !calendarValues ? StatsDateRanges["30DAYS"] : null
  );
  const [label, setLabel] = useAtom(adminDateRangeOptionAtom);

  const updateQueryString = useUpdateQueryString();

  useEffect(() => {
    setLabel(
      option
        ? statsDateRangesLabels[option]
        : `${queryParams?.start} - ${queryParams?.end}`
    );
  }, [option, queryParams?.end, queryParams?.start, setLabel]);

  useEffect(() => {
    if (!calendarValues) {
      updateQueryString(
        dateRangeFromOption(option || StatsDateRanges["30DAYS"])
      );
    }
  }, [option, calendarValues, updateQueryString]);

  return (
    <div className="relative">
      <div
        className="border border-neutral-600 flex h-9 items-center justify-between px-3 py-2 rounded-lg text-gray-900 min-w-56 w-full hover:bg-gray-100 hover:cursor-pointer sm:w-max dark:border-neutral-700 dark:hover:bg-white/10 dark:text-white"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="leading-4 pr-3">{label}</div>
        {isOpen ? (
          <IconClose className="mr-1" height={16} width={16} />
        ) : (
          <IconCaret />
        )}
      </div>
      {isOpen && (
        <Panel
          pt={{
            content: { className: "flex flex-col gap-6 md:flex-row" },
            root: { className: "dark:border dark:border-gray-900" },
          }}
          className="absolute top-full w-fit z-10"
        >
          <div>
            {arrayHasElements(statsDateRangesOptions) &&
              statsDateRangesOptions.map(({ label, value }, index) => (
                <Button
                  className={clsx(
                    "bg-transparent flex justify-between text-base text-gray-900 w-full md:w-56",
                    "dark:bg-transparent dark:text-white",
                    { "bg-primary-300": value === option }
                  )}
                  key={index}
                  onClick={() => {
                    setIsOpen(false);
                    setOption(value);
                    setDates(null);
                    updateQueryString({
                      page: null,
                      ...dateRangeFromOption(value),
                    });
                  }}
                >
                  {label}
                  {value === option && (
                    <IconCheck className="text-primary-500" size={14} />
                  )}
                </Button>
              ))}
          </div>

          <Calendar
            value={dates}
            onChange={(e) => {
              if (!!e.value?.[1]) {
                const [start, end] = e.value;
                setIsOpen(false);
                setOption(null);
                updateQueryString({
                  end: dayjs(end).format("DD-MM-YYYY"),
                  page: null,
                  start: dayjs(start).format("DD-MM-YYYY"),
                });
              }

              setDates(e.value);
            }}
            selectionMode="range"
            readOnlyInput
            hideOnRangeSelection
            inline
          />
        </Panel>
      )}
    </div>
  );
}
