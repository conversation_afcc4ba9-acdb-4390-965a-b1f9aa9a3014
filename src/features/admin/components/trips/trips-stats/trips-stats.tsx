import { useBookingsStatistics } from "@/features/admin/hooks/api";
import TripsStatsDisplay from "./stats-display";
import useQueryParamsDateRange from "@/features/admin/hooks/date-range";
import { useAtomValue } from "jotai";
import { adminDateRangeOptionAtom } from "@/features/admin/store/date-range";
import { statsDateRangesLabels } from "@/features/admin/constants/trips";

export default function TripsStats() {
  const { queryParams } = useQueryParamsDateRange() ?? {};
  const { data } = useBookingsStatistics(queryParams);
  const label = useAtomValue(adminDateRangeOptionAtom);

  return (
    <>
      <TripsStatsDisplay
        {...data}
        range={label ?? statsDateRangesLabels["30days"]}
      />
    </>
  );
}
