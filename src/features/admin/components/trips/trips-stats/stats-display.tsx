function formatCurrency(value: number): string {
  if (value < 1000) return `$${value.toFixed(2)}`;
  if (value < 1_000_000) return `$${(value / 1_000).toFixed(0)}k`;
  if (value < 1_000_000_000) return `$${(value / 1_000_000).toFixed(0)}M`;
  return `$${(value / 1_000_000_000).toFixed(0)}B`;
}

type StatProps = {
  label: string;
  range: string;
  value?: string;
};

function Stat({ label, range, value }: StatProps) {
  return (
    <div className="flex items-center gap-x-4 md:gap-x-6">
      <div className="sm:basis-1/3 grow-1 shrink-[3] font-bold text-3xl sm:text-right">
        {value}
      </div>
      <div className="sm:basis-1/2 grow-1 shrink-1 flex flex-col">
        <div className="font-bold leading-tight text-lg">{label}</div>
        <div className="italic leading-none overflow-hidden text-ellipsis text-nowrap text-neutral-600 text-sm dark:text-neutral-400">
          {range}
        </div>
      </div>
    </div>
  );
}

type TripsStatsDisplayProps = {
  total_flights_amount?: number;
  total_flights_booked?: number;
  total_hotels_amount?: number;
  total_hotels_booked?: number;
  range: string;
};

export default function TripsStatsDisplay({
  total_flights_amount,
  total_flights_booked,
  total_hotels_amount,
  total_hotels_booked,
  range,
}: TripsStatsDisplayProps) {
  return (
    <div className="grid grid-cols-1 gap-y-6 mt-6 w-full sm:grid-cols-2 lg:grid-cols-4 sm:gap-x-6 sm:place-content-center">
      <Stat
        label="Flights"
        range={range}
        value={total_flights_booked?.toString()}
      />
      <Stat
        label="Hotels"
        range={range}
        value={total_hotels_booked?.toString()}
      />
      {total_flights_amount !== undefined && (
        <Stat
          label="Flights"
          range={range}
          value={formatCurrency(total_flights_amount)}
        />
      )}
      {total_hotels_amount !== undefined && (
        <Stat label="Hotels" range={range} value={formatCurrency(total_hotels_amount)} />
      )}
    </div>
  );
}
