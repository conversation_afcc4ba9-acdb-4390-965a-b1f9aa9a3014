import { useState } from "react";
import { Dialog } from "primereact/dialog";
import { But<PERSON> } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { useImportThread } from "@/features/admin/hooks/api";
import useToast from "@/common/hooks/use-toast";

interface ThreadImportModalProps {
  visible: boolean;
  onHide: () => void;
}

export default function ThreadImportModal({ visible, onHide }: ThreadImportModalProps) {
  const [sourceThreadId, setSourceThreadId] = useState("");
  const { showToast } = useToast();
  const { importThread, isMutating } = useImportThread();

  const handleImport = async () => {
    if (!sourceThreadId) {
      showToast({
        content: "Please enter a source thread ID",
        severity: "error",
        life: 3000,
      });
      return;
    }

    try {
      const response = await importThread({
        source_thread_id: parseInt(sourceThreadId),
      });

      showToast({
        content: response.message,
        severity: "success",
        life: 5000,
      });
      
      onHide();
      setSourceThreadId("");
    } catch (error) {
      showToast({
        content: "Failed to import thread. Please check the console for details.",
        severity: "error",
        life: 5000,
      });
      console.error("Thread import error:", error);
    }
  };

  return (
    <Dialog
      header="Import Thread"
      visible={visible}
      style={{ width: "450px" }}
      onHide={onHide}
      modal
      dismissableMask
    >
      <div className="flex flex-col gap-4 p-2">
        <div className="flex flex-col gap-2">
          <label htmlFor="sourceThreadId" className="font-medium">
            Source Thread ID*
          </label>
          <InputText
            id="sourceThreadId"
            value={sourceThreadId}
            onChange={(e) => setSourceThreadId(e.target.value)}
            keyfilter="int"
            placeholder="Enter source thread ID"
          />
        </div>

        <div className="flex justify-end mt-4 gap-2">
          <Button
            label="Cancel"
            className="p-button-secondary p-button-outlined"
            onClick={onHide}
          />
          <Button
            label="Import"
            className="p-button-primary"
            onClick={handleImport}
            loading={isMutating}
          />
        </div>
      </div>
    </Dialog>
  );
}
