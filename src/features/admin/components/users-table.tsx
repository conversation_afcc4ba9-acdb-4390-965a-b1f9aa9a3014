import { Column, ColumnBodyOptions } from "primereact/column";
import { DataTable, DataTableSortEvent, SortOrder } from "primereact/datatable";
import { TableViewUser } from "../types/api";
import arrayHasElements from "@/common/utils/array-has-elements";
import { Avatar } from "primereact/avatar";
import ExportCSVButton from "./export-csv-button";
import { useUpdateQueryString } from "@/common/hooks/use-query-string";
import { useSetAtom } from "jotai";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import { ottoReplyingAtom } from "@/features/chat/store/chat";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/common/constants";
import { useGetUserTrips, useUsersList } from "@/features/admin/hooks/api";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useReset } from "@/features/chat/hooks/use-reset";

type AdminUsersTableProps = {
  users: TableViewUser[];
  onExport: VoidFunction;
  isExporting?: boolean;
  sortField?: string;
  sortOrder?: SortOrder;
};

export default function AdminUsersTable({
  users,
  onExport,
  isExporting,
  sortField,
  sortOrder,
}: AdminUsersTableProps) {
  const updateQueryString = useUpdateQueryString();
  const router = useRouter();
  const { resetChatHistory } = useReset();
  const setOttoReplying = useSetAtom(ottoReplyingAtom);
  const { data: usersList } = useUsersList();
  const setForeignUser = useSetAtom(foreignUserAtom);

  const getUserTrips = useGetUserTrips({});

  const handleViewAsUser = async (tableUser: TableViewUser) => {
    const fullUser = usersList?.find((user) => user.email === tableUser.email);

    if (!fullUser) {
      console.error("User not found in the full users list");
      return;
    }

    resetChatHistory();
    setOttoReplying(true);

    if (!fullUser.is_onboarding_completed) {
      router.push(ROUTES.ONBOARDING);
      setForeignUser(fullUser);
      return;
    }

    const { booked, planned } = await getUserTrips({
      urlEnding: `?user_id=${fullUser.id}`,
    });

    if (!arrayHasElements(booked) && !arrayHasElements(planned)) {
      router.push(ROUTES.TRIPS);
      setOttoReplying(false);
    } else {
      const lastTrip = arrayHasElements(planned)
        ? planned[planned.length - 1]
        : booked[booked.length - 1];
      router.push(`${ROUTES.TRIPS}/${lastTrip.id}`);
    }

    setForeignUser(fullUser);
  };

  if (!arrayHasElements(users)) {
    return <h2>No user data loaded.</h2>;
  }

  const onSort = ({ sortField, sortOrder }: DataTableSortEvent) => {
    updateQueryString({ sortField, sortOrder });
  };

  const renderCheckmark = (
    rowData: TableViewUser,
    { field }: ColumnBodyOptions
  ) => {
    const value = rowData[field as keyof TableViewUser];

    if (value === true) {
      return <FontAwesomeIcon icon="check" className="text-base size-4" />;
    }
    if (value === false) {
      return <FontAwesomeIcon icon="xmark" className="text-base size-4" />;
    }
    return "";
  };

  const commonClasses = "overflow-hidden text-nowrap text-ellipsis";

  return (
    <>
      <div className="flex justify-between items-center">
        <h2 className="font-semibold mt-10 text-lg">Existing beta users:</h2>
        <ExportCSVButton onExport={onExport} loading={isExporting} />
      </div>

      <DataTable
        pt={{
          wrapper: {
            className: "hide-scrollbar !text-sm",
          },
        }}
        value={users}
        tableStyle={{ minWidth: "100rem" }}
        removableSort
        onSort={onSort}
        sortField={sortField}
        sortOrder={sortOrder}
        lazy
      >
        <Column
          field="profile_picture"
          body={(user: TableViewUser) => (
            <Avatar
              image={user.profile_picture}
              label={user.name?.[0]}
              shape="circle"
            />
          )}
        />

        <Column className={commonClasses} field="name" header="User" sortable />
        <Column
          className={commonClasses}
          field="email"
          header="Email"
          sortable
          body={(user: TableViewUser) => (
            <div className="flex items-center gap-2">
              <span>{user.email}</span>
              <a
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handleViewAsUser(user);
                }}
                title="View as user"
                className="text-blue-500 hover:underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                <FontAwesomeIcon
                  icon="arrow-up-right-from-square"
                  className="text-base size-3.5"
                />
              </a>
            </div>
          )}
        />
        <Column
          className={commonClasses}
          field="identity_provider"
          header="Login"
          sortable
        />
        <Column
          className={commonClasses}
          field="is_tutorial_completed"
          header="Tutorial?"
          body={renderCheckmark}
          sortable
        />
        <Column
          className={commonClasses}
          field="is_onboarding_completed"
          header="Onboarding?"
          body={renderCheckmark}
          sortable
        />
        <Column
          className={commonClasses}
          field="has_preferences"
          header="Preferences set?"
          body={renderCheckmark}
          sortable
        />
        <Column
          className={commonClasses}
          field="is_company_policy_uploaded"
          header="Policy?"
          body={renderCheckmark}
          sortable
        />
        <Column
          className={commonClasses}
          field="is_calendar_connected"
          header="Calendar?"
          body={renderCheckmark}
          sortable
        />
        <Column
          className={commonClasses}
          field="last_sign_in"
          header="Last Sign In At"
          sortable
        />
        <Column
          className={commonClasses}
          field="sign_up_at"
          header="Sign Up At"
          sortable
        />
        <Column
          className={commonClasses}
          field="first_trip_created_at"
          header="First Trip"
          sortable
        />
        <Column
          className={commonClasses}
          field="last_trip_created_at"
          header="Last Trip"
          sortable
        />
        <Column
          className={commonClasses}
          field="first_hotel_searched_at"
          header="First Hotel Search"
          sortable
        />
        <Column
          className={commonClasses}
          field="last_hotel_searched_at"
          header="Last Hotel Search"
          sortable
        />
        <Column
          className={commonClasses}
          field="first_flight_searched_at"
          header="First Flight Search"
          sortable
        />
        <Column
          className={commonClasses}
          field="last_flight_searched_at"
          header="Last Flight Search"
          sortable
        />
      </DataTable>
    </>
  );
}
