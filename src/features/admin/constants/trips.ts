import { SelectItem } from "primereact/selectitem";
import { StatsDateRanges } from "../types/trips";

export const statsDateRangesLabels: { [key in StatsDateRanges]: string } = {
  [StatsDateRanges["30DAYS"]]: "Last 30 days",
  [StatsDateRanges["6MONTHS"]]: "Last 6 months",
  [StatsDateRanges.YEAR]: "Last 12 months",
};

export const statsDateRangesOptions: SelectItem[] = Object.entries(
  statsDateRangesLabels
).map((dateRange) => ({
  label: dateRange[1],
  value: dateRange[0],
}));
