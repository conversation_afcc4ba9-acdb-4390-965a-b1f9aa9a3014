import useSWRMutation from "swr/mutation";
import { ApiPaths, ApiRestMethod } from "@/common/constants";
import useToast from "@/common/hooks/use-toast";
import { fetcher } from "@/common/api/fetcher";
import { useSet<PERSON>tom } from "jotai";
import { tutorialStepAtom } from "../store/tutorial-store";
import { TutorialSteps } from "../types/tutorial-types";

export function useCompleteTutorial() {
  const { showErrorToast } = useToast();
  const setTutorialStep = useSetAtom(tutorialStepAtom);
  const { trigger, isMutating } = useSWRMutation(
    ApiPaths.USER_COMPLETE_TUTORIAL,
    fetcher
  );

  function completeTutorial() {
    setTutorialStep(TutorialSteps.COMPLETE);
    trigger({ options: { method: ApiRestMethod.POST } }).catch((error) => {
      showErrorToast(error, "Tutorial completion request failed.");
    });
  }

  return {
    completeTutorial,
    isMutating,
  };
}
