import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import { Toolt<PERSON> } from "react-tooltip";
import { But<PERSON> } from "primereact/button";
import { TutorialSteps } from "@/features/tutorial/types/tutorial-types";
import { tutorialStepAtom } from "@/features/tutorial/store/tutorial-store";
import { accountMenuAtom, showSidebar<PERSON>tom } from "@/common/store/sidebar";
import { useStoreToggle } from "@/common/hooks/toggles";
import { useCompleteTutorial } from "@/features/tutorial/hooks/api";

export default function TripsTooltip() {
  const { completeTutorial } = useCompleteTutorial();
  const setTutorialStep = useSetAtom(tutorialStepAtom);
  const { turnOff: closeSidebar } = useStoreToggle(showSidebarAtom);
  const { turnOn: openAccountMenu } = useStoreToggle(accountMenuAtom);

  return (
    <>
      <Tooltip
        className="bg-primary-550 fade-in max-w-80 p-4 rounded-lg z-30"
        classNameArrow="!top-1/2"
        clickable
        id={TutorialSteps.TRIPS}
        isOpen={true}
        offset={16}
        opacity={0}
        place="right-start"
      >
        <p>
          On the left side, you can find the trips you’re planning or have
          booked.
        </p>
        <div className="flex items-center gap-x-4 mt-4">
          <Button
            className="bg-white text-gray-950 dark:bg-gray-800 dark:text-white"
            data-testid="trips-tooltip-done-button"
            onClick={() => {
              openAccountMenu();
              setTutorialStep(TutorialSteps.ACCOUNT_MENU);
            }}
          >
            Done
          </Button>
          <Button
            className="text-white hover:underline"
            data-testid="trips-tooltip-skip-button"
            onClick={() => {
              closeSidebar();
              completeTutorial();
            }}
            plain
          >
            Skip
          </Button>
        </div>
      </Tooltip>
    </>
  );
}
