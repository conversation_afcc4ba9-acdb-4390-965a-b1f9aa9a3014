import { useAtomValue } from "jotai";
import SidebarTooltip from "./sidebar-tooltip";
import TripsTooltip from "./trips-tooltip";
import { tutorialStepAtom } from "../store/tutorial-store";
import { TutorialSteps } from "../types/tutorial-types";
import AccountMenuTooltip from "./account-menu-tooltip";
import CreateTripTooltip from "./create-trip-tooltip";

export default function TutorialTooltips() {
  const tutorialStep = useAtomValue(tutorialStepAtom);

  switch (tutorialStep) {
    case TutorialSteps.ACCOUNT_MENU:
      return <AccountMenuTooltip />;
      break;
    case TutorialSteps.CREATE_TRIP:
      return <CreateTripTooltip />;
      break;
    case TutorialSteps.SIDEBAR:
      return <SidebarTooltip />;
      break;
    case TutorialSteps.TRIPS:
      return <TripsTooltip />;
      break;
    default:
      return null;
  }
}
