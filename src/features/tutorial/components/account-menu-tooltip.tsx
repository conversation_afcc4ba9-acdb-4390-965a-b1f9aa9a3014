import { useSet<PERSON><PERSON> } from "jotai";
import { Toolt<PERSON> } from "react-tooltip";
import { But<PERSON> } from "primereact/button";
import { TutorialSteps } from "@/features/tutorial/types/tutorial-types";
import { tutorialStepAtom } from "@/features/tutorial/store/tutorial-store";
import { accountMenuAtom, showSidebar<PERSON>tom } from "@/common/store/sidebar";
import { useStoreToggle } from "@/common/hooks/toggles";
import { useCompleteTutorial } from "@/features/tutorial/hooks/api";

export default function AccountMenuTooltip() {
  const { completeTutorial } = useCompleteTutorial();
  const setTutorialStep = useSetAtom(tutorialStepAtom);
  const { turnOff: closeSidebar } = useStoreToggle(showSidebarAtom);
  const { turnOff: closeAccountMenu } = useStoreToggle(accountMenuAtom);

  return (
    <>
      <Tooltip
        className="bg-primary-550 fade-in max-w-80 p-4 rounded-lg z-30"
        clickable
        id={TutorialSteps.ACCOUNT_MENU}
        isOpen={true}
        offset={16}
        opacity={0}
      >
        <p>
          By clicking on your name, you can see and edit your Travel
          Preferences, edit your Profile, Loyalty and Payments and more.
          Don’t worry about adding anything to your profile or payments yet,
          we’ll gather that info during your first booking.
        </p>
        <div className="flex items-center gap-x-4 mt-4">
          <Button
            className="bg-white text-gray-900 dark:bg-gray-800 dark:text-white"
            onClick={() => {
              closeAccountMenu();
              setTutorialStep(TutorialSteps.CREATE_TRIP);
            }}
          >
            Done
          </Button>
          <Button
            className="text-white hover:underline"
            onClick={() => {
              closeAccountMenu();
              completeTutorial();
              setTimeout(() => closeSidebar(), 300);
            }}
            plain
          >
            Skip
          </Button>
        </div>
      </Tooltip>
    </>
  );
}
