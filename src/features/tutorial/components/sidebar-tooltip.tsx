import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import { <PERSON><PERSON><PERSON> } from "react-tooltip";
import { But<PERSON> } from "primereact/button";
import { TutorialSteps } from "@/features/tutorial/types/tutorial-types";
import { tutorialStepAtom } from "@/features/tutorial/store/tutorial-store";
import { showSidebarAtom } from "@/common/store/sidebar";
import { useStoreToggle } from "@/common/hooks/toggles";
import { useCompleteTutorial } from "../hooks/api";

export default function SidebarTooltip() {
  const { completeTutorial } = useCompleteTutorial();
  const { turnOn: openSidebar } = useStoreToggle(showSidebarAtom);
  const setTutorialStep = useSetAtom(tutorialStepAtom);

  return (
    <div className="absolute z-20 lg:hidden">
      <Tooltip
        className="bg-primary-550 max-w-80 opacity-100 p-4 rounded-lg z-30"
        clickable
        id={TutorialSteps.SIDEBAR}
        isOpen={true}
        offset={24}
      >
        <p>
          You can find a list of your trips and settings by clicking this icon
        </p>
        <div className="flex items-center gap-x-4 mt-4">
          <Button
            className="bg-white text-gray-900 dark:bg-gray-800 dark:text-white"
            data-testid="sidebar-tooltip-done-button"
            onClick={() => {
              openSidebar();
              setTutorialStep(TutorialSteps.COMPLETE);
              setTimeout(() => setTutorialStep(TutorialSteps.TRIPS), 200);
            }}
          >
            Done
          </Button>
          <Button
            className="text-white hover:underline"
            onClick={completeTutorial}
            data-testid="sidebar-tooltip-skip-button"
            plain
          >
            Skip
          </Button>
        </div>
      </Tooltip>
    </div>
  );
}
