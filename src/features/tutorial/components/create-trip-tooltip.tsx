import { Tooltip } from "react-tooltip";
import { TutorialSteps } from "@/features/tutorial/types/tutorial-types";

export default function CreateTripTooltip() {
  return (
    <>
      <Tooltip
        className="bg-primary-550 max-w-80 opacity-100 p-4 rounded-lg z-30"
        clickable
        id={TutorialSteps.CREATE_TRIP}
        isOpen={true}
        offset={16}
        place="right"
      >
        <p>That’s it. To get started, click here...</p>
      </Tooltip>
    </>
  );
}
