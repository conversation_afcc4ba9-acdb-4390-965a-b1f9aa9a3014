import { ReactNode } from "react";
import { useCheckFeatureFlags } from "@/features/feature-flags/hooks/api";

type WithFeatureFlagProps = {
  children: ReactNode;
  flag: string;
};

export default function WithFeatureFlag({
  children,
  flag,
}: WithFeatureFlagProps) {
  const { hasFlag, isLoading } = useCheckFeatureFlags(flag);

  if (isLoading || !hasFlag) {
    return null;
  }

  return children;
}
