import useS<PERSON> from "swr";
import FlagsResponse from "@/features/feature-flags/types/flags-response";
import { ApiPaths } from "@/common/constants";
import useSWRMutation from "swr/mutation";
import { fetcher } from "@/common/api/fetcher";
import { ApiRestMethod } from "@/common/constants";
import { FeatureFlagRequest } from "@/features/admin/types/api";

export function useCheckFeatureFlags(flag: string) {
  const { data, isLoading } = useSWR<FlagsResponse>(
    ApiPaths.USER_FEATURE_FLAGS
  );

  return {
    hasFlag: data?.feature_flags.includes(flag),
    isLoading,
  };
}

/**
 * Hook to update a memory feature flag using SWR mutation
 */
export function useUpdateMemoryFeatureFlag() {

  const updateMemoryFeatureFlagRequest = (
    url: string,
    { arg }: { arg: FeatureFlagRequest }
  ) =>
    fetcher(url, {
      arg: {
        options: { method: ApiRestMethod.POST, body: JSON.stringify(arg) },
      },
    });

  const { trigger, isMutating } = useSWRMutation(
    ApiPaths.USER_FEATURE_FLAGS,
    updateMemoryFeatureFlagRequest,
    {
      onSuccess: () => {
      },
      onError: (error) => {
        console.error("Failed to update feature flag:", error);
      },
    }
  );

  return {
    updateFeatureFlag: trigger,
    isUpdating: isMutating,
  };
}