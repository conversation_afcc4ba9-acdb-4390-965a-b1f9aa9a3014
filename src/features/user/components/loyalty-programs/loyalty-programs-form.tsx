import { useForm } from "react-hook-form";
import clsx from "clsx";
import { SelectItemOptionsType } from "primereact/selectitem";
import { Button } from "primereact/button";

import { LoyaltyFormFields } from "@/features/user/types/loyalty-programs";
import FormField from "@/common/components/form/form-field";
import FormSelect from "@/common/components/form/form-select";

type LoyaltyProgramsFormProps = {
  initialValues?: LoyaltyFormFields | null;
  isMutating?: boolean;
  onRemove: (program: string) => void;
  onSubmit: (data: LoyaltyFormFields) => void;
  programOptions: SelectItemOptionsType;
};

export default function LoyaltyProgramsForm({
  initialValues,
  isMutating,
  onRemove,
  onSubmit,
  programOptions,
}: LoyaltyProgramsFormProps) {
  const isEditState = !!initialValues?.program;

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    control,
  } = useForm<LoyaltyFormFields>({
    values: {
      program: initialValues?.program ?? "",
      number: initialValues?.number ?? "",
    },

    mode: "onChange",
    reValidateMode: "onChange",
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <FormSelect
        className={clsx({
          "!bg-transparent border-none !cursor-auto font-medium px-0 text-neutral-500":
            !!isEditState,
        })}
        control={control}
        disabled={isEditState}
        error={errors.program}
        filter
        label="Program name"
        name="program"
        options={programOptions}
        placeholder="Choose loyalty program"
        pt={{ trigger: { className: isEditState && "hidden" } }}
        required
      />
      <FormField
        {...register("number", { required: true })}
        containerClassName="mt-3"
        inputMode="text"
        label="Loyalty number"
        required
      />
      <div className="md:flex md:gap-x-2.5 md:items-center md:justify-end md:mt-4">
        {isEditState && (
          <Button
            className="mt-4 w-full md:w-max"
            disabled={isMutating}
            loading={isMutating}
            outlined
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              onRemove(initialValues.program);
            }}
          >
            Remove
          </Button>
        )}
        <Button
          className="mt-4 w-full md:w-max"
          disabled={isMutating || !isDirty || !isValid}
          loading={isMutating}
          type="submit"
        >
          Save
          <span className="max-md:hidden"> & continue</span>
        </Button>
      </div>
    </form>
  );
}
