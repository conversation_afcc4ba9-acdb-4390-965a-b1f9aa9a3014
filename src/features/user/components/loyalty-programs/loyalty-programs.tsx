import { useMemo, useState } from "react";

import {
  LoyaltyFormFields,
  LoyaltyProgram,
  LoyaltyProgramType,
} from "@/features/user/types/loyalty-programs";
import {
  airlinesProgramOptions,
  frequentFlyerPrograms,
  hotelsProgramOptions,
  IATACode as IATACodeType,
} from "@/features/user/constants/loyalty-programs";
import useLoyaltyPrograms from "@/features/user/api/use-loyalty-programs";
import useLoyaltyProgramsOperations from "@/features/user/api/use-loyalty-programs-operations";
import Popup from "@/common/components/popup";
import LoyaltyProgramsList from "@/features/user/components/loyalty-programs/loyalty-programs-list";
import LoyaltyProgramsForm from "@/features/user/components/loyalty-programs/loyalty-programs-form";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";

export default function LoyaltyPrograms() {
  const { isMobile } = useViewportSize();
  const { onboardingRedirect } = useOnboardingRedirect();

  const [showForm, setShowForm] = useState<LoyaltyProgramType | null>(null);
  const isFlightForm = showForm === "flight";
  const [initialValues, setInitialValues] = useState<LoyaltyFormFields | null>(
    null
  );

  const { data, isLoading } = useLoyaltyPrograms();
  const { flights_loyalty_programs, hotels_loyalty_programs } = data ?? {};

  const flightsProgramsData: LoyaltyProgram[] = useMemo(
    () =>
      flights_loyalty_programs?.map(({ IATACode, number }) => {
        const program = frequentFlyerPrograms[IATACode as IATACodeType];
        return {
          label: `${program?.airline} ${program?.program}`,
          number,
          program: IATACode,
        };
      }) ?? [],
    [flights_loyalty_programs]
  );

  const hotelsProgramsData =
    hotels_loyalty_programs?.map(({ number, program }) => ({
      label: "",
      number,
      program,
    })) ?? [];

  const header = useMemo(() => {
    if (isFlightForm) {
      return isMobile ? "Airline loyalty" : "Add airline loyalty";
    }
    return "Add hotel loyalty";
  }, [isFlightForm, isMobile]);

  const { addLoyaltyProgram, isMutating, removeLoyaltyProgram } =
    useLoyaltyProgramsOperations();

  const closeForm = () => {
    setShowForm(null);
    setInitialValues(null);
  };

  const onClose = () => {
    if (isMobile) {
      onboardingRedirect();
    } else {
      closeForm();
    }
  };

  return (
    <div className="flex flex-col">
      <p className="text-base">
        Add your loyalty numbers to earn points from airlines and hotels when
        you book with Otto.
      </p>

      <LoyaltyProgramsList
        header="Airlines"
        isLoading={isLoading}
        onAdd={() => setShowForm("flight")}
        onProgramSelect={(data) => {
          setInitialValues(data);
          setShowForm("flight");
        }}
        programs={flightsProgramsData}
      />

      {/* <LoyaltyProgramsList
        header="Hotels"
        isLoading={isLoading}
        onAdd={() => setShowForm("hotel")}
        onProgramSelect={(data) => {
          setInitialValues(data);
          setShowForm("hotel");
        }}
        programs={hotelsProgramsData}
      /> */}

      {!!showForm && (
        <Popup
          className="p-0 md:p-6 w-[34.375rem] max-md:top-0 max-md:left-0 max-md:size-full max-md:translate-x-0 max-md:translate-y-0 max-md:rounded-none"
          pt={{
            content: { className: "max-md:p-0" },
            header: { className: "max-md:hidden md:pb-3" },
            title: { className: "font-medium" },
          }}
          header={header}
          onClose={onClose}
          onBack={closeForm}
        >
          <div className="max-w-[34.375rem] mx-auto p-3.5 md:p-0">
            <LoyaltyProgramsForm
              initialValues={initialValues}
              isMutating={isMutating}
              onSubmit={(data) =>
                addLoyaltyProgram(data, showForm).finally(closeForm)
              }
              onRemove={(program) =>
                removeLoyaltyProgram(program, showForm).finally(closeForm)
              }
              programOptions={
                isFlightForm ? airlinesProgramOptions : hotelsProgramOptions
              }
            />
          </div>
        </Popup>
      )}
    </div>
  );
}
