import { faAngleRight } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import { LoyaltyProgram } from "@/features/user/types/loyalty-programs";

type LoyaltyProgramItemProps = LoyaltyProgram & {
  onClick?: (program: Omit<LoyaltyProgram, "label">) => void;
};

export default function LoyaltyProgramItem({
  label,
  number,
  onClick,
  program,
}: LoyaltyProgramItemProps) {
  return (
    <div
      className="bg-gray-100 dark:bg-neutral-800 cursor-pointer duration-200 mt-3 p-4 relative rounded-md /
      transition-colors hover:bg-neutral-200"
      onClick={() => onClick?.({ number, program })}
    >
      <div className="font-medium leading-5.5">{label}</div>
      <div className="font-medium leading-5.5 mt-0.5 text-neutral-500">
        {number}
      </div>
      <FontAwesomeIcon
        className="absolute right-4 top-1/2 -translate-y-1/2"
        icon={faAngleRight}
      />
    </div>
  );
}
