import { <PERSON><PERSON> } from "primereact/button";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus } from "@fortawesome/pro-regular-svg-icons";

import { LoyaltyProgram } from "@/features/user/types/loyalty-programs";
import arrayHasElements from "@/common/utils/array-has-elements";
import LoyaltyProgramItem from "@/features/user/components/loyalty-programs/loyalty-program-item";
import LoadingSpinner from "@/common/components/loading-spinner";

type LoyaltyProgramsListProps = {
  header: string;
  isLoading?: boolean;
  onAdd: VoidFunction;
  onProgramSelect?: (program: Omit<LoyaltyProgram, "label">) => void;
  programs?: LoyaltyProgram[];
};

export default function LoyaltyProgramsList({
  header,
  isLoading,
  onAdd,
  onProgramSelect,
  programs,
}: LoyaltyProgramsListProps) {
  return (
    <>
      <div className="flex items-center justify-between mt-14 w-full">
        <h2 className="font-medium text-xl">{header}</h2>
        <Button
          className="font-medium p-0 text-blue-500 text-base"
          link
          onClick={onAdd}
        >
          <FontAwesomeIcon icon={faPlus} />
          Add more
        </Button>
      </div>
      {isLoading && <LoadingSpinner className="mt-2" />}

      {arrayHasElements(programs) &&
        programs.map((program, index) => (
          <LoyaltyProgramItem
            {...program}
            onClick={onProgramSelect}
            key={index}
          />
        ))}
    </>
  );
}
