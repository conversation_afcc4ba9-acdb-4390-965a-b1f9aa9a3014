import Checklist from "@/common/components/checklist";
import Popup from "@/common/components/popup";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { usePopupResize } from "@/common/hooks/use-popup-resize";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useMemo, useState } from "react";
import PersonalDetailsForm from "./personal-details-form";
import PaymentDetailsForm from "./payment-settings/payment-details-form";
import { ScrollPanel } from "primereact/scrollpanel";
import useProfileFormToggle from "../hooks/use-profile-form-switch";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import { ProfileForms } from "../types/profile-forms";
import useToast from "@/common/hooks/use-toast";
import usePaymentInformation from "../api/use-payment-information";
import LoadingSpinner from "@/common/components/loading-spinner";
import clsx from "clsx";
import { captureMessage } from "@sentry/nextjs";

enum FormStep {
  Personal,
  Payment,
}

export const PaymentDialog = () => {
  const { isMobile, isDesktop } = useViewportSize();
  const { showToast } = useToast();

  const [formStep, setFormStep] = useState<FormStep>(FormStep.Personal);
  // Can dismiss after form iframes finished loading to avoid errors
  const [canDismiss, setCanDismiss] = useState(false);
  const [isPaymentFormLoadedSuccessfully, setIsPaymentFormLoadedSuccessfully] =
    useState(false);

  const popupRef = usePopupResize();

  const {
    handleProfileFormClose,
    closeProfileForm,
    profileForm,
    profileFormDoneMessage,
  } = useProfileFormToggle();

  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);

  const header = useMemo(() => {
    if (isDesktop) {
      return "Personal and payment information";
    }
    if (formStep === FormStep.Personal) {
      return "Personal information";
    }
    return "Payment details";
  }, [isDesktop, formStep]);

  const items = useMemo(
    () => [
      {
        checked: formStep === FormStep.Payment,
        text: "Personal information",
        icon:
          formStep === FormStep.Personal ? (
            <FontAwesomeIcon icon="circle-1" />
          ) : (
            <FontAwesomeIcon icon="circle-check" />
          ),
      },
      {
        checked: false,
        text: "Payment details",
        icon: <FontAwesomeIcon icon="circle-2" />,
      },
    ],
    [formStep]
  );

  const close = () => handleProfileFormClose(send);

  const showPaymentFormLoadError = () => {
    setTimeout(() => {
      showToast({
        severity: "error",
        summary: "Payment form failed to load",
        detail:
          "This could be due to an ad blocker or security settings blocking third-party content.",
        life: 10000,
      });
    }, 1000);

    captureMessage("Payment form failed to load", "error");
  };

  const onPersonalFormSaved = () => {
    setFormStep(FormStep.Payment);

    if (!isPaymentFormLoadedSuccessfully && canDismiss) {
      showPaymentFormLoadError();
    }
  };

  const onPaymentFormLoaded = (allIframesLoaded: boolean) => {
    setCanDismiss(true);
    setIsPaymentFormLoadedSuccessfully(allIframesLoaded);
    if (formStep === FormStep.Payment) {
      showPaymentFormLoadError();
    }
  };

  const {
    data: initialData,
    isValidating,
    isLoading,
    mutate,
  } = usePaymentInformation();

  const onPaymentFormSaved = () => {
    send({
      type: OutgoingMessageTypes.SILENT_PROMPT,
      text: profileFormDoneMessage,
    });
    showToast({
      severity: "success",
      detail: "Payment details successfully updated!",
    });
    mutate();
    closeProfileForm();
  };

  return (
    <Popup
      onClose={canDismiss ? close : undefined}
      onBack={
        formStep === FormStep.Payment && canDismiss
          ? () => setFormStep(FormStep.Personal)
          : undefined
      }
      header={header}
      className="md:top-10 p-0 md:p-6 max-w-[54.5rem] h-full md:h-[90%] w-full md:max-h-[45.5rem]"
      panelRef={popupRef}
      data-testid={`profile-dialog-${profileForm}`}
    >
      <ScrollPanel className="h-[calc(100%-4rem)] md:h-[calc(100%-2rem)] p-4 md:p-0">
        <div className="flex md:pt-8 h-full">
          {isDesktop && <Checklist items={items} className="max-w-56 w-full" />}
          {formStep === FormStep.Personal && (
            <PersonalDetailsForm
              title={isMobile ? "Personal details" : "Personal information"}
              saveText="Save & continue"
              onCancel={isDesktop && canDismiss ? close : undefined}
              onSaved={onPersonalFormSaved}
              isHotel={profileForm === ProfileForms.HOTELS}
            />
          )}
          {formStep === FormStep.Payment && (isValidating || isLoading) && (
            <LoadingSpinner />
          )}
          {initialData && (
            <div
              className={clsx("w-full", {
                hidden: formStep !== FormStep.Payment,
              })}
            >
              <PaymentDetailsForm
                initialData={initialData}
                title={isDesktop ? "Payment details" : "Payment information"}
                onCancel={isDesktop ? close : undefined}
                saveText="Save & finish"
                onFormLoaded={onPaymentFormLoaded}
                onSaved={onPaymentFormSaved}
                onBack={() => setFormStep(FormStep.Personal)}
              />
            </div>
          )}
        </div>
      </ScrollPanel>
    </Popup>
  );
};
