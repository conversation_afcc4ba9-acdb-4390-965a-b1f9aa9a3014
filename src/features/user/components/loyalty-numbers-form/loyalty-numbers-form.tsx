import React, {
  use<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  use<PERSON><PERSON><PERSON>,
  use<PERSON>em<PERSON>,
} from "react";
import { <PERSON><PERSON> } from "primereact/button";
import {
  Configuration,
  SpotnanaProfile,
} from "@/features/user/types/profile-forms";
import useProfileFormToggle from "@/features/user/hooks/use-profile-form-switch";
import {
  IVGSCollectTextField,
  VGSCollectHttpStatusCode,
} from "@vgs/collect-js-react";
import {
  FREQUENT_FLYER_KEY,
  frequentFlyerPrograms,
  IATACode,
} from "@/features/user/constants/profile-forms";
import { ScrollPanel } from "primereact/scrollpanel";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import useToast from "@/common/hooks/use-toast";
import LoadingSpinner from "@/common/components/loading-spinner";
import {
  getFrequentFlyerFieldConfig,
  getFrequentF<PERSON>er<PERSON>ieldConfigu<PERSON>,
  getFrequent<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  getIATACodeFromAirline,
} from "@/features/user/utils/profile-form";
import { ApiPaths, HttpStatusCode } from "@/common/constants";
import VGSField from "../vgs-field";
import objectHasEntries from "@/common/utils/object-has-entries";
import { useVGSForm } from "../../hooks/use-VGS-form";
import { useUserProfile } from "../../hooks/api";
import PlusIcon from "@/common/components/icons/plus";
import { LoyaltyProgramSelection } from "../loyalty-program-selection";
import { DarkModeOptions } from "@/common/constants/theme";
import { darkModeAtom } from "@/common/store/dark-mode";
import { useAtomValue } from "jotai";

interface FrequentFlyerFormProps {
  onFormLoaded: VoidFunction;
}

function LoyaltyNumbersForm({ onFormLoaded }: FrequentFlyerFormProps) {
  const { closeProfileForm } = useProfileFormToggle();
  const [isSaving, setIsSaving] = useState(false);
  const [
    frequentFlyerFieldConfigurations,
    setFrequentFlyerFieldConfigurations,
  ] = useState<Configuration>({});
  const [airlineCodes, setAirlineCodes] = useState<IATACode[]>([]);
  const [newAirlineCodes, setNewAirlineCodes] = useState<IATACode[]>([]);
  const [autoFocus, setAutoFocus] = useState<boolean>();

  const { showToast } = useToast();

  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);

  const { preferences } = useUserProfile();

  const darkMode = useAtomValue(darkModeAtom);

  const allDisplayedAirlines = useMemo(
    () => [...airlineCodes, ...newAirlineCodes],
    [airlineCodes, newAirlineCodes]
  );

  const addAirlineField = useCallback(() => {
    const firstUnselectedAirline = Object.keys(frequentFlyerPrograms).find(
      (code) => !allDisplayedAirlines.includes(code as IATACode)
    );
    if (!firstUnselectedAirline) {
      return;
    }
    setNewAirlineCodes((prevValue) => [
      ...prevValue,
      firstUnselectedAirline as IATACode,
    ]);
    setAutoFocus(true);
  }, [allDisplayedAirlines]);

  const onLoadForm = useCallback(
    (spotnanaData: SpotnanaProfile) => {
      // Show only fields that have values
      const frequentFlyerNumbers =
        spotnanaData.payment_profile?.frequentFlyerNumbers?.filter(
          ({ number }) => number
        ) || [];

      const preferredAirlines = preferences?.preferred_airline_brands;
      let preferredAirlineCodes = Array.isArray(preferredAirlines)
        ? preferredAirlines.map(getIATACodeFromAirline).filter(Boolean)
        : [];

      // Codes from preferences are combined with codes already saved on the profile
      const allIATACodes = Array.from(
        new Set([
          ...preferredAirlineCodes,
          ...frequentFlyerNumbers.map(({ IATACode }) => IATACode),
        ])
      ).filter((code) => frequentFlyerPrograms[code]);
      const frequentFlyerFieldConfigurations =
        getFrequentFlyerFieldConfigurations(frequentFlyerNumbers, allIATACodes);

      setAirlineCodes(allIATACodes);
      setFrequentFlyerFieldConfigurations(frequentFlyerFieldConfigurations);

      if (!allIATACodes.length) {
        addAirlineField();
      }
    },
    [addAirlineField, preferences?.preferred_airline_brands]
  );

  const { form, isFormReady, isLoading, deleteField } = useVGSForm({
    onFormLoaded,
    fieldsCount: allDisplayedAirlines.length,
    onLoadForm,
  });

  const isSubmitDisabled = isLoading || !form || isSaving;

  const onRenderedField = (name: string | IATACode) => {
    const fieldId = getFrequentFlyerFieldKey(name as IATACode);
    const fieldConfig =
      frequentFlyerFieldConfigurations[fieldId] ||
      getFrequentFlyerFieldConfig(name as IATACode);

    form?.field(`#${fieldId}`, {
      ...fieldConfig,
      autoFocus,
      css: {
        color: darkMode === DarkModeOptions.DARK ? "white" : "#1D1C1D",
      },
    } as IVGSCollectTextField);
  };

  const onChangeAirlineProgram = (code: IATACode, newCode: IATACode) => {
    deleteField(getFrequentFlyerFieldKey(code));
    setNewAirlineCodes((prevValue) =>
      prevValue.map((value) => (value === code ? newCode : value))
    );
  };

  const onSubmitSuccess = (
    status: VGSCollectHttpStatusCode | null,
    data: any
  ) => {
    setIsSaving(false);

    if (status !== HttpStatusCode.OK) {
      showToast({
        severity: "error",
        detail:
          data?.detail?.[0]?.message ||
          (typeof data === "string" ? data : "An error occurred"),
      });
      return;
    }
    send({ type: OutgoingMessageTypes.AGENT_RESUME });
    closeProfileForm();
    showToast({
      severity: "success",
      detail: "Profile successfully updated!",
    });
  };

  const getDataToSubmit = (values: any) => {
    const frequentFlyerNumbers = Object.entries(values).reduce<any[]>(
      (fields, [key, value]) => [
        ...fields,
        { IATACode: key.replace(FREQUENT_FLYER_KEY, ""), number: value },
      ],
      []
    );

    return {
      frequentFlyerNumbers,
    };
  };

  const handleFormSubmit: MouseEventHandler<HTMLButtonElement> = (e) => {
    e.preventDefault();
    if (isSubmitDisabled) {
      return;
    }

    setIsSaving(true);
    form?.submit(
      ApiPaths.UPDATE_SPOTNANA_FLYER_NUMBER,
      {
        data: getDataToSubmit,
        withCredentials: true,
      },
      onSubmitSuccess
    );
  };

  return (
    <div className="h-full [&_iframe]:w-full">
      <form className="h-full">
        <ScrollPanel
          pt={{
            barY: { className: "translate-x-[calc(100%+.5rem)]" },
            root: {
              className: "h-[calc(100%-3.75rem)]",
            },
            content: { className: "pt-3" },
          }}
        >
          <h2>Airline</h2>
          <div className="sm:grid grid-cols-2 gap-x-2">
            {objectHasEntries(frequentFlyerFieldConfigurations) &&
              airlineCodes.map((IATACode) => (
                <VGSField
                  key={IATACode}
                  label={`${frequentFlyerPrograms[IATACode].airline} - ${frequentFlyerPrograms[IATACode].program}`}
                  name={getFrequentFlyerFieldKey(IATACode)}
                  onRendered={() => onRenderedField(IATACode)}
                />
              ))}
          </div>

          <div className="mt-4 sm:mt-0 space-y-4 sm:space-y-0">
            {newAirlineCodes?.map((code) => (
              <LoyaltyProgramSelection
                key={code}
                selectedLoyaltyCode={code}
                isDisabled={isLoading || !isFormReady}
                selectionsToHide={allDisplayedAirlines.filter(
                  (otherCode) => otherCode !== code
                )}
                initField={() => onRenderedField(code)}
                onChangeProgram={(newCode) =>
                  onChangeAirlineProgram(code, newCode)
                }
              />
            ))}
          </div>

          <Button
            text
            rounded
            size="small"
            onClick={addAirlineField}
            type="button"
            className="mt-1"
          >
            <PlusIcon />
          </Button>
        </ScrollPanel>

        <div className="mt-6 flex items-center justify-end gap-2.5">
          {isLoading || !isFormReady ? (
            <LoadingSpinner className="m-3" />
          ) : (
            <>
              <Button outlined type="button" onClick={closeProfileForm}>
                Cancel
              </Button>
              <Button
                type="submit"
                onClick={handleFormSubmit}
                disabled={isSubmitDisabled}
                loading={isSaving}
              >
                Save & continue
              </Button>
            </>
          )}
        </div>
      </form>
    </div>
  );
}

export default React.memo(LoyaltyNumbersForm);
