import { But<PERSON> } from "primereact/button";
import { Dialog } from "primereact/dialog";

interface CalendarSwitchDialogProps {
  visible: boolean;
  onHide: VoidFunction;
  onConfirm: VoidFunction;
}

export function CalendarSwitchDialog({
  visible,
  onHide,
  onConfirm,
}: CalendarSwitchDialogProps) {
  return (
    <Dialog visible={visible} onHide={onHide}>
      <div className="flex flex-col gap-4 items-center text-center mb-2">
        <h2 className="font-semibold text-xl leading-6">
          Confirm calendar access
        </h2>
        <p className="text-sm leading-4.5">
          For now you can select and connect only one calendar at a time.
        </p>
        <p className="text-sm leading-4.5">
          I will replace the existing calendar connection with this new one.
        </p>
      </div>
      <div className="w-full space-y-2.5">
        <Button
          label="Continue to new calendar"
          className="w-full"
          onClick={onConfirm}
        />
        <Button text label="Cancel" className="mx-auto" onClick={onHide} />
      </div>
    </Dialog>
  );
}
