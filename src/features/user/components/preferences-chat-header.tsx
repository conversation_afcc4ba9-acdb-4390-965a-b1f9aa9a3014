import clsx from "clsx";
import { Button } from "primereact/button";
import RightSidebars from "@/features/chat/types/right-sidebars";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { SettingsHeader } from "@/common/components/settings-header";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/common/constants";
import Header from "@/common/components/header";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import { preferencesInitMessage } from "@/features/chat/constants/messages";

export default function PreferencesChatHeader() {
  const { sendInitMessage } = useWebSocket(
    process.env.NEXT_PUBLIC_WS_URL as string
  );
  const { isMobile } = useViewportSize();
  const router = useRouter();
  const { rightSidebar, switchTo } = useRightSidebarSwitch();
  const isSidebarOpen = rightSidebar === RightSidebars.PREFERENCES;

  const onBack = () => {
    router.push(ROUTES.PROFILE);
    switchTo(null);
  };

  const ViewButton = (
    <Button
      className={clsx("gap-x-1 text-nowrap", {
        hidden: isSidebarOpen,
      })}
      onClick={() => switchTo(isSidebarOpen ? null : RightSidebars.PREFERENCES)}
      outlined
    >
      View<span className="hidden md:inline">&nbsp;saved preferences</span>
    </Button>
  );

  if (isMobile) {
    return (
      <SettingsHeader title="Travel preferences" onBack={onBack}>
        {ViewButton}
      </SettingsHeader>
    );
  }

  return (
    <Header
      title="Manage your travel preferences"
      showUserSelect
      onClearUserSelect={() =>
        sendInitMessage({ ...preferencesInitMessage, isOnboarding: false })
      }
    >
      {ViewButton}
    </Header>
  );
}
