import React, { useEffect } from "react";
import { But<PERSON> } from "primereact/button";
import { useForm } from "react-hook-form";
import { countriesList } from "@/features/user/constants/profile-forms";
import LoadingSpinner from "@/common/components/loading-spinner";
import <PERSON><PERSON>ield from "@/common/components/form/form-field";
import {
  HotelPersonalInformation,
  PersonalInformation,
} from "../types/profile-forms";
import { usePersonalInformation } from "../api/use-personal-information";
import FormSelect from "@/common/components/form/form-select";
import FormMultiSelect from "@/common/components/form/form-multiselect";
import regex from "@/common/constants/regex";
import Form<PERSON>askField from "@/common/components/form/form-mask-field";
import { useFormKeyboardScroll } from "@/common/hooks/use-form-keyboard-scroll";

interface PersonalDetailsFormProps {
  title?: string;
  saveText?: string;
  onCancel?: VoidFunction;
  onSaved?: VoidFunction;
  isHotel?: boolean;
}

const PersonalDetailsFormComponent = ({
  title = "Personal details",
  saveText = "Save",
  onCancel,
  onSaved,
  isHotel = false,
}: PersonalDetailsFormProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    control,
  } = useForm<PersonalInformation>();

  const {
    personalInformation,
    updatePersonalInformation,
    isMutating,
    isLoading,
    dropdownValues,
  } = usePersonalInformation();

  useFormKeyboardScroll();

  useEffect(() => {
    if (personalInformation) {
      Object.entries(personalInformation).forEach(([key, value]) => {
        setValue(key as keyof PersonalInformation, value);
      });
    }
  }, [personalInformation, setValue]);

  const onSubmit = async (
    data: PersonalInformation | HotelPersonalInformation
  ) => {
    const serializedData: typeof data = {
      ...data,
      phone: data.phone?.replaceAll("-", "") || null,
    };
    await updatePersonalInformation(serializedData);
    onSaved?.();
  };

  return (
    <form
      className="size-full max-w-[25rem] md:max-w-160 flex flex-col gap-6 justify-between"
      onSubmit={handleSubmit(onSubmit)}
    >
      <div className="grid grid-cols-1 md:grid-cols-10 gap-x-2 gap-y-6 overflow-y-auto md:overflow-y-visible">
        <h2 className="font-medium leading-5.5 md:text-xl col-span-full">
          {title}
        </h2>
        <FormSelect
          error={errors.title}
          label="Title"
          name="title"
          control={control}
          required
          placeholder="---"
          containerClassName="col-span-full md:col-span-2"
          options={dropdownValues?.title}
          appendTo="self"
          panelClassName="w-full !top-16"
        />
        <FormField
          error={errors.first_name}
          label="First name"
          {...register("first_name", { 
            required: "First name is required",
            pattern: {
              value: regex.name,
              message: "Must be an alpha character",
            },
          })}
          required
          placeholder="John"
          containerClassName="col-span-full md:col-span-4"
        />
        <FormField
          error={errors.last_name}
          label="Last name"
          {...register("last_name", { 
            required: "Last name is required",
            pattern: {
              value: regex.name,
              message: "Must be an alpha character",
            },
          })}
          required
          placeholder="Doe"
          containerClassName="col-span-full md:col-span-4"
        />
        <FormMaskField
          error={errors.phone}
          label="Phone number"
          {...register("phone", {
            required: "Phone number is required",
            pattern: {
              value: regex.phone,
              message: "Invalid phone number",
            },
          })}
          required
          mask="************"
          placeholder="************"
          inputMode="numeric"
          containerClassName="col-span-full"
        />
        {!isHotel && (
          <FormMaskField
            error={errors.dob}
            label="Date of birth"
            {...register("dob", {
              required: "Date of birth is required",
              pattern: {
                value: regex.date,
                message: "Invalid date",
              },
            })}
            required
            mask="99/99/9999"
            placeholder="mm/dd/yyyy"
            slotChar="mm/dd/yyyy"
            inputMode="numeric"
            containerClassName="col-span-full md:col-span-5"
          />
        )}
        {!isHotel && (
          <FormSelect
            error={errors.gender}
            label="Gender"
            name="gender"
            control={control}
            required
            placeholder="---"
            containerClassName="col-span-full md:col-span-5"
            options={dropdownValues?.gender}
            appendTo="self"
            panelClassName="w-full !top-16"
          />
        )}
        {!isHotel && (
          <FormField
            label="Known traveler number"
            {...register("traveler_number")}
            containerClassName="col-span-full md:col-span-5"
          />
        )}
        {!isHotel && (
          <FormField
            label="Redress number"
            {...register("redress_number")}
            containerClassName="col-span-full md:col-span-5"
          />
        )}
        {!isHotel && (
          <FormMultiSelect
            label="Citizenship"
            name="citizenship"
            control={control}
            options={countriesList}
            optionValue="code"
            optionLabel="name"
            placeholder=" "
            filter
            showClear
            display="chip"
            showSelectAll={false}
            containerClassName="col-span-full"
            appendTo="self"
            panelClassName="max-md:!top-[2.375rem] w-full"
            filterInputAutoFocus={false}
          />
        )}
      </div>
      <div
        className="sticky md:relative inset-x-0 bottom-0 bg-white dark:bg-gray-900 dark:md:bg-transparent /
      flex gap-4 justify-end"
      >
        {isLoading ? (
          <LoadingSpinner className="mx-auto md:mr-0" />
        ) : (
          <>
            {onCancel && (
              <Button onClick={onCancel} outlined>
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isMutating}
              loading={isMutating}
              className="w-full md:w-fit min-w-20 h-fit"
              data-testid="save-personal-details"
            >
              {saveText}
            </Button>
          </>
        )}
      </div>
    </form>
  );
};

export default React.memo(PersonalDetailsFormComponent);
