import arrayHasElements from "@/common/utils/array-has-elements";
import { SpotnanaProfile } from "../types/profile-forms";
import dayjs from "dayjs";
import IconExclamationSolid from "@/common/components/icons/exclamation-solid";
import colors from "@/common/constants/colors";

type AirlineCreditsTableProps = {
  data: SpotnanaProfile["airline_credits"];
};

export default function AirlineCreditsTable({
  data,
}: AirlineCreditsTableProps) {
  if (!arrayHasElements(data)) {
    return null;
  }

  return data.map((airline, index) => (
    <div className="mt-8 first:mt-0" key={index}>
      <h2 className="text-lg">{airline.label}</h2>
      <div className="sm:gap-4 sm:grid sm:auto-rows-[1fr] sm:grid-cols-2">
        {airline.credits.map((credit, index) => (
          <div
            className="border border-neutral-250 mt-4 p-4 rounded-lg sm:mt-0 dark:border-gray-900"
            key={index}
          >
            <div>
              <div className="text-xl font-bold text-green-550">
                ${credit.amount}
              </div>
              <div className="flex flex-col mt-2">
                <span className="font-bold text-sm text-neutral-500">
                  Expires
                </span>
                <span className="leading-none">
                  {dayjs(credit.expire_date).format("MMMM Do YYYY")}
                </span>
              </div>
            </div>
            {!!arrayHasElements(credit.restrictions) && (
              <div className="mt-4">
                {credit.restrictions?.map((restriction, index) => (
                  <div
                    className="flex items-center gap-x-2 text-sm"
                    key={index}
                  >
                    <IconExclamationSolid
                      fill={colors.amber[500]}
                      height={14}
                      width={14}
                    />
                    {restriction}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  ));
}
