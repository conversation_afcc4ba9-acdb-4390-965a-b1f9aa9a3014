import { UserRole } from "@/features/user/types/user";
import { useUserProfile } from "@/features/user/hooks/api";
import { userSelectDebugAtom } from "@/features/chat/store/debugging";
import Header from "@/common/components/header";
import UserSelect from "@/features/admin/components/user-select";
import { useOnboardingRedirect } from "@/features/chat/hooks/redirects";
import { useAtomValue } from "jotai";

export default function OnboardingChatHeader() {
  const userSelectDebug = useAtomValue(userSelectDebugAtom);
  const { role } = useUserProfile();

  const { onboardingRedirect } = useOnboardingRedirect();

  return (
    <Header>
      <h1 className="w-full overflow-hidden text-nowrap text-ellipsis">
        Welcome to Otto!
      </h1>
      {role === UserRole.ADMIN && userSelectDebug && (
        <UserSelect onClear={onboardingRedirect} />
      )}
    </Header>
  );
}
