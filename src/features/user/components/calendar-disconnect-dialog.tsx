import { But<PERSON> } from "primereact/button";
import { Dialog } from "primereact/dialog";

interface CalendarDisconnectDialogProps {
  visible: boolean;
  onHide: VoidFunction;
  onConfirm: VoidFunction;
}

export function CalendarDisconnectDialog({
  visible,
  onHide,
  onConfirm,
}: CalendarDisconnectDialogProps) {
  return (
    <Dialog visible={visible} onHide={onHide}>
      <p className="text-center">
        I will no longer have access to your calendar. You can re-enable it
        anytime
      </p>
      <Button label="Sounds good" onClick={onConfirm} className="w-full" />
    </Dialog>
  );
}
