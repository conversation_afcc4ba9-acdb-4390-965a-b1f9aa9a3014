import Image from "next/image";
import { Panel } from "primereact/panel";
import dayjs from "dayjs";

import { AirlineCredit as AirlineCreditType } from "@/features/user/types/airline-credits";
import { airlineLogos } from "@/common/constants/airline-logos";
import { CurrencySymbols } from "@/common/constants/currency";
import { CurrencyCodes } from "@/common/types/currency";
import clsx from "clsx";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faWarning } from "@fortawesome/free-solid-svg-icons";

type AirlineCreditProps = AirlineCreditType & {
  className?: string;
};

export default function AirlineCredit({
  airline_code,
  airline_name,
  credit,
  expire_date,
  expires_soon,
}: AirlineCreditProps) {
  const { amount, currency_code } = credit;

  return (
    <Panel pt={{ root: { className: "border" } }}>
      <div className="flex items-center gap-x-1.5">
        <Image
          alt={"Airline logo"}
          height={24}
          src={!!airline_code ? airlineLogos[airline_code] : ""}
          width={24}
        />
        <span className="text-sm">{airline_name}</span>
      </div>
      <div className="font-bold leading-10 mt-2.5 text-2xl">
        {CurrencySymbols[currency_code as CurrencyCodes]}
        {amount}
      </div>
      <div
        className={clsx(
          "leading-5 mt-2.5 text-sm",
          expires_soon ? "text-amber-500" : "text-neutral-500"
        )}
      >
        Expires
        {expires_soon && (
          <>
            {" soon"}
            <FontAwesomeIcon className="ml-1" icon={faWarning} />
          </>
        )}
      </div>
      <div className="leading-5 text-sm">
        {dayjs(expire_date).format("MMMM Do YYYY")}
      </div>
    </Panel>
  );
}
