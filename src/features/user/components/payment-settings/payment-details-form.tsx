"use client";

import React, { useState, use<PERSON><PERSON>back, ReactNode } from "react";
import { <PERSON><PERSON> } from "primereact/button";
import { paymentDetailsFieldsConfiguration } from "@/features/user/constants/profile-forms";
import { IVGSCollectForm, VGSCollectFormState } from "@vgs/collect-js-react";
import LoadingSpinner from "@/common/components/loading-spinner";
import useToast from "@/common/hooks/use-toast";
import { ApiPaths, HttpStatusCode } from "@/common/constants";
import { HttpStatusCode as VGSCollectHttpStatusCode } from "@vgs/collect-js-react/dist/types/HttpStatusCode";
import VGSField from "../vgs-field";
import { usePaymentFormFields } from "../../hooks/use-payment-form-fields";
import { usePaymentVGSForm } from "../../hooks/use-payment-VGS-form";
import { PaymentInformationResponse } from "../../types/payment-information";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { cardBrandsAllowlist } from "../../constants/cards";
import parseApiErrorResponse from "@/common/utils/parse-api-error-response";
import { useSession } from "@/common/api/use-session";

type PaymentDetailsFormProps = {
  initialData?: Partial<PaymentInformationResponse>;
  isCompanyCard?: boolean;
  onBack?: VoidFunction;
  onCancel?: VoidFunction;
  onFormLoaded?: (allIframesLoaded: boolean) => void;
  onSaved?: VoidFunction;
  saveText?: string;
  title?: string;
};

function PaymentDetailsForm({
  initialData = {},
  isCompanyCard,
  onBack,
  onCancel,
  onFormLoaded,
  onSaved,
  saveText = "Save",
  title,
}: PaymentDetailsFormProps) {
  const [isSaving, setIsSaving] = useState(false);
  const { showToast } = useToast();

  const { accessToken, refreshToken } = useSession();

  const {
    errors,
    initCardNumberField,
    initCVCField,
    initFields,
    setErrors,
    isCVCEdited,
    isCardNumberEdited,
  } = usePaymentFormFields();

  const fieldsCount = Object.keys(paymentDetailsFieldsConfiguration).length + 2; // Added 2 for the fields initialized separately

  const onLoadForm = useCallback(
    (form: IVGSCollectForm) => {
      initCardNumberField(form, initialData);
      initCVCField(form, initialData);
      initFields(form, paymentDetailsFieldsConfiguration, initialData);
    },
    [initCVCField, initCardNumberField, initFields, initialData]
  );

  const { form, isLoading, isFormReady } = usePaymentVGSForm({
    fieldsCount,
    onLoadForm,
    onFormLoaded,
  });
  const isSubmitDisabled = isLoading || isSaving || !isFormReady;

  const onSubmitSuccess = (
    status: VGSCollectHttpStatusCode | null,
    data: any
  ) => {
    setIsSaving(false);
    if (status === HttpStatusCode.OK) {
      onSaved?.();
    } else {
      let detail: ReactNode =
        parseApiErrorResponse(data) ?? "Failed to update payment details";

      showToast({
        severity: "error",
        detail,
      });
    }
  };

  const onSubmitError = (errors: VGSCollectFormState | null) => {
    setIsSaving(false);
    setErrors(errors);
    if (errors) {
      const errorFields = Object.keys(errors);
      errorFields.forEach((fieldName) => {
        const fieldElement = document.getElementById(fieldName);
        if (fieldElement) {
          fieldElement.classList.add("validation-shake");
          setTimeout(() => {
            fieldElement.classList.remove("validation-shake");
          }, 500);
        }
      });
      const errorIds = errorFields.map((key) => `label[for=${key}]`).join(", ");
      const firstElementWithError = document.querySelector(errorIds);
      firstElementWithError?.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleFormSubmit = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    if (isSubmitDisabled) return;
    setIsSaving(true);

    const cardType =
      form?.state?.card_number?.cardType ??
      initialData.payment_information?.card_type;

    form?.submit(
      ApiPaths.USER_PAYMENT_INFORMATION,
      {
        data: (values) => ({
          ...values,
          card_type:
            !!cardType && cardBrandsAllowlist.includes(cardType)
              ? cardType
              : null,
          isCardNumberEdited,
          isCVCEdited,
          isCompanyCard,
        }),
        headers: {
          "X-Access-Token": accessToken,
          "X-Refresh-Token": refreshToken,
        },
      },
      onSubmitSuccess,
      onSubmitError
    );
  };

  return (
    <form className="flex flex-col gap-6 size-full [&_iframe]:w-full">
      <div
        className="grid grid-cols-2 gap-y-6 gap-x-4 overflow-y-auto /
      md:overflow-y-visible"
      >
        {title && (
          <h2 className="font-medium leading-5.5 md:text-xl col-span-full">
            {title}
          </h2>
        )}
        <VGSField
          error={errors?.card_nickname}
          label="Card nickname"
          name="card_nickname"
          className="col-span-full md:col-span-1"
        />
        <VGSField
          error={errors?.cardholder_name}
          label="Cardholder's name"
          name="cardholder_name"
          required
          className="col-span-full md:col-span-1"
        />

        <VGSField
          error={errors?.card_number}
          label="Credit card number"
          name="card_number"
          required
          className="col-span-full"
        />

        <VGSField
          error={errors?.exp_date}
          label="Expiration date"
          name="exp_date"
          required
          className="col-span-full md:col-span-1"
        />
        <VGSField
          error={errors?.card_cvc}
          label="CVV"
          required
          name="card_cvc"
          className="col-span-full md:col-span-1"
        />

        <VGSField
          error={errors?.address}
          label="Billing address"
          name="address"
          required
          className="col-span-full"
        />

        <VGSField
          error={errors?.city}
          label="City"
          name="city"
          required
          className="col-span-full md:col-span-1"
        />
        <VGSField
          error={errors?.state}
          label="State"
          name="state"
          required
          className="col-span-full md:col-span-1"
        />

        <VGSField
          error={errors?.zip_code}
          label="Billing zip code"
          name="zip_code"
          required
          className="col-span-full"
        />
      </div>

      <div
        className="sticky md:relative inset-x-0 bottom-0 bg-white dark:bg-gray-900 dark:md:bg-transparent /
      flex gap-4 justify-end"
      >
        {isLoading ? (
          <LoadingSpinner className="shrink-0 mx-auto md:mr-0" />
        ) : (
          <>
            {onBack && (
              <Button
                onClick={(event) => {
                  event.preventDefault();
                  onBack();
                }}
                text
                className="mr-auto"
              >
                <FontAwesomeIcon icon={["far", "arrow-left"]} />
                Back
              </Button>
            )}
            {onCancel && (
              <Button onClick={onCancel} outlined>
                Cancel
              </Button>
            )}
            <Button
              className="max-md:w-full h-fit"
              disabled={isSubmitDisabled}
              loading={isSaving}
              onClick={handleFormSubmit}
              type="submit"
              data-testid="save-payment-details"
            >
              {saveText}
            </Button>
          </>
        )}
      </div>
    </form>
  );
}

export default React.memo(PaymentDetailsForm);
