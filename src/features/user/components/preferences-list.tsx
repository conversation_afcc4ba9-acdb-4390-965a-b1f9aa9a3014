import { Button } from "primereact/button";
import colors from "@/common/constants/colors";
import IconClose from "@/common/components/icons/close";
import { UserPreferences } from "@/features/user/types/user";
import { ClockIcon } from "@/common/components/icons/clock";
import dayjs from "dayjs";

type PreferencesListProps = {
  preferences: UserPreferences;
  canEdit?: boolean;
  onDeletePreference?: (key: string, value: string) => void;
};

function formatPreferenceString(key: string, value: string) {
  const capitalizedFirstLetter = key[0].toUpperCase();
  const restKeyString = key.slice(1).replaceAll("_", " ");

  return `${capitalizedFirstLetter}${restKeyString}: ${value}`;
}

export function PreferencesList({
  preferences,
  canEdit = false,
  onDeletePreference,
}: PreferencesListProps) {
  const { updated_at, ...restPreferences } = preferences ?? {};
  return (
    <>
      <ul className="w-full">
        {!!restPreferences &&
          Object.entries(restPreferences).map(
            ([preferenceKey, value], keyIndex) => {
              if (!value) {
                return;
              }

              const valuesArray: string[] = Array.isArray(value)
                ? value
                : [value];

              return valuesArray.map((value, preferenceIndex) => (
                <li
                  className="bg-gray-100 flex gap-x-4 items-start justify-between mt-3 px-4 py-3 rounded-lg w-full dark:bg-gray-900"
                  key={`${keyIndex}${preferenceIndex}`}
                >
                  {formatPreferenceString(preferenceKey, value)}
                  {canEdit && onDeletePreference && (
                    <Button
                      className="basis-4 px-0 py-1"
                      plain
                      onClick={() => onDeletePreference(preferenceKey, value)}
                    >
                      <IconClose
                        height={16}
                        width={16}
                        fill={colors.neutral[350]}
                      />
                    </Button>
                  )}
                </li>
              ));
            }
          )}
      </ul>
      {updated_at && (
        <div className="flex items-center gap-x-1 mt-3">
          <ClockIcon />
          <i className="text-[0.625rem] leading-4">
            Last updated on {dayjs.utc(updated_at).local().format("MM/DD/YYYY")}
          </i>
        </div>
      )}
    </>
  );
}
