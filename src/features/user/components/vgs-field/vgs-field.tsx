"use client";

import clsx from "clsx";
import { VGSFieldProps } from "@/features/user/types/profile-forms";
import arrayHasElements from "@/common/utils/array-has-elements";
import { useLayoutEffect, useRef } from "react";

export default function VGSField({
  className,
  error,
  label,
  name,
  required,
  onRendered = () => {},
}: VGSFieldProps) {
  const { errorMessages } = error ?? {};
  const hasError = arrayHasElements(errorMessages);
  const hasRendered = useRef(false);

  // <PERSON> needed to know when to inject the iframe and to do it only once
  useLayoutEffect(() => {
    if (!hasRendered.current) {
      onRendered();
      hasRendered.current = true;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={clsx("grid relative w-full", className)}>
      <label
        className={clsx("font-semibold leading-4 mb-1.5 text-xs", {
          "text-red-500": hasError,
        })}
        htmlFor={name}
      >
        {label}
        {!!required && <span className="text-red-500">&nbsp;*</span>}
      </label>
      <div
        id={name}
        className={clsx(
          "border border-neutral-250 h-10 pt-1.5 px-2.5 rounded [&_iframe]:h-7 dark:border-[#646464]",
          { "border-red-500": hasError }
        )}
      />
      {hasError && (
        <p
          className="absolute bottom-0 first-letter:capitalize left-1 text-xs text-red-500 /
        translate-y-full"
        >
          {(errorMessages as string[])[0]}
        </p>
      )}
    </div>
  );
}
