import { Dropdown } from "primereact/dropdown";
import { useMemo } from "react";
import { frequentFlyerPrograms, IATACode } from "../../constants/profile-forms";
import VGSField from "../vgs-field";
import { getFrequentFlyer<PERSON>ieldKey } from "../../utils/profile-form";

interface LoyaltyProgramSelectionProps {
  isDisabled?: boolean;
  selectionsToHide?: IATACode[];
  selectedLoyaltyCode: IATACode;
  initField: VoidFunction;
  onChangeProgram: (value: IATACode) => void;
  filterAlliance?: boolean;
}

export default function LoyaltyProgramSelection({
  isDisabled,
  selectionsToHide,
  selectedLoyaltyCode,
  initField,
  onChangeProgram,
  filterAlliance,
}: LoyaltyProgramSelectionProps) {
  const selectedOption = frequentFlyerPrograms[selectedLoyaltyCode];

  const getOptions = (programs: {
    [key: string]: (typeof frequentFlyerPrograms)[IATACode];
  }) =>
    Object.entries(programs).map(([code, { airline, program, alliance }]) => ({
      program: `${airline} - ${program}`,
      code,
      alliance,
    }));

  const loyaltyPrograms = useMemo(() => {
    if (!selectedOption) {
      return [];
    }

    if (filterAlliance && selectedOption.alliance == null) {
      return getOptions({ [selectedLoyaltyCode]: selectedOption });
    }

    return getOptions(frequentFlyerPrograms).filter(
      ({ code, alliance }) =>
        !selectionsToHide?.includes(code as IATACode) &&
        (filterAlliance ? alliance === selectedOption.alliance : true)
    );
  }, [filterAlliance, selectedLoyaltyCode, selectedOption, selectionsToHide]);

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 items-end sm:gap-2">
      <div>
        <p className="font-semibold leading-4 mb-1 text-xs mt-0.5">
          Add New Air Program
        </p>
        <Dropdown
          placeholder="Airline loyalty program"
          options={loyaltyPrograms}
          value={selectedLoyaltyCode}
          filter
          onChange={(e) => onChangeProgram(e.value)}
          optionLabel="program"
          optionValue="code"
          disabled={isDisabled || !selectedOption}
          checkmark
          className="h-10"
        />
      </div>
      <VGSField
        key={selectedLoyaltyCode}
        label="Add New Number"
        name={getFrequentFlyerFieldKey(selectedLoyaltyCode)}
        onRendered={initField}
      />
    </div>
  );
}
