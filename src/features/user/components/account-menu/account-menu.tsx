import { use<PERSON>tomValue } from "jotai";
import clsx from "clsx";
import { DivProps } from "@/common/types/html-elements-props";
import { TutorialSteps } from "@/features/tutorial/types/tutorial-types";
import { useLogOut, useUserProfile } from "@/features/user/hooks/api";
import { useStoreToggle } from "@/common/hooks/toggles";
import { accountMenuAtom, showSidebarAtom } from "@/common/store/sidebar";
import { tutorialStepAtom } from "@/features/tutorial/store/tutorial-store";
import Accordion from "@/common/components/accordion";
import { InterfaceSettingsAccordion } from "@/features/user/components/account-menu";
import LoadingSpinner from "@/common/components/loading-spinner";
import IconSignOut from "@/common/components/icons/sign-out";
import BackDrop from "@/common/components/backdrop";
import AccountMenuButton from "./account-menu-button";
import Link from "next/link";
import { ROUTES } from "@/common/constants";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { Avatar } from "primereact/avatar";
import { agentsDebugAtom } from "@/features/chat/store/debugging";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCreditCard } from "@fortawesome/free-regular-svg-icons";
import { useViewportSize } from "@/common/hooks/use-viewport-size";

export default function AccountMenu({ className }: DivProps) {
  const { isLoading, profile, isAdmin, isCompanyAdmin } = useUserProfile();
  const { user_name, user_email, profile_picture, id } = profile ?? {};
  const agentsDebug = useAtomValue(agentsDebugAtom);
  const tutorialStep = useAtomValue(tutorialStepAtom);
  const isAccountMenuStep = tutorialStep === TutorialSteps.ACCOUNT_MENU;
  const { isMobile } = useViewportSize();

  const {
    toggle: toggleMenu,
    turnOff: closeMenu,
    value: isMenuOpen,
  } = useStoreToggle(accountMenuAtom);
  const { turnOff: closeSidebar } = useStoreToggle(showSidebarAtom);
  const { closeRightSidebar } = useRightSidebarSwitch();

  const { isLoggingOut, triggerLogout } = useLogOut({ onSuccess: closeMenu });

  const closeOverlays = () => {
    closeMenu();
    closeRightSidebar();
    closeSidebar();
  };

  // Server side user_name is already resolve between preferred name, first name.
  // Could still be empty for first time OTP users.
  const userNameFirstLetter =
    user_name && user_name.trim().length > 0
      ? user_name.trim()[0]
      : user_email?.trim()?.[0] || "-";
  let userProfilePicture = profile_picture;
  if (
    !!userNameFirstLetter &&
    userProfilePicture?.includes("static/default-avatar-icon.jpg")
  ) {
    // let's not show the default avatar if we have some form of name
    userProfilePicture = undefined;
  }

  const accordionHeader = isLoading ? (
    <LoadingSpinner className="w-6" />
  ) : (
    <>
      <Avatar
        image={userProfilePicture}
        label={userNameFirstLetter}
        shape="circle"
      />
      <div className="truncate">{user_name || user_email}</div>
      {id != null && agentsDebug && (
        <span className="text-xs text-gray-500 ml-2 dark:text-neutral-400">
          {id}
        </span>
      )}
    </>
  );

  return (
    <>
      <div
        className={clsx("mt-auto absolute bottom-6 w-full px-6", className, {
          "z-20": isMenuOpen,
        })}
      >
        {!!isAccountMenuStep && <BackDrop className="z-10" />}
        <Accordion
          contentOutside
          isOpen={isMenuOpen}
          onToggle={toggleMenu}
          pt={{
            content: {
              className: "absolute bottom-full",
            },
            header: {
              className: "h-6",
              render: accordionHeader,
            },
            wrapper: {
              className: clsx("relative", {
                "pointer-events-none z-10": isAccountMenuStep,
              }),
            },
          }}
        >
          <div
            className="flex flex-col gap-y-2 pb-2"
            data-tooltip-id={TutorialSteps.ACCOUNT_MENU}
          >
            {isAdmin && (
              <>
                <Link href={ROUTES.ADMIN_TRIPS} onClick={closeOverlays}>
                  <AccountMenuButton
                    icon={
                      <FontAwesomeIcon icon="gear" className="text-red-500" />
                    }
                    onClick={closeOverlays}
                  >
                    Admin panel
                  </AccountMenuButton>
                </Link>
                <InterfaceSettingsAccordion />
              </>
            )}
            {isAdmin && (
                <Link
                  href={ROUTES.FLIGHT_EVALUATION}
                  onClick={closeOverlays}
                >
                  <AccountMenuButton
                    icon={
                      <FontAwesomeIcon
                        icon="plane"
                        className="text-red-500"
                      />
                    }
                  >
                    Flight Evaluation
                  </AccountMenuButton>
                </Link>
            )}      
            {isCompanyAdmin && (
              <>
                <Link
                  href={
                    isMobile
                      ? ROUTES.ADMIN_SETTINGS
                      : ROUTES.ADMIN_SETTINGS_DOMAINS
                  }
                >
                  <AccountMenuButton
                    icon={
                      <FontAwesomeIcon
                        icon={["far", "grid-horizontal"]}
                        className="text-primary-500"
                      />
                    }
                  >
                    Admin settings
                  </AccountMenuButton>
                </Link>
              </>
            )}
            <Link
              href={isMobile ? ROUTES.PROFILE : ROUTES.PERSONAL_INFO}
              onClick={closeOverlays}
            >
              <AccountMenuButton
                icon={
                  <FontAwesomeIcon
                    icon={["far", "circle-user"]}
                    className="text-primary-500"
                  />
                }
              >
                User profile
              </AccountMenuButton>
            </Link>

            <Link
              href={isMobile ? ROUTES.PAYMENT : ROUTES.PAYMENT_DETAILS}
              onClick={closeOverlays}
            >
              <AccountMenuButton
                icon={
                  <FontAwesomeIcon
                    className="text-primary-500"
                    icon={faCreditCard}
                  />
                }
              >
                Payments
              </AccountMenuButton>
            </Link>
            <Link href={ROUTES.TRAVEL_POLICY} onClick={closeOverlays}>
              <AccountMenuButton
                icon={
                  <FontAwesomeIcon
                    icon="shield-check"
                    className="text-primary-500"
                  />
                }
              >
                Travel policy
              </AccountMenuButton>
            </Link>
            <Link href={ROUTES.SETTINGS} onClick={closeOverlays}>
              <AccountMenuButton
                icon={
                  <FontAwesomeIcon icon="gear" className="text-primary-500" />
                }
              >
                Settings
              </AccountMenuButton>
            </Link>
            <AccountMenuButton icon={<IconSignOut />} onClick={triggerLogout}>
              Sign out
              {isLoggingOut && (
                <LoadingSpinner className="ml-auto w-8 h-auto" />
              )}
            </AccountMenuButton>
          </div>
        </Accordion>
      </div>
      {isMenuOpen && (
        <BackDrop
          className={clsx({
            "pointer-events-none": isAccountMenuStep,
          })}
          absolute
          onClick={closeMenu}
        />
      )}
    </>
  );
}
