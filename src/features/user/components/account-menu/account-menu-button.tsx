import clsx from "clsx";
import { Button, ButtonProps } from "primereact/button";

export default function AccountMenuButton({
  className,
  children,
  ...restProps
}: ButtonProps) {
  return (
    <Button
      {...restProps}
      className={clsx(
        "bg-white gap-x-3 h-max justify-start px-4 py-3 shadow-md-12% text-base text-gray-900 w-full",
        "dark:bg-gray-800 dark:shadow-md-12%-light dark:text-white",
        className
      )}
    >
      {children}
    </Button>
  );
}
