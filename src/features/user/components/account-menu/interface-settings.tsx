import { useAtomValue, useSet<PERSON><PERSON> } from "jotai";
import { But<PERSON> } from "primereact/button";
import { InputSwitch } from "primereact/inputswitch";
import { isDevelopment, isStaging } from "@/common/utils/env";
import { ROUTES } from "@/common/constants";
import {
  futureTripsInitMessage,
  preferencesInitMessage,
} from "@/features/chat/constants/messages";
import { initialForeignTrips } from "@/features/admin/constants/foreign-trips";
import { useCurrentRoute } from "@/common/hooks/use-current-route";
import { useRedirectToLastTrip } from "@/features/chat/hooks/redirects";
import { useDeleteAccount } from "@/features/user/hooks/api";
import { useTripsList } from "@/common/hooks/api";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import { useStoreToggle } from "@/common/hooks/toggles";
import {
  agentsDebug<PERSON>tom,
  enableDeleteMessageFlag<PERSON>tom,
  flightCardsDebug<PERSON>tom,
  rawToolOutputDebugAtom,
  travelContextDebugAtom,
  userSelectDebugAtom,
} from "@/features/chat/store/debugging";
import { ottoReplyingAtom } from "@/features/chat/store/chat";
import { foreignTripsAtom } from "@/features/admin/store/trips";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import { currentTripAtom } from "@/features/chat/store/current-trip";
import Accordion from "@/common/components/accordion";
import {
  ENABLE_MEMORY_KEY,
  ENABLE_SAVED_TRIP_KEY,
} from "../../constants/feature-flags";
import { useUpdateMemoryFeatureFlag } from "@/features/feature-flags/hooks/api";
import {
  enableMemoryFlagAtom,
  enableSavedTripFlagAtom,
  enableUpdatedScrollBehaviourFlagAtom,
} from "../../store/feature-flags";
import { useState } from "react";
import ThreadImportModal from "@/features/admin/components/thread-import-modal";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useReset } from "@/features/chat/hooks/use-reset";

export function InterfaceSettingsAccordion() {
  const { triggerDelete } = useDeleteAccount();
  const redirectToLastTrip = useRedirectToLastTrip();
  const currentRoute = useCurrentRoute();
  const { data: trips } = useTripsList();
  const [importModalVisible, setImportModalVisible] = useState(false);

  const currentTrip = useAtomValue(currentTripAtom);
  const setForeignTrips = useSetAtom(foreignTripsAtom);
  const setForeignUser = useSetAtom(foreignUserAtom);
  const setOttoReplying = useSetAtom(ottoReplyingAtom);

  const { sendInitMessage } = useWebSocket(
    process.env.NEXT_PUBLIC_WS_URL as string
  );

  const { value: agentsDebug, toggle: toggleAgentsDebug } =
    useStoreToggle(agentsDebugAtom);
  const { value: flightCardsDebug, toggle: toggleFlightCardsDebug } =
    useStoreToggle(flightCardsDebugAtom);
  const { value: travelContextDebug, toggle: toggleTravelContextDebug } =
    useStoreToggle(travelContextDebugAtom);
  const { value: rawToolOutputDebug, toggle: toggleRawToolOutputDebug } =
    useStoreToggle(rawToolOutputDebugAtom);
  const { value: userSelectDebug, toggle: toggleUserSelectDebug } =
    useStoreToggle(userSelectDebugAtom);
  const { value: enableMemoryFlag, toggle: toggleEnableMemoryFlag } =
    useStoreToggle(enableMemoryFlagAtom);
  const { value: enableSavedTripFlag, toggle: toggleEnableSavedTripFlag } =
    useStoreToggle(enableSavedTripFlagAtom);
  const {
    value: enableUpdatedScrollBehaviour,
    toggle: toggleEnableUpdatedScrollBehaviour,
  } = useStoreToggle(enableUpdatedScrollBehaviourFlagAtom);
  const {
    value: enableDeleteMessageFlag,
    toggle: toggleEnableDeleteMessageFlag,
  } = useStoreToggle(enableDeleteMessageFlagAtom);

  const { updateFeatureFlag } = useUpdateMemoryFeatureFlag();

  const { resetChatHistory } = useReset();

  const clearForeignUser = () => {
    resetChatHistory();
    setOttoReplying(true);
    setForeignTrips(initialForeignTrips);
    setForeignUser(null);
    switch (currentRoute) {
      case ROUTES.FUTURE_TRIPS:
        sendInitMessage({ ...futureTripsInitMessage, isOnboarding: false });
        break;
      case ROUTES.PREFERENCES:
        sendInitMessage({ ...preferencesInitMessage, isOnboarding: false });
        break;
      case ROUTES.TRIPS:
        const isOwnTrip = [
          ...(trips?.booked ?? []),
          ...(trips?.planned ?? []),
        ].some((trip) => trip.id === currentTrip);
        if (!isOwnTrip) {
          redirectToLastTrip();
        }
        break;
    }
  };

  const handleMemoryToggle = (e: { value: boolean }) => {
    toggleEnableMemoryFlag();
    updateFeatureFlag({
      enabled: e.value,
      feature_flag: ENABLE_MEMORY_KEY,
    });
  };

  const handleSavedTripToggle = (e: { value: boolean }) => {
    toggleEnableSavedTripFlag();
    updateFeatureFlag({
      enabled: e.value,
      feature_flag: ENABLE_SAVED_TRIP_KEY,
    });
  };

  const settingLabelClassName =
    "leading-6 text-neutral-600 text-sm dark:text-neutral-400";

  return (
    <Accordion
      pt={{
        header: {
          render: (
            <>
              <FontAwesomeIcon icon="gear" className="text-red-500" />
              Interface settings
            </>
          ),
        },
      }}
    >
      <div className="flex justify-between items-center">
        <span className={settingLabelClassName}>Agents debugging</span>
        <InputSwitch
          checked={agentsDebug}
          onChange={() => toggleAgentsDebug()}
        />
      </div>
      <div className="flex justify-between items-center mt-2">
        <span className={settingLabelClassName}>Flight cards debugging</span>
        <InputSwitch
          checked={flightCardsDebug}
          onChange={() => toggleFlightCardsDebug()}
        />
      </div>
      <div className="flex justify-between items-center mt-2">
        <span className={settingLabelClassName}>Travel context debugging</span>
        <InputSwitch
          checked={travelContextDebug}
          onChange={() => toggleTravelContextDebug()}
        />
      </div>

      <div className="flex justify-between items-center mt-2">
        <span className={settingLabelClassName}>Raw tool output debugging</span>
        <InputSwitch
          checked={rawToolOutputDebug}
          onChange={() => toggleRawToolOutputDebug()}
        />
      </div>

      <div className="flex justify-between items-center mt-2">
        <span className={settingLabelClassName}>Show user select</span>
        <InputSwitch
          checked={userSelectDebug}
          onChange={() => {
            userSelectDebug && clearForeignUser();
            toggleUserSelectDebug();
          }}
        />
      </div>

      <div className="flex justify-between items-center mt-2">
        <span className={settingLabelClassName}>Enable memory</span>
        <InputSwitch checked={enableMemoryFlag} onChange={handleMemoryToggle} />
      </div>

      <div className="flex justify-between items-center mt-2">
        <span className={settingLabelClassName}>Enable saved trip</span>
        <InputSwitch
          checked={enableSavedTripFlag}
          onChange={handleSavedTripToggle}
        />
      </div>

      {(isDevelopment() || isStaging()) && (
        <div className="flex justify-between items-center mt-2">
          <span className={settingLabelClassName}>Enable delete message</span>
          <InputSwitch
            checked={enableDeleteMessageFlag}
            onChange={toggleEnableDeleteMessageFlag}
          />
        </div>
      )}

      <div className="flex justify-between items-center mt-2">
        <span className={settingLabelClassName}>
          Enable updated scroll behaviour
        </span>
        <InputSwitch
          checked={enableUpdatedScrollBehaviour}
          onChange={toggleEnableUpdatedScrollBehaviour}
        />
      </div>

      <Button className="mt-4" onClick={() => setImportModalVisible(true)}>
        Import Thread
      </Button>

      <Button className="mt-4 bg-red-600" onClick={triggerDelete}>
        Delete account
      </Button>

      <ThreadImportModal
        visible={importModalVisible}
        onHide={() => setImportModalVisible(false)}
      />
    </Accordion>
  );
}
