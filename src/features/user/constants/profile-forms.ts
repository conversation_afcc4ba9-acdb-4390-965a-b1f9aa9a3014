import regex from "@/common/constants/regex";
import { Configuration } from "../types/profile-forms";

export const FREQUENT_FLYER_KEY = "frequent_flyer_number_";

export const VGSFieldCSS = {
  fontSize: "16px",
  lineHeight: "20px",
  "&::placeholder": { color: "#DADADA" },
};

export const commonFieldConfigurations: Configuration = {
  first_name: {
    type: "text",
    name: "first_name",
    placeholder: "<PERSON>",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "given-name",
  },
  last_name: {
    type: "text",
    name: "last_name",
    placeholder: "Doe",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "family-name",
  },
  phone: {
    type: "text",
    name: "phone",
    placeholder: "************",
    validations: ["required", String(regex.phone)],
    css: VGSFieldCSS,
    autoComplete: "tel",
    mask: { format: "************" },
    serializers: [{ name: "replace", options: { old: "-", new: "" } }],
  },
  cardholder_name: {
    type: "text",
    name: "cardholder_name",
    placeholder: "John Doe",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "cc-name",
  },
  exp_date: {
    type: "card-expiration-date",
    name: "exp_date",
    placeholder: "12/2028",
    validations: ["required", "validCardExpirationDate"],
    yearLength: 4,
    css: VGSFieldCSS,
    autoComplete: "cc-exp",
  },
  address: {
    type: "text",
    name: "address",
    placeholder: "12345 Elm Street",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "street-address",
  },
  city: {
    type: "text",
    name: "city",
    placeholder: "Seattle",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "address-level2",
  },
  state: {
    type: "text",
    name: "state",
    placeholder: "WA",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "address-level1",
  },
  zip_code: {
    type: "text",
    name: "zip_code",
    placeholder: "12345",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "postal-code",
  },
};

export const securedFieldConfigurations: Configuration = {
  card_number: {
    type: "card-number",
    name: "card_number",
    placeholder: "4111 1111 1111 1111",
    validations: ["required", "validCardNumber"],
    css: VGSFieldCSS,
    autoComplete: "cc-number",
    showCardIcon: {
      right: "0",
    },
  },
  card_cvc: {
    type: "card-security-code",
    name: "card_cvc",
    placeholder: "111",
    validations: ["required", "validCardSecurityCode"],
    css: VGSFieldCSS,
    autoComplete: "cc-csc",
    showCardIcon: {
      right: "0",
    },
  },
};

export const paymentDetailsFieldsConfiguration: Configuration = {
  card_nickname: {
    name: "card_nickname",
    type: "text",
    css: VGSFieldCSS,
  },
  cardholder_name: {
    type: "text",
    name: "cardholder_name",
    placeholder: "John Doe",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "cc-name",
  },
  exp_date: {
    type: "card-expiration-date",
    name: "exp_date",
    placeholder: "12/2028",
    validations: ["required", "validCardExpirationDate"],
    yearLength: 4,
    css: VGSFieldCSS,
    autoComplete: "cc-exp",
  },
  address: {
    type: "text",
    name: "address",
    placeholder: "12345 Elm Street",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "street-address",
  },
  city: {
    type: "text",
    name: "city",
    placeholder: "Seattle",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "address-level2",
  },
  state: {
    type: "text",
    name: "state",
    placeholder: "WA",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "address-level1",
  },
  zip_code: {
    type: "text",
    name: "zip_code",
    placeholder: "12345",
    validations: ["required"],
    css: VGSFieldCSS,
    autoComplete: "postal-code",
  },
};

export const hotelsFieldConfigurations: Configuration = {
  // marriott_bonvoy_account_number: {
  //   type: "text",
  //   name: "marriott_bonvoy_account_number",
  //   placeholder: "*********",
  //   css: VGSFieldCSS,
  // },
};

export const flightsFieldConfigurations: Configuration = {
  title: {
    type: "dropdown",
    name: "title",
    validations: ["required"],
    css: VGSFieldCSS,
  },
  dob: {
    type: "text",
    name: "dob",
    validations: ["required", String(regex.date)],
    placeholder: "mm/dd/yyyy",
    css: VGSFieldCSS,
    autoComplete: "bday",
    mask: {
      format: "M9/D9/9999",
      maskChar: null,
      formatChar: { M: "[0-1]", D: "[0-3]", 9: "[0-9]" },
    },
  },
  gender: {
    type: "dropdown",
    name: "gender",
    validations: ["required"],
    css: VGSFieldCSS,
  },
  traveler_number: {
    type: "text",
    name: "traveler_number",
    placeholder: "*********",
    css: VGSFieldCSS,
  },
  redress_number: {
    type: "text",
    name: "redress_number",
    placeholder: "*********",
    css: VGSFieldCSS,
  },
};

export enum AirlineAlliance {
  StarAlliance,
  SkyTeam,
  Oneworld,
}

// NK is missing
export const frequentFlyerPrograms = {
  "3L": {
    airline: "Air Arabia Abu Dhabi",
    program: "AirRewards",
    alliance: null,
  },
  "4I": { airline: "Air Antilles", program: "e-Smiles", alliance: null },
  "4Y": {
    airline: "Eurowings Discover",
    program: "Miles & More",
    alliance: null,
  },
  "5Z": { airline: "CemAir", program: "SKYREWARDS", alliance: null },
  "6E": { airline: "IndiGo Airlines", program: "6E Rewards", alliance: null },
  "7H": { airline: "Ravn Alaska", program: "FlyCoin", alliance: null },
  "9K": { airline: "Cape Air", program: "TravelPass", alliance: null },
  "9N": { airline: "Tropic Air", program: "TROPICMILES", alliance: null },
  "9W": {
    airline: "Jet Airways (India)",
    program: "InterMiles",
    alliance: null,
  },
  A3: {
    airline: "Aegean Airlines",
    program: "Miles&Bonus",
    alliance: AirlineAlliance.StarAlliance,
  },
  AA: {
    airline: "American Airlines",
    program: "AA Advantage Program",
    alliance: AirlineAlliance.Oneworld,
  },
  AC: {
    airline: "Air Canada",
    program: "Aeroplan",
    alliance: AirlineAlliance.StarAlliance,
  },
  AD: {
    airline: "Azul Brazilian Airlines",
    program: "TudoAzul",
    alliance: null,
  },
  AF: {
    airline: "Air France",
    program: "Flying Blue",
    alliance: AirlineAlliance.SkyTeam,
  },
  AI: {
    airline: "Air India Limited",
    program: "Flying Returns",
    alliance: AirlineAlliance.StarAlliance,
  },
  AM: {
    airline: "Aeroméxico",
    program: "Club Premier",
    alliance: AirlineAlliance.SkyTeam,
  },
  AR: {
    airline: "Aerolíneas Argentinas",
    program: "Aerolíneas Plus",
    alliance: AirlineAlliance.SkyTeam,
  },
  AS: {
    airline: "Alaska Airlines, Inc.",
    program: "Mileage Plan",
    alliance: AirlineAlliance.Oneworld,
  },
  AT: {
    airline: "Royal Air Maroc",
    program: "Safar Flyer Loyalty",
    alliance: AirlineAlliance.Oneworld,
  },
  AV: {
    airline: "Avianca",
    program: "LifeMiles",
    alliance: AirlineAlliance.StarAlliance,
  },
  AY: {
    airline: "Finnair",
    program: "Finnair Plus",
    alliance: AirlineAlliance.Oneworld,
  },
  AZ: {
    airline: "Alitalia",
    program: "Volare",
    alliance: AirlineAlliance.SkyTeam,
  },
  B0: { airline: "La Compagnie", program: "MyCompagnie", alliance: null },
  B6: { airline: "JetBlue Airways", program: "TrueBlue", alliance: null },
  BA: {
    airline: "British Airways",
    program: "Executive Club (Avios)",
    alliance: AirlineAlliance.Oneworld,
  },
  BR: {
    airline: "EVA Air",
    program: "Infinity MileageLands",
    alliance: AirlineAlliance.StarAlliance,
  },
  C2: {
    airline: "CEIBA Intercontinental",
    program: "CEIBA MILE",
    alliance: null,
  },
  CA: {
    airline: "Air China",
    program: "Air China Companion/Phoenix Miles",
    alliance: AirlineAlliance.StarAlliance,
  },
  CI: {
    airline: "China Airlines",
    program: "Dynasty Flyer",
    alliance: AirlineAlliance.SkyTeam,
  },
  CM: {
    airline: "Copa Airlines",
    program: "ConnectMiles",
    alliance: AirlineAlliance.StarAlliance,
  },
  CX: {
    airline: "Cathay Pacific",
    program: "Marco Polo Club",
    alliance: AirlineAlliance.Oneworld,
  },
  CZ: {
    airline: "China Southern Airlines",
    program: "Sky Pearl Club",
    alliance: null,
  },
  DL: {
    airline: "Delta Air Lines",
    program: "SkyMiles",
    alliance: AirlineAlliance.SkyTeam,
  },
  DY: {
    airline: "Norwegian Air Shuttle",
    program: "Norwegian Reward",
    alliance: null,
  },
  EI: { airline: "Aer Lingus", program: "AerClub", alliance: null },
  EK: { airline: "Emirates", program: "Skywards", alliance: null },
  ET: {
    airline: "Ethiopian Airlines",
    program: "ShebaMiles",
    alliance: AirlineAlliance.StarAlliance,
  },
  EW: { airline: "Eurowings", program: "Boomerang Club", alliance: null },
  EY: { airline: "Etihad Airways", program: "Etihad Guest", alliance: null },
  F9: {
    airline: "Frontier Airlines",
    program: "Frontier Miles",
    alliance: null,
  },
  FA: { airline: "Safair", program: "FlyMore Club", alliance: null },
  FI: {
    airline: "Icelandair",
    program: "Icelandair Saga Club",
    alliance: null,
  },
  FJ: { airline: "Fiji Airways", program: "Tabua Club", alliance: null },
  FZ: { airline: "Flydubai", program: "Emirates Skywards", alliance: null },
  G3: {
    airline: "Gol Transportes Aéreos",
    program: "GOL Smiles",
    alliance: null,
  },
  GA: {
    airline: "Garuda Indonesia",
    program: "GarudaMiles",
    alliance: AirlineAlliance.SkyTeam,
  },
  GQ: { airline: "Sky Express", program: "SKY experience+", alliance: null },
  HA: {
    airline: "Hawaiian Airlines",
    program: "HawaiianMiles",
    alliance: null,
  },
  IB: {
    airline: "Iberia Airlines",
    program: "Iberia Plus (Avios)",
    alliance: AirlineAlliance.Oneworld,
  },
  JL: {
    airline: "Japan Airlines",
    program: "JMB",
    alliance: AirlineAlliance.Oneworld,
  },
  K7: {
    airline: "Mingalar Aviation Services",
    program: "Sky Smile Program",
    alliance: null,
  },
  KE: {
    airline: "Korean Air",
    program: "Sky Pass",
    alliance: AirlineAlliance.SkyTeam,
  },
  KL: {
    airline: "KLM",
    program: "Flying Blue",
    alliance: AirlineAlliance.SkyTeam,
  },
  KQ: {
    airline: "Kenya Airways",
    program: "Asante Rewards",
    alliance: AirlineAlliance.SkyTeam,
  },
  LA: { airline: "LATAM Chile", program: "LATAM Pass", alliance: null },
  LH: {
    airline: "Lufthansa",
    program: "Miles & More",
    alliance: AirlineAlliance.StarAlliance,
  },
  LM: { airline: "Loganair", program: "Clan Loganair", alliance: null },
  LO: {
    airline: "LOT Polish Airlines",
    program: "Miles & More",
    alliance: AirlineAlliance.StarAlliance,
  },
  LS: { airline: "Jet2", program: "myJet2", alliance: null },
  LU: { airline: "LATAM Airlines", program: "LATAM Pass", alliance: null },
  LX: {
    airline: "Swiss International Air Lines",
    program: "Miles & More",
    alliance: AirlineAlliance.StarAlliance,
  },
  LY: {
    airline: "El Al Israel Airlines",
    program: "EL AL Matmid Frequent Flyer Club",
    alliance: null,
  },
  M0: { airline: "Aero Mongolia", program: "Skymiles", alliance: null },
  ME: {
    airline: "Middle East Airlines",
    program: "Cedar Miles",
    alliance: AirlineAlliance.SkyTeam,
  },
  MF: {
    airline: "Xiamen Airlines",
    program: "Egret Card",
    alliance: AirlineAlliance.SkyTeam,
  },
  MH: {
    airline: "Malaysia Airlines",
    program: "Enrich",
    alliance: AirlineAlliance.Oneworld,
  },
  MS: {
    airline: "Egyptair",
    program: "EgyptAir Plus",
    alliance: AirlineAlliance.StarAlliance,
  },
  MU: {
    airline: "China Eastern Airlines",
    program: "Eastern Miles",
    alliance: AirlineAlliance.SkyTeam,
  },
  MX: {
    airline: "Breeze Airways",
    program: "Breeze Points Reward Program",
    alliance: null,
  },
  NE: {
    airline: "Nesma Airlines",
    program: "Almosafer Points",
    alliance: null,
  },
  NH: {
    airline: "All Nippon Airways",
    program: "ANA Mileage Club",
    alliance: AirlineAlliance.StarAlliance,
  },
  NZ: {
    airline: "Air New Zealand",
    program: "Airpoints Membership",
    alliance: AirlineAlliance.StarAlliance,
  },
  OK: {
    airline: "Czech Airlines",
    program: "OK Plus",
    alliance: AirlineAlliance.SkyTeam,
  },
  OS: {
    airline: "Austrian Airlines",
    program: "Miles & More",
    alliance: AirlineAlliance.StarAlliance,
  },
  OU: {
    airline: "Croatia Airlines",
    program: "Miles & More",
    alliance: AirlineAlliance.StarAlliance,
  },
  OZ: {
    airline: "Asiana Airlines",
    program: "Asiana Club",
    alliance: AirlineAlliance.StarAlliance,
  },
  PD: { airline: "Porter Airlines", program: "VIPorter", alliance: null },
  PR: {
    airline: "Philippine Airlines",
    program: "Mabuhay Miles",
    alliance: null,
  },
  QF: {
    airline: "Qantas",
    program: "Qantas Frequent Flyer",
    alliance: AirlineAlliance.Oneworld,
  },
  QG: { airline: "Citilink", program: "LinkMiles", alliance: null },
  QR: {
    airline: "Qatar Airways",
    program: "Privilege Club",
    alliance: AirlineAlliance.Oneworld,
  },
  RJ: {
    airline: "Royal Jordanian",
    program: "Royal Club",
    alliance: AirlineAlliance.Oneworld,
  },
  RO: {
    airline: "Tarom",
    program: "Flying Blue",
    alliance: AirlineAlliance.SkyTeam,
  },
  S1: {
    airline: "Saurya Airlines",
    program: "Saurya Saarathi",
    alliance: null,
  },
  SA: {
    airline: "South African Airways",
    program: "Voyager",
    alliance: AirlineAlliance.StarAlliance,
  },
  SG: { airline: "Spicejet", program: "SpiceClub", alliance: null },
  SK: {
    airline: "Scandinavian Airlines",
    program: "SAS Eurobonus",
    alliance: AirlineAlliance.StarAlliance,
  },
  SN: {
    airline: "Brussels Airlines",
    program: "Miles & More",
    alliance: AirlineAlliance.StarAlliance,
  },
  SQ: {
    airline: "Singapore Airlines",
    program: "PPS Club/ KrisFlyer",
    alliance: AirlineAlliance.StarAlliance,
  },
  SU: {
    airline: "Aeroflot Russian Airlines",
    program: "Aeroflot Bonus",
    alliance: AirlineAlliance.SkyTeam,
  },
  SV: {
    airline: "Saudia",
    program: "Al-Fursan",
    alliance: AirlineAlliance.SkyTeam,
  },
  T0: { airline: "Avianca Perú", program: "LifeMiles", alliance: null },
  TG: {
    airline: "Thai Airways International",
    program: "Royal Orchid Plus",
    alliance: AirlineAlliance.StarAlliance,
  },
  TK: {
    airline: "Turkish Airlines",
    program: "Miles & Smiles",
    alliance: AirlineAlliance.StarAlliance,
  },
  TN: { airline: "Air Tahiti Nui", program: "Club Tiare", alliance: null },
  TP: {
    airline: "TAP Portugal",
    program: "Victoria",
    alliance: AirlineAlliance.StarAlliance,
  },
  U2: { airline: "easyJet", program: "Flight Club", alliance: null },
  UA: {
    airline: "United Airlines",
    program: "Mileage Plus",
    alliance: AirlineAlliance.StarAlliance,
  },
  UK: { airline: "Vistara", program: "Club Vistara", alliance: null },
  UL: {
    airline: "SriLankan Airlines",
    program: "FlySmiLes",
    alliance: AirlineAlliance.Oneworld,
  },
  UX: {
    airline: "Air Europa",
    program: "Flying Blue",
    alliance: AirlineAlliance.SkyTeam,
  },
  VA: {
    airline: "Virgin Australia",
    program: "Velocity Frequent Flyer",
    alliance: null,
  },
  VN: {
    airline: "Vietnam Airlines",
    program: "Golden Lotus Plus",
    alliance: AirlineAlliance.SkyTeam,
  },
  VS: { airline: "Virgin Atlantic", program: "Virgin Red", alliance: null },
  W4: {
    airline: "Wizz Air Malta",
    program: "Wizz Discount Club",
    alliance: null,
  },
  WN: {
    airline: "Southwest Airlines",
    program: "Southwest Airlines Rapid Rewards",
    alliance: null,
  },
  WS: { airline: "WestJet", program: "WestJet Rewards", alliance: null },
  WY: { airline: "Oman Air", program: "Sindbad", alliance: null },
  X5: {
    airline: "Air Europa Express",
    program: "Air Europa SUMA",
    alliance: null,
  },
  YP: { airline: "Air Premia", program: "Premia", alliance: null },
  Z0: {
    airline: "Norse Atlantic UK",
    program: "Norwegian Reward",
    alliance: null,
  },
  ZH: {
    airline: "Shenzhen Airlines",
    program: "PhoenixMiles",
    alliance: AirlineAlliance.StarAlliance,
  },
};

export type IATACode = keyof typeof frequentFlyerPrograms;

import countries from "i18n-iso-countries";
import enLocale from "i18n-iso-countries/langs/en.json";

countries.registerLocale(enLocale);

export const countriesList = Object.entries(countries.getNames("en"))
  .filter(([code, name]) => code && name)
  .map(([code, name]) => ({
    name,
    code,
  }));
