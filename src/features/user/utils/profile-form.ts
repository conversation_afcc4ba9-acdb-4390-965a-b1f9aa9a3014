import {
  FREQUENT_FLYER_KEY,
  frequentFlyerPrograms,
  IATACode,
  VGSFieldCSS,
} from "@/features/user/constants/profile-forms";
import {
  Configuration,
  FrequentFlyerNumber,
} from "@/features/user/types/profile-forms";

export const getFrequentFlyerFieldKey = (code: IATACode) =>
  `${FREQUENT_FLYER_KEY}${code}`;

export const getIATACodeFromAirline = (airline: string) =>
  Object.entries(frequentFlyerPrograms).find(
    ([_, value]) => airline === value.airline
  )?.[0] as IATACode;

export const getFrequentFlyerFieldConfig = (
  IATACode: IATACode,
  number?: string | null
) => ({
  type: "text",
  name: getFrequent<PERSON>lyer<PERSON>ield<PERSON>ey(IATACode),
  placeholder: "123456789",
  css: VGSFieldCSS,
  ...(number && { defaultValue: number }),
});

export const getFrequentFlyerFieldConfigurations = (
  initialValues: FrequentFlyerNumber[],
  frequentFlyerIATACodes: IATACode[]
): Configuration => {
  const initialConfig: Configuration = initialValues?.reduce(
    (configuration, { IATACode, number }) => ({
      ...configuration,
      [getFrequentFlyerFieldKey(IATACode)]: getFrequentFlyerFieldConfig(
        IATACode,
        number
      ),
    }),
    {}
  );

  return frequentFlyerIATACodes.reduce((configuration, code) => {
    const key = getFrequentFlyerFieldKey(code);
    if (!configuration[key]) {
      configuration[key] = getFrequentFlyerFieldConfig(code);
    }
    return configuration;
  }, initialConfig);
};
