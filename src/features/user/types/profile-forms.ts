import { VGSCollectStateParams } from "@vgs/collect-js-react";
import { CSSProperties } from "react";
import { IATACode } from "@/features/user/constants/profile-forms";

export enum ProfileForms {
  PROFILE_UPDATE_AGENT = "profile_update_agent",
  FLIGHTS = "flights",
  HOTELS = "hotels",
}

export type VGSFieldProps = {
  className?: string;
  error?: VGSCollectStateParams;
  label: string;
  name: string;
  required?: boolean;
  onRendered?: VoidFunction;
};

export type VGSFieldError = {
  errorMessages: string[];
  errors: {
    code: number;
    description: string;
    details: object;
    message: string;
  }[];
  isDirty: boolean;
  isEmpty: boolean;
  isFocused: boolean;
  isTouched: boolean;
  isValid: boolean;
  name: string;
};

export interface FlyerProgramOption {
  program: string;
  code: string;
}

export interface FrequentFlyerNumber {
  IATACode: IATACode;
  number: string | null;
}

export interface AirlineCredit {
  label: string;
  credits: {
    amount: number;
    currency_code: string;
    expire_date: string;
    restrictions: string[];
  }[];
}

export interface SpotnanaDropdownValues {
  gender: { value: string; text: string }[];
  title: { value: string; text: string }[];
}

export interface SpotnanaProfile {
  airline_credits: AirlineCredit[];
  dropdown_values: SpotnanaDropdownValues;
  payment_profile: {
    address: string;
    card_cvc: string;
    card_number: string;
    cardholder_name: string;
    city: string;
    dob: string;
    email: string;
    exp_date: string;
    first_name: string;
    gender: string;
    last_name: string;
    frequentFlyerNumbers?: FrequentFlyerNumber[];
    phone: string;
    redress_number: string;
    state: string;
    title: string;
    traveler_number: string;
    users_id: number;
    zip_code: string;
  } | null;
}

export interface FieldConfiguration {
  type: string;
  name: string;
  placeholder?: string;
  validations?: string[];
  css?: CSSProperties;
  mask?: {
    format: string;
    maskChar?: string | null;
    formatChar?: object;
  };
  autoComplete?: string;
  yearLength?: number;
  showCardIcon?: boolean | CSSProperties;
  serializers?: object[];
  defaultValue?: string;
}

export interface Configuration {
  [key: string]: FieldConfiguration;
}

export interface PersonalInformation {
  title: string | null;
  first_name: string | null;
  last_name: string | null;
  phone: string | null;
  dob: string | null;
  gender: string | null;
  traveler_number: string | null;
  redress_number: string | null;
  citizenship: string[] | null;
}

export type HotelPersonalInformation = Pick<
  PersonalInformation,
  "title" | "first_name" | "last_name" | "phone"
>;
