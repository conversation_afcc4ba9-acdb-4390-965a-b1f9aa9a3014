import { TravelPolicyData } from "@/features/travel-policy/types/travel-policy";

export type User = {
  firstName?: string;
  lastName?: string;
};

export enum UserRole {
  ADMIN = "admin",
  COMPANY_ADMIN = "company_admin",
  USER = "user",
}

export type UserPreferences = Record<string, string | string[]> & {
  updated_at: string | null;
};

export type UserProfile = {
  id: number;
  user_name: string;
  user_email: string;
  profile_picture: string;
  role: UserRole;
  new_trip_count?: number;
  preferred_name?: string;
  first_name?: string;
  last_name?: string;
  citizenship?: string[];
  organization_id?: number;
};

export type UserProfileResponse = {
  userPreferences: UserPreferences;
  userProfile: UserProfile;
  userCompanyTravelPolicy: TravelPolicyData;
  shouldShowSampleTrips: boolean;
  shouldShowDontShowAgain: boolean;
  microsoft_calendar_enabled: boolean;
  google_calendar_enabled: boolean;
};
