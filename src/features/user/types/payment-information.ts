export type PaymentInformationResponse = {
  payment_information: {
    address: string;
    card_cvc: string;
    card_number: string;
    card_type: string;
    cardholder_name: string;
    city: string;
    exp_date: string;
    isCVCEdited: boolean | null;
    isCardNumberEdited: boolean | null;
    state: string;
    users_id: number;
    zip_code: string;
  };
  has_company_payment_information?: boolean | null;
};

export type VGSFieldState = {
  cardType: string | undefined;
  errorMessages: string[];
  isDirty: boolean;
  isEmpty: boolean;
  isFocused: boolean;
  isTouched: boolean;
  isValid: boolean;
  name: string;
};
