import { atom } from "jotai";
import { ProfileForms } from "@/features/user/types/profile-forms";
import { IATACode } from "../constants/profile-forms";

export const profileformPopupAtom = atom<ProfileForms | null | undefined>(null);

export const frequentFlyerIATACodesAtom = atom<IATACode[]>([]);

export const profileFormCloseMessageAtom = atom<string>("");
export const profileFormDoneMessageAtom = atom<string>("");
