import {
  ENABLE_MEMORY_KEY,
  ENABLE_SAVED_TRIP_KEY,
  ENABLE_UPDATED_SCROLL_BEHAVIOUR_KEY,
} from "@/features/user/constants/feature-flags";
import { atomWithStorage } from "jotai/utils";

export const enableMemoryFlagAtom = atomWithStorage(ENABLE_MEMORY_KEY, false);
export const enableSavedTripFlagAtom = atomWithStorage(
  ENABLE_SAVED_TRIP_KEY,
  false
);
export const enableUpdatedScrollBehaviourFlagAtom = atomWithStorage(
  ENABLE_UPDATED_SCROLL_BEHAVIOUR_KEY,
  false
);
