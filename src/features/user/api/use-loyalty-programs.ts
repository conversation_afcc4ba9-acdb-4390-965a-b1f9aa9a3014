import useSWR from "swr";

import { LoyaltyProgramsResponse } from "@/features/user/types/loyalty-programs";
import { ApiPaths } from "@/common/constants";
import { fetcher } from "@/common/api/fetcher";

export default function useLoyaltyPrograms() {
  return useSWR<LoyaltyProgramsResponse>(
    ApiPaths.USER_LOYALTY_PROGRAMS,
    fetcher,
    {
      revalidateIfStale: true,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );
}
