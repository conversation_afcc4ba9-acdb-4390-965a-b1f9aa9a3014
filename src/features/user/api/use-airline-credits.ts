import useSWR from "swr";
import { AirlineCreditsResponse } from "@/features/user/types/airline-credits";
import { ApiPaths } from "@/common/constants";
import { fetcher } from "@/common/api/fetcher";

export default function useAirlineCredits() {
  return useSWR<AirlineCreditsResponse>(
    ApiPaths.USER_AIRLINE_CREDITS,
    fetcher,
    {
      revalidateIfStale: false,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );
}
