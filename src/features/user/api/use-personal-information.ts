import { ApiPaths } from "@/common/constants";
import useS<PERSON> from "swr";
import useSWRMutation from "swr/mutation";
import {
  HotelPersonalInformation,
  PersonalInformation,
  SpotnanaDropdownValues,
} from "../types/profile-forms";
import { fetcher } from "@/common/api/fetcher";
import { ApiRestMethod } from "@/common/constants";
import useToast from "@/common/hooks/use-toast";

export function usePersonalInformation() {
  const { showToast, showErrorToast } = useToast();

  const { data, isLoading } = useSWR<{
    dropdown_values: SpotnanaDropdownValues;
    personal_information: PersonalInformation;
  }>(ApiPaths.PERSONAL_INFORMATION);
  const { trigger, isMutating } = useSWRMutation(
    ApiPaths.PERSONAL_INFORMATION,
    (
      url: string,
      { arg }: { arg: PersonalInformation | HotelPersonalInformation }
    ) =>
      fetcher(url, {
        arg: {
          options: {
            method: ApiRestMethod.POST,
            body: JSON.stringify(arg),
          },
        },
      }),
    {
      onSuccess: () => {
        showToast({
          severity: "success",
          detail: "Personal details successfully updated!",
        });
      },
      onError: (error) => {
        showErrorToast(error, "Failed to update personal details");
      },
    }
  );

  return {
    personalInformation: data?.personal_information,
    dropdownValues: data?.dropdown_values,
    isLoading,
    isMutating,
    updatePersonalInformation: trigger,
  };
}
