import useSWR from "swr";

import { ApiPaths } from "@/common/constants";
import { fetcher } from "@/common/api/fetcher";

export default function usePaymentInformation(isCompanyCard: boolean = false) {
  return useSWR(
    `${ApiPaths.USER_PAYMENT_INFORMATION}${
      isCompanyCard ? "?is_company_card=true" : ""
    }`,
    fetcher,
    {
      revalidateIfStale: false,
      revalidateOnFocus: false,
    }
  );
}
