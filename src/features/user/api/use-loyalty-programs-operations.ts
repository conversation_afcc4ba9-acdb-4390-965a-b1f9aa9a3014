import useSWRMutation from "swr/mutation";

import {
  LoyaltyFormFields,
  LoyaltyProgramType,
} from "@/features/user/types/loyalty-programs";
import { fetcher } from "@/common/api/fetcher";
import { ApiPaths, ApiRestMethod } from "@/common/constants";
import useLoyaltyPrograms from "@/features/user/api/use-loyalty-programs";

export default function useLoyaltyProgramsOperations() {
  const { mutate: getLoyaltyPrograms } = useLoyaltyPrograms();

  const { isMutating: isMutatingFlights, trigger: triggerFlightRequest } =
    useSWRMutation(ApiPaths.USER_FLIGHTS_LOYALTY_PROGRAMS, fetcher);

  const { isMutating: isMutatingHotels, trigger: triggerHotelRequest } =
    useSWRMutation(ApiPaths.USER_HOTELS_LOYALTY_PROGRAMS, fetcher);

  return {
    addLoyaltyProgram(
      { number, program }: LoyaltyFormFields,
      type: LoyaltyProgramType
    ) {
      const isFlight = type === "flight";
      const trigger = (options: RequestInit) =>
        isFlight
          ? triggerFlightRequest({ options })
          : triggerHotelRequest({ options });
      const body = JSON.stringify({
        IATACode: isFlight ? program : undefined,
        number,
        program: isFlight ? undefined : program,
      });
      return trigger({
        body,
        method: ApiRestMethod.POST,
      }).then(getLoyaltyPrograms);
    },

    removeLoyaltyProgram(program: string, type: LoyaltyProgramType) {
      const isFlight = type === "flight";
      const trigger = (program: string) => {
        const triggerArgs = {
          options: { method: ApiRestMethod.DELETE },
          urlEnding: `?${isFlight ? "IATACode" : "program"}=${program}`,
        };

        return isFlight
          ? triggerFlightRequest(triggerArgs)
          : triggerHotelRequest(triggerArgs);
      };

      return trigger(program).then(getLoyaltyPrograms);
    },
    isMutating: isMutatingFlights || isMutatingHotels,
  };
}
