import useSWRMutation from "swr/mutation";
import { PaymentInformationResponse } from "@/features/user/types/payment-information";
import { ApiPaths } from "@/common/constants";
import { fetcher } from "@/common/api/fetcher";

export default function useGetPaymentInformation() {
  const { isMutating, trigger } = useSWRMutation<PaymentInformationResponse>(
    ApiPaths.USER_PAYMENT_INFORMATION,
    fetcher
  );

  return {
    getPaymentInformation: trigger,
    isLoading: isMutating,
  };
}
