import { ApiPaths, ROUTES } from "@/common/constants";
import { fetcher } from "@/common/api/fetcher";
import { ApiRestMethod } from "@/common/constants";
import useSWRMutation from "swr/mutation";
import useToast from "@/common/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useUserProfile } from "../hooks/api";
import { OutgoingJSBridgeEvents } from "@/common/types/js-bridge-events";
import { isNativeBridgeAtom } from "@/common/store/nativebridge";
import { useAtomValue } from "jotai";
import * as nativebridge from "@nrk/nativebridge";
import { useEffect } from "react";

const postCalendarConnect = (
  url: string,
  {
    arg,
  }: {
    arg: {
      enable: boolean;
    };
  }
) =>
  fetcher(url, {
    arg: {
      options: {
        method: ApiRestMethod.POST,
        body: JSON.stringify(arg),
      },
    },
  });

export function useCalendarConnect() {
  const router = useRouter();
  const { showErrorToast } = useToast();
  const { mutateUserProfile } = useUserProfile();
  const isNativeBridge = useAtomValue(isNativeBridgeAtom);

  const onGoogleCalendarSuccess = (response?: {
    enabled: boolean;
    calendarAuthUrl?: string;
  }) => {
    if (!response?.enabled && response?.calendarAuthUrl) {
      // Should enable
      if (isNativeBridge) {
        router.push(ROUTES.PREFERENCES);
        setTimeout(() => {
          try {
            nativebridge.emit(OutgoingJSBridgeEvents.REQUEST_CALENDAR_ACCESS);
          } catch (e) {}
        }, 1000);
      } else {
        router.push(response.calendarAuthUrl);
      }
    } else {
      mutateUserProfile();
    }
  };
  const onMicrosoftCalendarSuccess = (response?: {
    enabled: boolean;
    calendarAuthUrl?: string;
  }) => {
    if (!response?.enabled && response?.calendarAuthUrl) {
      // Should enable
      router.push(response.calendarAuthUrl);
    } else {
      mutateUserProfile();
    }
  };

  const onError = (error: Error) =>
    showErrorToast(error, "Failed to connect calendar");

  const { trigger: connectGoogleCalendar, isMutating: isLoadingGoogleConnect } =
    useSWRMutation(ApiPaths.GOOGLE_CALENDAR_CONNECT, postCalendarConnect, {
      onSuccess: onGoogleCalendarSuccess,
      onError,
    });
  const {
    trigger: connectMicrosoftCalendar,
    isMutating: isLoadingMicrosoftConnect,
  } = useSWRMutation(ApiPaths.MICROSOFT_CALENDAR_CONNECT, postCalendarConnect, {
    onSuccess: onMicrosoftCalendarSuccess,
    onError,
  });

  // Have fresh data on first render
  useEffect(() => {
    mutateUserProfile();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    connectGoogleCalendar,
    connectMicrosoftCalendar,
    isLoading: isLoadingGoogleConnect || isLoadingMicrosoftConnect,
  };
}
