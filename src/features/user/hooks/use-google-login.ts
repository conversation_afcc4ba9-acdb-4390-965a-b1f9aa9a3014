// Fixed issues in the library hook: https://github.com/MomenSherif/react-oauth/blob/master/packages/%40react-oauth/google/src/hooks/useGoogleLogin.ts

import {
  CodeClientConfig,
  CodeResponse,
  NonOAuthError,
  OverridableTokenClientConfig,
  TokenClientConfig,
  TokenResponse,
  useGoogleOAuth,
} from "@react-oauth/google";
import { useCallback, useEffect, useRef } from "react";
import { usePathname } from "next/navigation";

interface CommonFlowOptions {
  onNonOAuthError?: (nonOAuthError: NonOAuthError) => void;
  overrideScope?: boolean;
  redirect_uri?: string;
  login_hint?: string;
  hd?: string;
  select_account?: boolean;
  prompt?: "" | "select_account" | "none" | "consent";
}

interface ImplicitFlowOptions
  extends Omit<TokenClientConfig, "client_id" | "scope" | "callback">,
    CommonFlowOptions {
  onSuccess?: (
    tokenResponse: Omit<
      TokenResponse,
      "error" | "error_description" | "error_uri"
    >
  ) => void;
  onError?: (
    errorResponse: Pick<
      TokenResponse,
      "error" | "error_description" | "error_uri"
    >
  ) => void;
  scope?: TokenClientConfig["scope"];
}

interface AuthCodeFlowOptions
  extends Omit<CodeClientConfig, "client_id" | "scope" | "callback">,
    CommonFlowOptions {
  onSuccess?: (
    codeResponse: Omit<
      CodeResponse,
      "error" | "error_description" | "error_uri"
    >
  ) => void;
  onError?: (
    errorResponse: Pick<
      CodeResponse,
      "error" | "error_description" | "error_uri"
    >
  ) => void;
  scope?: CodeResponse["scope"];
}

export type UseGoogleLoginOptionsImplicitFlow = {
  flow?: "implicit";
} & ImplicitFlowOptions;

export type UseGoogleLoginOptionsAuthCodeFlow = {
  flow?: "auth-code";
} & AuthCodeFlowOptions;

export type UseGoogleLoginOptions =
  | UseGoogleLoginOptionsImplicitFlow
  | UseGoogleLoginOptionsAuthCodeFlow;

export default function useGoogleLogin({
  flow = "implicit",
  scope = "",
  onSuccess,
  onError,
  onNonOAuthError,
  overrideScope,
  state,
  ...props
}: UseGoogleLoginOptions) {
  const pathname = usePathname();
  const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();
  const clientRef = useRef<any>();

  const onSuccessRef = useRef(onSuccess);
  onSuccessRef.current = onSuccess;

  const onErrorRef = useRef(onError);
  onErrorRef.current = onError;

  const onNonOAuthErrorRef = useRef(onNonOAuthError);
  onNonOAuthErrorRef.current = onNonOAuthError;

  useEffect(() => {
    if (!scriptLoadedSuccessfully || !clientId) return;

    const clientMethod =
      flow === "implicit" ? "initTokenClient" : "initCodeClient";

    const client = window?.google?.accounts?.oauth2[clientMethod]({
      client_id: clientId,
      scope: overrideScope ? scope : `openid profile email ${scope}`,
      //   @ts-ignore
      callback: (response: TokenResponse | CodeResponse) => {
        if (response.error) return onErrorRef.current?.(response);

        onSuccessRef.current?.(response as any);
      },
      error_callback: (nonOAuthError: NonOAuthError) => {
        onNonOAuthErrorRef.current?.(nonOAuthError);
      },
      state,
      ...props,
    });

    clientRef.current = client;
  }, [
    clientId,
    scriptLoadedSuccessfully,
    flow,
    scope,
    state,
    overrideScope,
    props,
  ]);

  const loginImplicitFlow = useCallback(
    (overrideConfig?: OverridableTokenClientConfig) =>
      clientRef.current?.requestAccessToken(overrideConfig),
    []
  );

  const loginAuthCodeFlow = useCallback(
    () => clientRef.current?.requestCode(),
    []
  );

  const generateAuthUrl = useCallback(
    ({ loginHint }: { loginHint?: string } = {}) => {
      const baseUrl = "https://accounts.google.com/o/oauth2/v2/auth";
      const currentUrl = window.location.origin + pathname;
      const login_hint = loginHint || props.login_hint;
      const params = new URLSearchParams({
        client_id: clientId || "",
        redirect_uri: props.redirect_uri || currentUrl,
        response_type: flow === "implicit" ? "token" : "code",
        scope: overrideScope ? scope : `openid profile email ${scope}`,
        include_granted_scopes: (
          props.include_granted_scopes ?? true
        ).toString(),
        access_type: "offline",
        ...((props.prompt && !login_hint) && { prompt: props.prompt }),
        ...(state && { state }),
        ...(login_hint && { login_hint }),
        ...(props.hd && { hd: props.hd }),
        ...(props.select_account && { select_account: "true" }),
      });

      return `${baseUrl}?${params.toString()}`;
    },
    [
      pathname,
      clientId,
      props.redirect_uri,
      props.include_granted_scopes,
      props.prompt,
      props.login_hint,
      props.hd,
      props.select_account,
      flow,
      overrideScope,
      scope,
      state,
    ]
  );

  return {
    login: flow === "implicit" ? loginImplicitFlow : loginAuthCodeFlow,
    generateAuthUrl,
  };
}
