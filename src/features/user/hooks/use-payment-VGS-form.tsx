import {
  IVGSCollectForm,
  VGSCollectFormState,
  VGSCollectVaultEnvironment,
} from "@vgs/collect-js-react";
import { useCallback, useEffect, useRef, useState } from "react";
import useToast from "@/common/hooks/use-toast";
import { loadVGSCollect } from "@vgs/collect-js";

interface VGSFormProps {
  fieldsCount: number;
  onLoadForm?: (form: IVGSCollectForm, citizneship?: string[]) => void;
  onFormLoaded?: (allIframesLoaded: boolean) => void;
}

export const usePaymentVGSForm = ({
  onLoadForm = () => {},
  onFormLoaded,
  fieldsCount,
}: VGSFormProps) => {
  const [form, setForm] = useState<
    IVGSCollectForm & { state: VGSCollectFormState }
  >();
  const [loadedFields, setLoadedFields] = useState(new Set());
  const [isFormReady, setIsFormReady] = useState(false);

  const hasVGSCollectRun = useRef(false);

  const { showErrorToast } = useToast();

  const loadForm = useCallback(async () => {
    try {
      loadVGSCollect({
        vaultId: process.env.NEXT_PUBLIC_VGS_VAULT_ID as string,
        environment: process.env
          .NEXT_PUBLIC_VGS_ENVIRONMENT as VGSCollectVaultEnvironment,
        version: "2.24.6",
      }).then((vgsCollect) => {
        // @ts-ignore
        vgsCollect?.subscribe("fieldLoad", (field) => {
          setLoadedFields((prev) => {
            const newSet = new Set(prev);
            newSet.add(field.name);
            return newSet;
          });
        });

        const form = vgsCollect
          // @ts-ignore
          ?.create(
            process.env.NEXT_PUBLIC_VGS_VAULT_ID,
            process.env.NEXT_PUBLIC_VGS_ENVIRONMENT,
            () => {}
          )
          .useCname(process.env.NEXT_PUBLIC_VGS_CNAME);

        onLoadForm(form);
        setForm(form);
      });
    } catch (err) {
      showErrorToast(err, "Profile data request failed.");
    }
  }, [onLoadForm, showErrorToast]);

  useEffect(() => {
    if (!hasVGSCollectRun.current) {
      hasVGSCollectRun.current = true;
      loadForm();
    }
  }, [loadForm]);

  const onIframesCheckDone = useCallback(
    (allIframesLoaded: boolean) => {
      // Strangely there are some cases when some iframes are still not responding right away
      setTimeout(() => {
        setIsFormReady(allIframesLoaded);
        onFormLoaded?.(allIframesLoaded);
      }, 100);
    },
    [onFormLoaded]
  );

  const checkIframesLoaded = useCallback(() => {
    let attempts = 0;
    const maxAttempts = 30;
    const interval = 100; // 100ms

    const intervalId = setInterval(() => {
      attempts++;
      const allIframes = Array.from(
        document.querySelectorAll(
          Array.from(loadedFields)
            .map((fieldName) => `#${fieldName} iframe`)
            .join(", ") || ""
        )
      ) as HTMLIFrameElement[];

      // Check if all iframes are loaded by listening to their load events
      const allIframesLoaded = allIframes.every(
        (iframe) => iframe?.dataset.loaded === "true"
      );

      if (allIframesLoaded || attempts >= maxAttempts) {
        clearInterval(intervalId);
        onIframesCheckDone(allIframesLoaded);
      }
    }, interval);

    // Add load event listeners to iframes
    loadedFields.forEach((fieldName) => {
      const iframe = document.querySelector(
        `#${fieldName} iframe`
      ) as HTMLIFrameElement;
      if (iframe) {
        iframe.addEventListener("load", () => {
          iframe.dataset.loaded = "true";
        });
      }
    });
  }, [loadedFields, onIframesCheckDone]);

  const deleteField = (fieldName: string) => {
    setLoadedFields((prev) => {
      const newSet = new Set(prev);
      newSet.delete(fieldName);
      return newSet;
    });
  };

  useEffect(() => {
    if (!fieldsCount) {
      onIframesCheckDone(true);
      return;
    }
    if (loadedFields.size === fieldsCount) {
      checkIframesLoaded();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fieldsCount, loadedFields.size]);

  useEffect(() => {
    return () => {
      setIsFormReady(false);
      setLoadedFields(new Set());
    };
  }, []);

  return { form, isLoading: !form, isFormReady, deleteField };
};
