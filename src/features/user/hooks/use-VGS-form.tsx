import {
  IVGSCollectForm,
  VGSCollectFormState,
  VGSCollectVaultEnvironment,
} from "@vgs/collect-js-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useSpotnanaProfile } from "./api";
import useToast from "@/common/hooks/use-toast";
import { loadVGSCollect } from "@vgs/collect-js";
import { SpotnanaProfile } from "../types/profile-forms";
import { useUserProfile } from "@/features/user/hooks/api";

interface VGSFormProps {
  fieldsCount: number;
  onLoadForm?: (
    spotnanaProfile: SpotnanaProfile,
    form: IVGSCollectForm,
    citizneship?: string[]
  ) => void;
  onFormLoaded?: VoidFunction;
}

export const useVGSForm = ({
  onLoadForm = () => {},
  onFormLoaded,
  fieldsCount,
}: VGSFormProps) => {
  const [form, setForm] = useState<
    IVGSCollectForm & { state: VGSCollectFormState }
  >();
  const [loadedFields, setLoadedFields] = useState(new Set());
  const [isFormReady, setIsFormReady] = useState(false);

  const hasVGSCollectRun = useRef(false);

  const { isLoading, getProfile } = useSpotnanaProfile();

  const { citizneship } = useUserProfile();

  const { showErrorToast } = useToast();

  const loadForm = useCallback(async () => {
    try {
      const spotnanaData = await getProfile();
      loadVGSCollect({
        vaultId: process.env.NEXT_PUBLIC_VGS_VAULT_ID as string,
        environment: process.env
          .NEXT_PUBLIC_VGS_ENVIRONMENT as VGSCollectVaultEnvironment,
        version: "2.24.6",
      }).then((vgsCollect) => {
        // @ts-ignore
        vgsCollect?.subscribe("fieldLoad", (field) => {
          setLoadedFields((prev) => {
            const newSet = new Set(prev);
            newSet.add(field.name);
            return newSet;
          });
        });

        const form = vgsCollect
          // @ts-ignore
          ?.create(
            process.env.NEXT_PUBLIC_VGS_VAULT_ID,
            process.env.NEXT_PUBLIC_VGS_ENVIRONMENT,
            () => {}
          )
          .useCname(process.env.NEXT_PUBLIC_VGS_CNAME);

        onLoadForm(spotnanaData, form, citizneship);
        setForm(form);
      });
    } catch (err) {
      showErrorToast(err, "Profile data request failed.");
    }
  }, [citizneship, getProfile, onLoadForm, showErrorToast]);

  useEffect(() => {
    if (!hasVGSCollectRun.current) {
      hasVGSCollectRun.current = true;
      loadForm();
    }
  }, [getProfile, loadForm]);

  const onIframesCheckDone = useCallback(
    (allIframesLoaded: boolean) => {
      // Strangely there are some cases when some iframes are still not responding right away
      setTimeout(() => {
        setIsFormReady(allIframesLoaded);
        onFormLoaded?.();
      }, 100);
    },
    [onFormLoaded]
  );

  const checkIframesLoaded = useCallback(() => {
    let attempts = 0;
    const maxAttempts = 30;
    const interval = 100; // 100ms

    const intervalId = setInterval(() => {
      attempts++;
      const allIframes = Array.from(
        document.querySelectorAll(
          Array.from(loadedFields)
            .map((fieldName) => `#${fieldName} iframe`)
            .join(", ") || ""
        )
      ) as HTMLIFrameElement[];

      // Check if all iframes are loaded by listening to their load events
      const allIframesLoaded = allIframes.every(
        (iframe) => iframe?.dataset.loaded === "true"
      );

      if (allIframesLoaded || attempts >= maxAttempts) {
        clearInterval(intervalId);
        onIframesCheckDone(allIframesLoaded);
      }
    }, interval);

    // Add load event listeners to iframes
    loadedFields.forEach((fieldName) => {
      const iframe = document.querySelector(
        `#${fieldName} iframe`
      ) as HTMLIFrameElement;
      if (iframe) {
        iframe.addEventListener("load", () => {
          iframe.dataset.loaded = "true";
        });
      }
    });
  }, [loadedFields, onIframesCheckDone]);

  const deleteField = (fieldName: string) => {
    setLoadedFields((prev) => {
      const newSet = new Set(prev);
      newSet.delete(fieldName);
      return newSet;
    });
  };

  useEffect(() => {
    if (!fieldsCount) {
      onIframesCheckDone(true);
      return;
    }
    if (loadedFields.size === fieldsCount) {
      checkIframesLoaded();
    }
  }, [
    checkIframesLoaded,
    fieldsCount,
    form,
    loadedFields.size,
    onFormLoaded,
    onIframesCheckDone,
  ]);

  useEffect(() => {
    return () => {
      setIsFormReady(false);
      setLoadedFields(new Set());
    };
  }, []);

  return { form, isLoading: isLoading || !form, isFormReady, deleteField };
};
