import { useCallback, useMemo, useRef, useState } from "react";
import { VGSCollectFormState } from "@vgs/collect-js-react";
import { Configuration } from "@/features/user/types/profile-forms";
import { useAtomValue } from "jotai";
import { securedFieldConfigurations } from "@/features/user/constants/profile-forms";
import {
  PaymentInformationResponse,
  VGSFieldState,
} from "@/features/user/types/payment-information";
import { darkModeAtom } from "@/common/store/dark-mode";
import { DarkModeOptions } from "@/common/constants/theme";
import { mask } from "@/common/utils/mask-string";
import { cardIcons } from "../constants/cards";

export function usePaymentFormFields() {
  const darkModeOption = useAtomValue(darkModeAtom);
  const deviceDarkMode =
    typeof window !== "undefined" &&
    !!window?.matchMedia("(prefers-color-scheme: dark)")?.matches;
  const darkMode =
    darkModeOption === DarkModeOptions.DARK ||
    (darkModeOption === DarkModeOptions.DEVICE_DEFAULT && deviceDarkMode);

  const [errors, setErrors] = useState<VGSCollectFormState | null>(null);
  const [isCardNumberEdited, setIsCardNumberEdited] = useState(false);
  const [isCVCEdited, setIsCVCEdited] = useState(false);

  const requireCardNumberValidation = useRef<boolean>(false);

  const fieldsCSS = useMemo(
    () => ({
      color: darkMode ? "white" : "#1D1C1D",
      "& option": {
        background: darkMode ? "#404040" : "white",
        color: darkMode ? "white" : "#1D1C1D",
      },
      "&::placeholder": {
        color: darkMode ? "#CCC" : "#999",
      },
      fontSize: "16px",
    }),
    [darkMode]
  );

  const addKeypressEvent = useCallback((formField: any, fieldName: string) => {
    const clearFieldError = () => {
      setErrors((currValue) => {
        if (!currValue) {
          return currValue;
        }
        const { [fieldName]: _, ...rest } = currValue;
        return rest as VGSCollectFormState;
      });
    };

    formField.on("keypress", clearFieldError);
    formField.on("input", clearFieldError);
  }, []);

  const initFields = useCallback(
    (
      form: any,
      configuration: Configuration,
      { payment_information }: Partial<PaymentInformationResponse>
    ) => {
      Object.values(configuration).forEach((config) => {
        const fieldName = config.name;
        const valueFromProfile =
          payment_information?.[fieldName as keyof typeof payment_information];
        const formField = form.field(`#${fieldName}`, {
          ...config,
          ...(valueFromProfile && { defaultValue: valueFromProfile }),
          css: fieldsCSS,
          inputMode: fieldName === "exp_date" ? "numeric" : undefined,
          yearLength: fieldName === "exp_date" ? 2 : undefined,
        });
        if (config.mask) {
          const { format, maskChar, formatChar } = config.mask;
          formField.mask(format, maskChar, formatChar);
        }
        addKeypressEvent(formField, fieldName);
      });
    },
    [addKeypressEvent, fieldsCSS]
  );

  const initCardNumberField = useCallback(
    (form: any, paymentInformation: Partial<PaymentInformationResponse>) => {
      const { card_number, card_type } =
        paymentInformation?.payment_information ?? {};
      const cardNumberField = form.field("#card_number", {
        ...securedFieldConfigurations.card_number,
        ...(card_number && { validations: undefined }),
        css: {
          ...fieldsCSS,
          "&::placeholder": {
            color: darkMode ? "white" : "#1D1C1D",
          },
        },
        icons: {
          cardPlaceholder: !!card_type && (cardIcons?.[card_type] ?? undefined),
        },
        placeholder: card_number ? mask(card_number, 12, "*") : "",
        inputMode: "numeric",
      });
      if (card_number) {
        cardNumberField.on("focus", () => {
          cardNumberField.update({
            placeholder: "",
          });
          addKeypressEvent(cardNumberField, "card_number");
        });
        cardNumberField.on("blur", () => {
          cardNumberField.update({
            placeholder: card_number ? mask(card_number, 12, "*") : "",
          });
        });
      }
      cardNumberField.on("update", ({ isDirty, isEmpty }: VGSFieldState) => {
        if (!isEmpty && isDirty) {
          if (!requireCardNumberValidation?.current) {
            requireCardNumberValidation.current = true;
            cardNumberField.update({
              validations: ["validCardNumber"],
            });
            setIsCardNumberEdited(true);
          }
        } else if (requireCardNumberValidation?.current) {
          requireCardNumberValidation.current = false;
          cardNumberField.update({
            validations: !card_number ? ["validCardNumber"] : undefined,
            icons: { placeholder: undefined },
          });
          setIsCardNumberEdited(false);
        }
      });
    },
    [addKeypressEvent, darkMode, fieldsCSS]
  );

  const initCVCField = useCallback(
    (form: any, paymentInformation: Partial<PaymentInformationResponse>) => {
      const card_cvc = paymentInformation?.payment_information?.card_cvc;
      const cardCVCField = form.field("#card_cvc", {
        ...securedFieldConfigurations.card_cvc,
        ...(card_cvc && { validations: ["required"] }),
        css: fieldsCSS,
        defaultValue: card_cvc ? "***" : "",
        inputMode: "numeric",
      });
      if (card_cvc) {
        cardCVCField.on("focus", () => {
          cardCVCField.delete();
          const formField = form.field("#card_cvc", {
            ...securedFieldConfigurations.card_cvc,
            autoFocus: true,
            css: fieldsCSS,
          });
          addKeypressEvent(formField, "card_cvc");
          setIsCVCEdited(true);
        });
      }
      addKeypressEvent(cardCVCField, "card_cvc");
    },
    [addKeypressEvent, fieldsCSS]
  );

  return {
    errors,
    setErrors,
    initFields,
    initCardNumberField,
    initCVCField,
    addKeypressEvent,
    isCardNumberEdited,
    isCVCEdited,
  };
}
