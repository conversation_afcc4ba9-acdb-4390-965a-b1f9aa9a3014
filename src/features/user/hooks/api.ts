import { useRouter } from "next/navigation";
import * as nativebridge from "@nrk/nativebridge";
import useSWR, { useSWRConfig } from "swr";
import { OutgoingJSBridgeEvents } from "@/common/types/js-bridge-events";
import useSWRMutation from "swr/mutation";
import LoginResponse from "@/features/user/types/login-response";
import { UserProfileResponse, UserRole } from "@/features/user/types/user";
import SessionHistory from "@/features/user/types/session-history";
import { SpotnanaProfile } from "@/features/user/types/profile-forms";
import {
  ApiPaths,
  ApiRestMethod,
  HttpStatusCode,
  ROUTES,
} from "@/common/constants";
import { fetcher } from "@/common/api/fetcher";
import { useCreateTrip } from "@/common/hooks/api";
import { useUsersList } from "@/features/admin/hooks/api";
import useToast from "@/common/hooks/use-toast";
import { useCallback } from "react";
import { usePostRequest } from "@/common/hooks/swr";
import { cleanupLocalStorage } from "@/common/utils/local-storage-cleanup";
import { LAST_LOGGED_IN_GMAIL_KEY } from "@/common/constants/local-storage-keys";
import { showCalendarMessageAtom } from "../store/settings";
import { useSetAtom } from "jotai";

export function useLogin(tryingMode?: boolean) {
  const { showErrorToast } = useToast();
  const router = useRouter();
  const { createTrip, isMutating: isCreatingTrip } = useCreateTrip();

  const loginSuccessRedirect = (
    {
      is_onboarding_completed,
      last_checkpoint_trip_id,
      current_trip_id,
      user_email,
    }: LoginResponse,
    { isGoogleLogin }: { isGoogleLogin?: boolean } = {}
  ) => {
    // keep this logic in sync with the one in useOnboardingRedirect
    const shouldRedirectToOnboarding = !is_onboarding_completed && !tryingMode;

    if (!!isGoogleLogin) {
      localStorage.setItem(LAST_LOGGED_IN_GMAIL_KEY, user_email);
    }

    if (shouldRedirectToOnboarding) {
      router.push(ROUTES.ONBOARDING);
    } else if (!!current_trip_id) {
      const tripUrl = `${ROUTES.TRIPS}/${current_trip_id}`;
      router.push(tripUrl);
    } else if (!!last_checkpoint_trip_id) {
      const tripUrl = `${ROUTES.TRIPS}/${last_checkpoint_trip_id}`;
      router.push(tripUrl);
    } else {
      createTrip();
    }
  };

  const {
    error: loginError,
    trigger: loginRequest,
    isMutating: isLoadingLogin,
  } = useSWRMutation(ApiPaths.GOOGLE_LOGIN, fetcher, {
    onSuccess: (data) => {
      loginSuccessRedirect(data, { isGoogleLogin: true });
    },
  });

  const {
    trigger: triggerMicrosoftLogin,
    error: microsoftLoginError,
    isMutating: isLoadingMicrosoftLogin,
  } = useSWRMutation(ApiPaths.MICROSOFT_LOGIN, fetcher, {
    onSuccess: ({ url }) => router.push(url),
    onError: (error) => showErrorToast(error, "Microsoft login failed"),
  });

  const {
    trigger: triggerAppleLogin,
    error: appleLoginError,
    isMutating: isLoadingAppleLogin,
  } = useSWRMutation(ApiPaths.APPLE_LOGIN, fetcher, {
    onSuccess: ({ url }) => {
      //  Safari might be caching the previous cancellation state, preventing the popup from appearing again
      const timestamp = new Date().getTime();
      router.push(`${url}&t=${timestamp}`);
    },
    onError: (error) => showErrorToast(error, "Apple login failed"),
  });

  const triggerLogin = (body: BodyInit) =>
    loginRequest({
      options: {
        body,
        method: ApiRestMethod.POST,
      },
    }).catch((error) => {
      if (error?.status === HttpStatusCode.FORBIDDEN) {
        router.push(ROUTES.LOGIN_DENIED);
      } else {
        showErrorToast(error, "Login failed");
      }
    });

  return {
    error: loginError || microsoftLoginError || appleLoginError,
    isMutating:
      isLoadingLogin ||
      isLoadingMicrosoftLogin ||
      isLoadingAppleLogin ||
      isCreatingTrip,
    triggerLogin,
    triggerMicrosoftLogin,
    triggerAppleLogin,
    loginSuccessRedirect,
  };
}

export function useOtpLogin(tryingModeEnabled?: boolean) {
  const router = useRouter();
  const { showErrorToast } = useToast();
  const { loginSuccessRedirect } = useLogin(tryingModeEnabled);

  const { trigger: generateOtp, isMutating: isLoadingGenerateOtp } =
    usePostRequest<{ email: string }>(ApiPaths.EMAIL_LOGIN_GENERATE_OTP, {
      onError: (error) => {
        if (error?.status === HttpStatusCode.FORBIDDEN) {
          router.push(ROUTES.LOGIN_DENIED);
        } else {
          showErrorToast(error, "One-time password generation failed");
        }
      },
    });

  const {
    trigger: validateOtp,
    error: codeValidationError,
    isMutating: isLoadingValidateOtp,
  } = usePostRequest<{ email: string; code: string }>(
    ApiPaths.EMAIL_LOGIN_VALIDATE_OTP,
    {
      onSuccess: (data) => {
        try {
          nativebridge.emit(OutgoingJSBridgeEvents.OTP_LOGIN_SUCCESS);
        } catch (e) {}
        loginSuccessRedirect(data);
      },
      onError: (error) => {
        if (error?.status === HttpStatusCode.FORBIDDEN) {
          router.push(ROUTES.LOGIN_DENIED);
        }
      },
    }
  );

  return {
    generateOtp,
    validateOtp,
    isLoadingGenerateOtp,
    isLoadingValidateOtp,
    codeValidationError,
  };
}

export function useRedeemOtc() {
  const router = useRouter();
  const { showErrorToast } = useToast();

  const { trigger, isMutating } = usePostRequest<{
    otc: string;
  }>(ApiPaths.OTC_BETA_REDEEM, {
    onSuccess: () => router.push(ROUTES.HOME),
    onError: (error) => showErrorToast(error, "Invite code redemption failed"),
  });

  return {
    redeemOtc: trigger,
    isLoading: isMutating,
  };
}

export function useGetSessionHistory() {
  const { showErrorToast } = useToast();
  const { trigger, isMutating } = useSWRMutation<SessionHistory>(
    ApiPaths.USER_SESSION_HISTORY,
    fetcher
  );

  const getSessionHistory = useCallback(
    () =>
      trigger().catch((error) =>
        showErrorToast(error, "Session history request failed.")
      ),
    [showErrorToast, trigger]
  );

  return {
    getSessionHistory,
    isLoading: isMutating,
  };
}

export function useSessionHistory() {
  const { data, error, isLoading, mutate } = useSWR<SessionHistory>(
    ApiPaths.USER_SESSION_HISTORY,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnMount: false,
    }
  );

  const {
    is_onboarding_completed,
    tutorial_completed,
    last_checkpoint_trip_id,
    current_trip_id,
  } = data ?? {};

  return {
    error,
    isLoading,
    isOnboardingComplete: is_onboarding_completed,
    isTutorialComplete: tutorial_completed,
    lastTripCheckpoint: last_checkpoint_trip_id,
    currentTripId: current_trip_id,
    mutate,
  };
}

export function useLogOut({
  onSuccess = () => {},
}: { onSuccess?: VoidFunction } = {}) {
  const { showErrorToast } = useToast();
  const router = useRouter();
  const { trigger, isMutating: isLoggingOut } = useSWRMutation(
    ApiPaths.USER_LOGOUT,
    fetcher,
    {
      onSuccess: async () => {
        try {
          nativebridge.emit(OutgoingJSBridgeEvents.LOG_OUT_SUCCESS);
        } catch (e) {}
        cleanupLocalStorage();
        router.push(ROUTES.LOGIN);
        onSuccess();
      },
    }
  );

  return {
    triggerLogout: () =>
      trigger({ options: { method: ApiRestMethod.POST } }).catch((error) =>
        showErrorToast(error, "Logout failed.")
      ),
    isLoggingOut,
  };
}

export function useDeleteAccount() {
  const { showErrorToast } = useToast();
  const router = useRouter();
  const { trigger } = useSWRMutation(ApiPaths.DELETE_ACCOUNT, fetcher, {
    onSuccess: () => router.push(ROUTES.LOGIN),
  });

  return {
    triggerDelete: () =>
      trigger().catch((error) =>
        showErrorToast(error, "Account deletion request failed.")
      ),
  };
}

export function useUserProfileMarkSampleTripsAsHidden() {
  const { showErrorToast } = useToast();
  const { mutateUserProfile } = useUserProfile();

  const { trigger, isMutating } = usePostRequest<
    { hide_sample_trips: boolean },
    UserProfileResponse
  >(ApiPaths.USER_PROFILE_HIDE_SAMPLE_TRIP, {
    onSuccess: mutateUserProfile,
    onError: (error) =>
      showErrorToast(error, "Failed to update sample trips preference"),
  });

  const markSampleTripsAsHidden = useCallback(() => {
    trigger({ hide_sample_trips: true }).catch((error) =>
      showErrorToast(error, "Failed to update sample trips preference")
    );
  }, [trigger, showErrorToast]);

  return {
    markSampleTripsAsHidden,
    isHiding: isMutating,
  };
}

export function useUserProfile() {
  const { showErrorToast } = useToast();
  const { error, data: users, mutate: mutateUsersList } = useUsersList();
  const setShowCalendarMessage = useSetAtom(showCalendarMessageAtom);

  const { data, isLoading, mutate } = useSWR<UserProfileResponse>(
    ApiPaths.USER_PROFILE,
    fetcher,
    {
      onSuccess: (data) => {
        const {
          userProfile,
          google_calendar_enabled,
          microsoft_calendar_enabled,
        } = data;
        const { role } = userProfile;

        if (role === UserRole.ADMIN && !users) {
          mutateUsersList();
        }
        // Hide the calendar message if the user has connected a calendar once
        if (google_calendar_enabled || microsoft_calendar_enabled) {
          setShowCalendarMessage(false);
        }
      },
      revalidateOnFocus: false,
      revalidateOnMount: false,
    }
  );

  const {
    userCompanyTravelPolicy,
    userPreferences,
    userProfile,
    shouldShowSampleTrips,
    shouldShowDontShowAgain,
    microsoft_calendar_enabled,
    google_calendar_enabled,
  } = data ?? {};
  const isAdmin = userProfile?.role === UserRole.ADMIN;
  const isCompanyAdmin = userProfile?.role === UserRole.COMPANY_ADMIN;

  return {
    error,
    isLoading,
    profile: userProfile,
    preferences: userPreferences,
    role: userProfile?.role,
    citizneship: userProfile?.citizenship,
    mutateUserProfile: () =>
      mutate().catch((error) =>
        showErrorToast(error, "User profile request failed.")
      ),
    travelPolicy: userCompanyTravelPolicy,
    sampleTrips: {
      shouldShowSampleTrips,
      shouldShowDontShowAgain,
    },
    microsoftCalendarEnabled: !!microsoft_calendar_enabled,
    googleCalendarEnabled: !!google_calendar_enabled,
    isAdmin,
    isCompanyAdmin,
  };
}

export function useDeletePreference() {
  const { showErrorToast } = useToast();
  const { mutate } = useSWRConfig();
  const swrReturn = useSWRMutation(
    `${ApiPaths.USER_REMOVE_PREFERENCE}`,
    fetcher,
    {
      onSuccess: () =>
        mutate(ApiPaths.USER_PROFILE).catch((error) =>
          showErrorToast(error, "User profile request failed.")
        ),
    }
  );

  function trigger(key: string, value: string) {
    swrReturn
      .trigger({
        options: {
          body: JSON.stringify({
            key,
            value,
          }),
          method: ApiRestMethod.POST,
        },
      })
      .catch((error) => showErrorToast(error, "Preference deletion failed."));
  }

  return {
    ...swrReturn,
    trigger,
  };
}

export function useSpotnanaProfile() {
  const { trigger, isMutating, data } = useSWRMutation<SpotnanaProfile>(
    ApiPaths.GET_SPOTNANA_PROFILE,
    fetcher
  );

  return {
    isLoading: isMutating,
    getProfile: trigger,
    profile: data,
  };
}
