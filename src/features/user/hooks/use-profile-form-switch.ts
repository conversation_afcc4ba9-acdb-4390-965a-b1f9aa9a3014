import { use<PERSON>tom, useSet<PERSON><PERSON> } from "jotai";
import { ProfileForms } from "@/features/user/types/profile-forms";
import {
  frequentFlyerIATACodesAtom,
  profileFormCloseMessageAtom,
  profileFormDoneMessageAtom,
  profileformPopupAtom,
} from "@/features/user/store/profile-form-popup";
import { IATACode } from "../constants/profile-forms";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import { WebSocketSender } from "@/features/chat/types/web-socket";

const FLIGHT_PROFILE_CLOSE_MESSAGE =
  "I closed the flight payment profile pop up without saving it as I'm not ready to proceed just yet.";

const HOTEL_PROFILE_CLOSE_MESSAGE =
  "I closed the hotel payment profile pop up without saving it as I'm not ready to proceed just yet.";

const DEFAULT_NO_NEED_TO_UPDATE_PROFILE_DONE_MESSAGE =
  "I don't need to update my profile/payment. can proceed the booking prcoess now.";

const DEFAULT_PROFILE_DONE_MESSAGE =
  "I updated my profile/payment. please proceed the booking process now.";

export default function useProfileFormToggle() {
  const [profileForm, setProfileForm] = useAtom(profileformPopupAtom);
  const setFrequentFlyerIATACodes = useSetAtom(frequentFlyerIATACodesAtom);
  const [profileFormCloseMessage, setProfileFormCloseMessage] = useAtom(
    profileFormCloseMessageAtom
  );
  const [profileFormDoneMessage, setProfileFormDoneMessage] = useAtom(
    profileFormDoneMessageAtom
  );

  function openProfileForm(
    formToOpen: ProfileForms | null,
    frequentFlyerIATACodes: IATACode[] = [],
    formCloseMessage: string | null = null,
    formDoneMessage: string | null = null
  ) {
    if (formToOpen === profileForm) {
      return;
    }

    setProfileForm(formToOpen);
    setFrequentFlyerIATACodes(frequentFlyerIATACodes);
    setProfileFormCloseMessage(
      formCloseMessage ??
        (profileForm === ProfileForms.FLIGHTS
          ? FLIGHT_PROFILE_CLOSE_MESSAGE
          : HOTEL_PROFILE_CLOSE_MESSAGE)
    );
    setProfileFormDoneMessage(
      formDoneMessage ?? DEFAULT_PROFILE_DONE_MESSAGE
    );
  }

  function close() {
    setProfileForm(null);
    setFrequentFlyerIATACodes([]);
  }

  function handleProfileFormClose(send: WebSocketSender) {
    if (
      profileForm === ProfileForms.FLIGHTS ||
      profileForm === ProfileForms.HOTELS
    ) {
      send({
        text: profileFormCloseMessage,
        type: OutgoingMessageTypes.SILENT_PROMPT,
      });
    } else if (profileForm === ProfileForms.PROFILE_UPDATE_AGENT) {
      send({
        type: OutgoingMessageTypes.SILENT_PROMPT,
        text: DEFAULT_NO_NEED_TO_UPDATE_PROFILE_DONE_MESSAGE,
      });
    }
    close();
  }

  return {
    closeProfileForm: close,
    openProfileForm,
    profileForm,
    profileFormDoneMessage,
    handleProfileFormClose,
  };
}
