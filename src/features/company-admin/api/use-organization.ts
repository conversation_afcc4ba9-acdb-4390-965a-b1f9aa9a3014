import { ApiPaths } from "@/common/constants";
import { useUserProfile } from "@/features/user/hooks/api";
import { Organization } from "@/features/company-admin/types/organization";
import useSWR from "swr";
import useToast from "@/common/hooks/use-toast";
import { usePostRequest } from "@/common/hooks/swr";
import { fetcher } from "@/common/api/fetcher";
import { ApiRestMethod } from "@/common/constants";
import useSWRMutation from "swr/mutation";

const deleteDomainRequest = (url: string, { arg }: { arg: string }) =>
  fetcher(`${url}?domain_name=${encodeURIComponent(arg)}`, {
    arg: {
      options: { method: ApiRestMethod.DELETE },
    },
  });

export function useOrganization() {
  const { showErrorToast } = useToast();
  const { profile } = useUserProfile();
  const organizationId = profile?.organization_id;

  const { data: organization, mutate: mutateOrganization } =
    useSWR<Organization>(
      organizationId ? `${ApiPaths.ORGANIZATIONS}/${organizationId}` : null
    );
  const { trigger: addDomain, isMutating: isAddDomainLoading } =
    usePostRequest<{ domain: string }>(
      `${ApiPaths.ORGANIZATIONS}/${organizationId}/domains`,
      {
        onSuccess: () => mutateOrganization(),
        onError: (error) => showErrorToast(error, "Failed to add domain"),
      }
    );
  const { trigger: deleteDomain, isMutating: isDeleteDomainLoading } =
    useSWRMutation(
      `${ApiPaths.ORGANIZATIONS}/${organizationId}/domains`,
      deleteDomainRequest,
      {
        onSuccess: () => mutateOrganization(),
        onError: (error) => showErrorToast(error, "Failed to delete domain"),
      }
    );

  return {
    organization,
    addDomain,
    isAddDomainLoading,
    deleteDomain,
    isDeleteDomainLoading,
  };
}
