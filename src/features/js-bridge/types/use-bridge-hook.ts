import { NativeCalendarResponse } from "@/common/types/native-calendar-response";

export type BridgeHookOptions = {
  onInit?: VoidFunction;
  onNewTripNotification?: VoidFunction;
  onPostLogin?: VoidFunction;
  onLoginAccessDenied?: VoidFunction;
  onShowLoader?: VoidFunction;
  onHideLoader?: VoidFunction;
  onWSDebug?: VoidFunction;
  onDisplayPushNotifications?: VoidFunction;
  onCalendarGranted?: (authArgs: NativeCalendarResponse) => void;
  preventLoadedMessage?: boolean;
};
