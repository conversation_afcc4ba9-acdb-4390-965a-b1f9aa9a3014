import { useCallback } from "react";
import * as nativebridge from "@nrk/nativebridge";
import {
  IncomingJSBridgeEvents,
  OutgoingJSBridgeEvents,
} from "@/common/types/js-bridge-events";
import { BridgeHookOptions } from "@/features/js-bridge/types/use-bridge-hook";

export default function useJSBridge() {
  const init = useCallback((options: BridgeHookOptions = {}) => {
    const {
      onInit,
      onNewTripNotification,
      onPostLogin,
      onLoginAccessDenied,
      onShowLoader,
      onHideLoader,
      onWSDebug,
      onDisplayPushNotifications,
      onCalendarGranted,
      preventLoadedMessage,
    } = options ?? {};

    try {
      if (typeof onInit === "function") {
        nativebridge.once(IncomingJSBridgeEvents.NATIVE_APP_LOADED, onInit);
      }

      if (typeof onPostLogin === "function") {
        nativebridge.on(IncomingJSBridgeEvents.POST_LOGIN, onPostLogin);
      }

      if (typeof onLoginAccessDenied === "function") {
        nativebridge.on(
          IncomingJSBridgeEvents.LOGIN_ACCESS_DENIED,
          onLoginAccessDenied
        );
      }

      if (typeof onShowLoader === "function") {
        nativebridge.on(IncomingJSBridgeEvents.SHOW_LOADER, onShowLoader);
      }

      if (typeof onHideLoader === "function") {
        nativebridge.on(IncomingJSBridgeEvents.HIDE_LOADER, onHideLoader);
      }

      if (typeof onDisplayPushNotifications === "function") {
        nativebridge.on(
          IncomingJSBridgeEvents.DISPLAY_PUSH_NOTIFICATIONS,
          onDisplayPushNotifications
        );
      }

      if (typeof onCalendarGranted === "function") {
        nativebridge.on(
          IncomingJSBridgeEvents.CALENDAR_ACCESS_GRANTED,
          onCalendarGranted
        );
      }

      if (typeof onWSDebug === "function") {
        nativebridge.once(
          IncomingJSBridgeEvents.ENABLE_WEB_SOCKET_DEBUG,
          onWSDebug
        );
      }

      if (typeof onNewTripNotification === "function") {
        nativebridge.once(
          IncomingJSBridgeEvents.NOTIFICATION_NEW_TRIP_TAP,
          onNewTripNotification
        );
      }

      if (!preventLoadedMessage) {
        nativebridge.emit(OutgoingJSBridgeEvents.WEB_APP_LOADED);
      }
    } catch (e) {}
  }, []);

  return init;
}
