import { useRef, useState, useEffect, useCallback } from "react";
import { ApiPaths, ApiRestMethod } from "@/common/constants";
import { OutgoingMessageTypes } from "../types/messages";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import useSWRMutation from "swr/mutation";
import { fetcher } from "@/common/api/fetcher";
import MicRecorder from "mic-recorder-to-mp3";
import { OutgoingJSBridgeEvents } from "@/common/types/js-bridge-events";
import * as nativeBridge from "@nrk/nativebridge";
import { MAX_RECORDING_SECONDS } from "../constants/transcriptions";
import { useAtomValue } from "jotai";
import { isNativeBridgeAtom } from "@/common/store/nativebridge";
import useMicrophonePopup from "@/common/hooks/use-microphone-popup";

type NativeResponse = { showMicrophonePopup: boolean };

interface UseAudioRecordingProps {
  onNotAllowedOrFound?: VoidFunction;
  onTranscriptionError?: VoidFunction;
}

const useAudioRecording = ({
  onNotAllowedOrFound = () => {},
  onTranscriptionError = () => {},
}: UseAudioRecordingProps) => {
  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);

  const isNativeBridge = useAtomValue(isNativeBridgeAtom);
  const [isRecording, setIsRecording] = useState(false);
  const [elapsedSeconds, setElapsedSeconds] = useState<number>(0);
  // for silence detection
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const silenceCheckInterval = useRef<number>();
  const [isMostlySilent, setIsMostlySilent] = useState(true);

  const micRecorderRef = useRef<any | null>(null);
  const timerRef = useRef<number>();
  const isRecordingTriggered = useRef(false);

  const { openMicrophonePopup, MicrophonePopup } = useMicrophonePopup();

  const { trigger, isMutating } = useSWRMutation(
    ApiPaths.OPENAI_AUDIO_TRANSCRIPTIONS,
    fetcher,
    {
      onSuccess: ({ text }) => {
        if (text) {
          send({
            text,
            type: OutgoingMessageTypes.PROMPT,
          });
        }
      },
      onError: onTranscriptionError,
    }
  );

  useEffect(() => {
    micRecorderRef.current = new MicRecorder();
  }, []);

  const checkSilence = async () => {
    setIsMostlySilent(true);
    mediaStreamRef.current = await navigator.mediaDevices.getUserMedia({
      audio: true,
    });
    const stream = mediaStreamRef.current;
    audioContextRef.current = new AudioContext();
    analyserRef.current = audioContextRef.current.createAnalyser();
    sourceRef.current = audioContextRef.current.createMediaStreamSource(stream);
    sourceRef.current.connect(analyserRef.current);
    const dataArray = new Uint8Array(analyserRef.current.fftSize);

    silenceCheckInterval.current = window.setInterval(() => {
      if (!analyserRef.current) return;
      analyserRef.current.getByteTimeDomainData(dataArray);
      const avg =
        dataArray.reduce((sum, val) => sum + Math.abs(val - 128), 0) /
        dataArray.length;
      console.log("Average deviation:", avg);
      if (avg > 5) {
        setIsMostlySilent(false);
      }
    }, 500);
  };

  const captureMedia = async () => {
    try {
      // Parallel audio context to detect silence
      await checkSilence();

      await micRecorderRef.current.start();
      setIsRecording(true);
      setElapsedSeconds(0);
      timerRef.current = window.setInterval(() => {
        setElapsedSeconds((prevTime) => prevTime + 1);
      }, 1000);
    } catch (error) {
      console.debug("Error accessing microphone:", error);
      onNotAllowedOrFound();
      isRecordingTriggered.current = false;
    }
  };

  const startRecording = async () => {
    if (isRecordingTriggered.current) {
      return;
    }
    isRecordingTriggered.current = true;

    if (isNativeBridge) {
      try {
        nativeBridge.rpc({
          topic: OutgoingJSBridgeEvents.RECORD_BUTTON_PRESSED,
          data: {},
          resolve: ({ showMicrophonePopup }: NativeResponse) => {
            if (showMicrophonePopup) {
              openMicrophonePopup();
              isRecordingTriggered.current = false;
            } else {
              captureMedia();
            }
          },
          reject: (error: TypeError) => {
            console.error("Error from native:", error);
            isRecordingTriggered.current = false;
          },
        });
      } catch (e) {}
      return;
    }

    captureMedia();
  };

  const sendAudio = useCallback(
    (buffer: BlobPart[], type: string) => {
      const file = new File(buffer, "tsn.mp3", {
        type,
      });

      const body = new FormData();
      body.append("file", file, "tsn.mp3");
      body.append("model", "whisper-1");
      body.append("language", "en");

      trigger({
        options: {
          method: ApiRestMethod.POST,
          headers: {
            Authorization: "Token proxy",
          },
          body,
        },
      });
    },
    [trigger]
  );

  const stopRecording = useCallback(async () => {
    if (!isRecordingTriggered.current) {
      return;
    }
    isRecordingTriggered.current = false;

    try {
      const [buffer, blob] = await micRecorderRef.current.stop().getMp3();
      clearInterval(timerRef.current);
      if (elapsedSeconds > 1 && !isMostlySilent) {
        sendAudio(buffer, blob.type);
      } else {
        onTranscriptionError();
      }
    } catch (e) {
      console.error(e);
      onTranscriptionError();
    } finally {
      setIsRecording(false);
      setElapsedSeconds(0);
      // Stop the audio context and media stream for silence detection
      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
        if (mediaStreamRef.current) {
          mediaStreamRef.current.getTracks().forEach((track) => track.stop());
          mediaStreamRef.current = null;
        }
      }
      clearInterval(silenceCheckInterval.current);
    }
  }, [elapsedSeconds, isMostlySilent, onTranscriptionError, sendAudio]);

  useEffect(() => {
    if (elapsedSeconds >= MAX_RECORDING_SECONDS) {
      stopRecording();
    }
  }, [stopRecording, elapsedSeconds]);

  useEffect(() => {
    return () => clearInterval(timerRef.current);
  }, []);

  return {
    isRecording,
    isLoading: isMutating,
    elapsedSeconds,
    isMostlySilent,
    startRecording,
    stopRecording,
    MicrophonePopup,
  };
};

export default useAudioRecording;
