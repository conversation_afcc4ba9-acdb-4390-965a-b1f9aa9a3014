import { useSet<PERSON><PERSON> } from "jotai";
import { chatHistory<PERSON>tom, chatHistoryReceivedAtom } from "../store/chat";
import { useCallback } from "react";

export function useReset() {
  const setChatHistory = useSetAtom(chatHistoryAtom);
  const setChatHistoryReceived = useSetAtom(chatHistoryReceivedAtom);

  const resetChatHistory = useCallback(() => {
    setChatHistory([]);
    setChatHistoryReceived(false);
  }, [setChatHistory, setChatHistoryReceived]);

  return { resetChatHistory };
}
