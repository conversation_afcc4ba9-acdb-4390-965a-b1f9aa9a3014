import { use<PERSON>tom } from "jotai";
import dayjs from "dayjs";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import { threadTimestampAtom } from "@/features/chat/store/current-trip";

export default function useCreateTimestamp() {
  const [threadTimestamp, setThreadTimestamp] = useAtom(threadTimestampAtom);
  const { lastTimestamp, threshold } = threadTimestamp ?? {
    threshold: 3600,
  };

  function createTimestamp() {
    const shouldCreateTimestamp =
      dayjs.utc().diff(lastTimestamp, "seconds") > threshold;

    if (shouldCreateTimestamp) {
      const timestamp = dayjs.utc().toISOString();
      setThreadTimestamp({ threshold, lastTimestamp: timestamp });
      return { timestamp, type: OutgoingMessageTypes.PROMPT };
    }

    setThreadTimestamp({
      threshold,
      lastTimestamp: dayjs.utc().toISOString(),
    });
  }

  return createTimestamp;
}
