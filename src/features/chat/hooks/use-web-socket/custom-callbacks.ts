import {
  IncomingMessage,
  IncomingMessageStatus,
  IncomingMessageTypes,
  PaymentFormTypes,
  SkeletonsMessageTypes,
} from "@/features/chat/types/messages";
import { WebSocketOptions } from "@/features/chat/types/web-socket";

export default function registerCustomCallbacks(
  event: MessageEvent,
  options?: WebSocketOptions
) {
  const message: IncomingMessage = JSON.parse(event.data);
  const {
    expectResponse,
    refreshPage,
    status,
    travel_context,
    type: messageType,
    missingFreqFlyerAirlines,
  } = message ?? {};
  const {
    onErrorMessage,
    onExpectResponse,
    onHistoryMessage,
    onItineraryUpdate,
    onOnboardingComplete,
    onPreferencesComplete,
    onProfileUpdate,
    onRefreshMessage,
    onSkeleton,
    onTravelContext,
    onTripsUpdate,
    onOpenPaymentForm,
    onGoogleCalendarAccess,
    onMicrosoftCalendarAccess,
  } = options ?? {};

  if (!!expectResponse && typeof onExpectResponse === "function") {
    onExpectResponse();
  }

  if (
    status === IncomingMessageStatus.ERROR &&
    typeof onErrorMessage === "function"
  ) {
    onErrorMessage();
  }

  if (!!refreshPage && typeof onRefreshMessage === "function") {
    onRefreshMessage();
  }

  if (!!travel_context && typeof onTravelContext === "function") {
    onTravelContext(travel_context);
  }

  switch (messageType) {
    case IncomingMessageTypes.TRIPS_UPDATE:
      onTripsUpdate?.();
      break;

    case IncomingMessageTypes.HISTORY:
      onHistoryMessage?.(message);
      break;

    case IncomingMessageTypes.ITINERARY_UPDATE:
      onItineraryUpdate?.();
      break;

    case IncomingMessageTypes.ONBOARDING_COMPLETE:
      onOnboardingComplete?.();
      break;

    case IncomingMessageTypes.PREFERENCES_COMPLETE:
      onPreferencesComplete?.();
      break;

    case IncomingMessageTypes.PROFILE_UPDATE:
      onProfileUpdate?.();
      break;

    case IncomingMessageTypes.FARES_SKELETON:
    case IncomingMessageTypes.FLIGHTS_SKELETON:
    case IncomingMessageTypes.HOTELS_SKELETON:
    case IncomingMessageTypes.FLIGHTS_SKELETON_ASYNC:
    case IncomingMessageTypes.HOTELS_SKELETON_ASYNC:
      onSkeleton?.(message.type as SkeletonsMessageTypes, message.text);
      break;
    case IncomingMessageTypes.OPEN_FLIGHTS_PAYMENT_FORM:
    case IncomingMessageTypes.OPEN_HOTELS_PAYMENT_FORM:
    case IncomingMessageTypes.OPEN_PAYMENT_FORM:
      onOpenPaymentForm?.(message.type as PaymentFormTypes, {
        frequentFlyerIATACodes: missingFreqFlyerAirlines || [],
        formMessage: message.formMessage,
      });
      break;
    case IncomingMessageTypes.GOOGLE_CALENDAR_ACCESS:
      onGoogleCalendarAccess?.(message.loginInitUrl);
      break;
    case IncomingMessageTypes.MICROSOFT_CALENDAR_ACCESS:
      onMicrosoftCalendarAccess?.(message.loginInitUrl);
      break;
    default:
      break;
  }
}
