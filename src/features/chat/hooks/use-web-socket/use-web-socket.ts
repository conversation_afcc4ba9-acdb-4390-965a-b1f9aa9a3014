import { use<PERSON><PERSON>, use<PERSON><PERSON>Value, use<PERSON>et<PERSON><PERSON> } from "jotai";
import useWebSocket, { ReadyState } from "react-use-websocket";
import * as nativebridge from "@nrk/nativebridge";
import {
  IncomingMessage,
  OutgoingMessage,
  OutgoingMessageTypes,
  RenderedMessage,
} from "@/features/chat/types/messages";
import {
  WebSocketOptions,
  WebSocketReturnType,
  WebSocketSender,
} from "@/features/chat/types/web-socket";
import { OutgoingJSBridgeEvents } from "@/common/types/js-bridge-events";
import cookies from "js-cookie";
import {
  chatHistoryAtom,
  connectWebsocketAtom,
  ottoReplyingAtom,
} from "@/features/chat/store/chat";
import { nativeWSDebugAtom } from "@/common/store/nativebridge";
import arrayHasElements from "@/common/utils/array-has-elements";
import useCreateTimestamp from "./use-create-timestamp";
import registerCustomCallbacks from "./custom-callbacks";
import { useCallback, useEffect } from "react";
import { COOKIE_ACCESS_TOKEN_NAME } from "@/common/constants";
import { isHeartbeat } from "../../utils/is-heartbeat";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import { currentTripAtom } from "@/features/chat/store/current-trip";
import { shouldAttemptReconnectCodes } from "../../constants/web-socket";
import {
  chatHistoryHeightAtom,
  chatHistoryOverflowAtom,
  chatHistoryRefAtom,
  chatRefAtom,
} from "../../store/components";
import { DomHandler } from "primereact/utils";

export default function useSocketConnection(
  url: string,
  options?: WebSocketOptions
): WebSocketReturnType {
  const token = cookies.get(COOKIE_ACCESS_TOKEN_NAME);
  const nativeWSDebug = useAtomValue(nativeWSDebugAtom);
  const foreignUser = useAtomValue(foreignUserAtom);
  const currentTrip = useAtomValue(currentTripAtom);
  const [messagesList, setMessagesList] = useAtom(chatHistoryAtom);
  const [connectWebsocket, setConnectWebsocket] = useAtom(connectWebsocketAtom);
  const setIsOttoReplying = useSetAtom(ottoReplyingAtom);
  const createTimestamp = useCreateTimestamp();
  const { onMessage, ...restOptions } = options ?? {};
  const chatHistoryElement = useAtomValue(chatHistoryRefAtom)?.current;
  const chatElement = useAtomValue(chatRefAtom)?.current;
  const setChatHistoryHeight = useSetAtom(chatHistoryHeightAtom);
  const setChatHistoryOverflow = useSetAtom(chatHistoryOverflowAtom);

  const { readyState, sendJsonMessage, lastMessage, lastJsonMessage } =
    useWebSocket(
      url,
      {
        onMessage: (event) => {
          if (isHeartbeat(event)) {
            return;
          }
          typeof onMessage === "function" && onMessage(event);
          registerCustomCallbacks(event, restOptions);
        },
        protocols: ["Authorization", token as string],
        filter: (message) => !isHeartbeat(message),
        ...restOptions,
        shouldReconnect:
          restOptions?.shouldReconnect ??
          ((event) => shouldAttemptReconnectCodes.includes(event.code)),
        share: true,
        heartbeat: true,
      },
      !!token && connectWebsocket && !foreignUser
    );

  const status = {
    [ReadyState.CONNECTING]: "Connecting",
    [ReadyState.OPEN]: "Open",
    [ReadyState.CLOSING]: "Closing",
    [ReadyState.CLOSED]: "Closed",
    [ReadyState.UNINSTANTIATED]: "Uninstantiated",
  }[readyState];

  const send: WebSocketSender = (messages, preventTimestamp) => {
    setIsOttoReplying(true);
    const messagesArray = (
      arrayHasElements(messages) ? messages : [messages]
    ) as OutgoingMessage[];
    const addedMessages: RenderedMessage[] = [];

    if (!preventTimestamp) {
      const timestampMessage = createTimestamp();
      !!timestampMessage && messagesArray.unshift(timestampMessage);
    }

    messagesArray.map((message) => {
      sendJsonMessage<OutgoingMessage>(message);
      try {
        !!nativeWSDebug &&
          nativebridge.emit(OutgoingJSBridgeEvents.WEB_SOCKET_DEBUG, message);
      } catch (e) {}

      if (
        message.type === OutgoingMessageTypes.PROMPT ||
        message.type === OutgoingMessageTypes.SILENT_PROMPT
      ) {
        addedMessages.push(message as RenderedMessage);
      }
    });

    if (chatHistoryElement && chatElement) {
      const isTouchDevice = DomHandler.isTouchDevice();
      const chatInputOffset = isTouchDevice ? 72 : 88;

      setChatHistoryOverflow(
        chatHistoryElement.getBoundingClientRect().bottom -
          window.innerHeight +
          chatInputOffset
      );
      setChatHistoryHeight(chatHistoryElement.scrollHeight);
    }
    setMessagesList((prevMessages) => [...prevMessages, ...addedMessages]);
  };

  const sendInitMessage: WebSocketReturnType["sendInitMessage"] = useCallback(
    (initMessage) => {
      setIsOttoReplying(true);
      sendJsonMessage<OutgoingMessage>(initMessage);
      try {
        !!nativeWSDebug &&
          nativebridge.emit(
            OutgoingJSBridgeEvents.WEB_SOCKET_DEBUG,
            initMessage
          );
      } catch (e) {}
      setMessagesList([initMessage]);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      nativeWSDebug,
      sendJsonMessage,
      setIsOttoReplying,
      setMessagesList,
      currentTrip,
    ]
  );

  const sendSilentInitMessage: WebSocketReturnType["sendInitMessage"] =
    useCallback(
      (initMessage) => {
        sendJsonMessage<OutgoingMessage>(initMessage);
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [sendJsonMessage, currentTrip]
    );

  useEffect(() => {
    const handleOnOnline = () => {
      setConnectWebsocket(true);
    };

    window.addEventListener("online", handleOnOnline);

    return () => {
      window.removeEventListener("online", handleOnOnline);
    };
  }, [sendInitMessage, setConnectWebsocket]);

  return {
    send,
    sendInitMessage,
    sendSilentInitMessage,
    messagesList,
    lastReceivedMessage: lastJsonMessage as IncomingMessage,
    lastMessage,
    status,
    readyState,
  };
}
