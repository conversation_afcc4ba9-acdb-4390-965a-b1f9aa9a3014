import { useAtom, useAtomValue } from "jotai";
import dayjs from "dayjs";
import { previousForeignTripAtom } from "@/features/admin/store/trips";
import { isUserSelectedAtom } from "@/features/admin/store/foreign-user";
import { OutgoingMessageTypes } from "../types/messages";
import { useEffect } from "react";
import { isMobile } from "@/common/utils/dom";

export default function useInitMessage(tripId: number, openingMessage?: string | null) {
  const isUserSelected = useAtomValue(isUserSelectedAtom);
  const [previousForeignTrip, setPreviousForeignTrip] = useAtom(
    previousForeignTripAtom
  );

  useEffect(() => {
    return () => {
      setPreviousForeignTrip(undefined);
    };
  }, [setPreviousForeignTrip]);

  return () => {
    if (isUserSelected) {
      setPreviousForeignTrip(tripId);
      return;
    }

    if (previousForeignTrip !== tripId) {
      setPreviousForeignTrip(undefined);
      return {
        clientTimezone: dayjs.tz.guess(),
        type: OutgoingMessageTypes.TRIP_INIT,
        tripId: tripId,
        isMobile: isMobile(),
        openingMessage: openingMessage || undefined,
      };
    }

    return;
  };
}
