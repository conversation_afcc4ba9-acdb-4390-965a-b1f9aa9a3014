import { useEffect, useRef, useState } from "react";
import dayjs from "dayjs";

export default function useExpiry(limit: string): boolean {
  const timeLeft = dayjs.utc(limit).diff();
  const timerSet = useRef(false);
  const [expired, setExpired] = useState<boolean>(timeLeft <= 0);

  useEffect(() => {
    if (!timerSet.current) {
      timerSet.current = true;
      if (timeLeft > 0) {
        setTimeout(() => {
          setExpired(true);
        }, timeLeft);
      }
    }
  }, [timeLeft, timerSet]);

  return expired;
}
