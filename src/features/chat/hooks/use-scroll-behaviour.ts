import {
  chat<PERSON>eader<PERSON><PERSON>,
  chatHistory<PERSON>ei<PERSON><PERSON>tom,
  chatHistory<PERSON><PERSON><PERSON><PERSON>tom,
  chatHistoryRef<PERSON>tom,
  chatRef<PERSON>tom,
  sampleTripsRefAtom,
} from "@/features/chat/store/components";
import { useAtomValue } from "jotai";
import { useCallback, useEffect, useRef } from "react";
import { chatH<PERSON><PERSON><PERSON><PERSON>, ottoReplying<PERSON>tom } from "../store/chat";
import { IncomingMessage, IncomingMessageTypes } from "../types/messages";
import { useStoreToggle } from "@/common/hooks/toggles";
import { enableUpdatedScrollBehaviourFlagAtom } from "@/features/user/store/feature-flags";
import { useUserProfile } from "@/features/user/hooks/api";

const useSampleTripsOffset = () => {
  const sampleTripsElement = useAtomValue(sampleTripsRefAtom)?.current;
  const headerElement = useAtomValue(chatHeaderAtom)?.current;

  const {
    sampleTrips: { shouldShowSampleTrips },
  } = useUserProfile();

  const sampleTripsTopOffset =
    !!sampleTripsElement && headerElement
      ? sampleTripsElement.getBoundingClientRect().top -
        headerElement.getBoundingClientRect().bottom
      : 12;

  const sampleTripsHeight = shouldShowSampleTrips
    ? sampleTripsElement?.offsetHeight ?? 112
    : 0;

  return sampleTripsTopOffset + sampleTripsHeight;
};

export default function useScrollBehaviour() {
  const chatElement = useAtomValue(chatRefAtom)?.current;
  const headerElement = useAtomValue(chatHeaderAtom)?.current;
  const chatHistoryHeight = useAtomValue(chatHistoryHeightAtom);
  const chatHistoryOverflow = useAtomValue(chatHistoryOverflowAtom);
  const chatHistoryElement = useAtomValue(chatHistoryRefAtom)?.current;
  const history = useAtomValue(chatHistoryAtom);

  const lastMessageRef = useRef<HTMLDivElement>(null);
  const lastMessage = history.at(-1) as IncomingMessage;

  const { value: enableUpdatedScrollBehaviour } = useStoreToggle(
    enableUpdatedScrollBehaviourFlagAtom
  );

  const isOttoReplying = useAtomValue(ottoReplyingAtom);

  const sampleTripsOffset = useSampleTripsOffset();

  useEffect(() => {
    if (!enableUpdatedScrollBehaviour) return;

    const lastMessageElement = lastMessageRef?.current;

    if (!headerElement || !lastMessageElement || !chatHistoryElement) {
      return;
    }

    if (
      lastMessage?.type === IncomingMessageTypes.PROMPT &&
      !lastMessage?.isBotMessage
    ) {
      const chatHistoryStyle = getComputedStyle(chatHistoryElement);
      const chatHistoryPadding =
        parseInt(chatHistoryStyle.paddingTop, 10) +
        2 * parseInt(chatHistoryStyle.paddingBottom, 10);

      const addedHeight =
        lastMessageElement.getBoundingClientRect().top -
        headerElement.offsetHeight -
        chatHistoryOverflow -
        chatHistoryPadding -
        sampleTripsOffset;

      (chatHistoryElement as HTMLDivElement).style.setProperty(
        "--min-height",
        `${chatHistoryHeight + addedHeight}px`
      );
    }
  }, [
    chatHistoryElement,
    chatHistoryHeight,
    chatHistoryOverflow,
    enableUpdatedScrollBehaviour,
    headerElement,
    lastMessage,
    sampleTripsOffset,
  ]);

  useEffect(() => {
    if (!!enableUpdatedScrollBehaviour) return;

    if (!chatElement) {
      return;
    }
    const hasUserScrolled =
      chatElement.getAttribute("data-scrolled") === "true";

    if (!hasUserScrolled) {
      chatElement.scrollTo(0, chatElement?.scrollHeight);
    }
  }, [chatElement, enableUpdatedScrollBehaviour, history, isOttoReplying]);

  useEffect(() => {
    if (!enableUpdatedScrollBehaviour) return;

    if (!chatElement || !chatHistoryElement) {
      return;
    }

    const shouldScroll =
      lastMessage?.type === IncomingMessageTypes.HISTORY ||
      (lastMessage?.type === IncomingMessageTypes.PROMPT &&
        !lastMessage?.isBotMessage);

    if (shouldScroll) {
      chatElement.scrollTo(0, chatElement.scrollHeight);
    } else if (
      chatHistoryElement.getBoundingClientRect().bottom >
      chatElement.getBoundingClientRect().bottom
    ) {
      setTimeout(() => chatElement.setAttribute("data-scrolled", "true"), 0);
    }
  }, [
    chatElement,
    chatHistoryElement,
    enableUpdatedScrollBehaviour,
    lastMessage,
  ]);

  return lastMessageRef;
}

export const useSilentPromptScroll = () => {
  const chatHistoryElement = useAtomValue(chatHistoryRefAtom)?.current;
  const chatElement = useAtomValue(chatRefAtom)?.current;
  const sampleTripsOffset = useSampleTripsOffset();

  const { value: enableUpdatedScrollBehaviour } = useStoreToggle(
    enableUpdatedScrollBehaviourFlagAtom
  );

  return useCallback(() => {
    if (!enableUpdatedScrollBehaviour || !chatElement || !chatHistoryElement) {
      return;
    }

    const chatHistoryElementStyle = window.getComputedStyle(chatHistoryElement);
    const chatHistoryPadding =
      parseInt(chatHistoryElementStyle.paddingTop, 10) +
      2 * parseInt(chatHistoryElementStyle.paddingBottom, 10);

    const newHeight =
      chatHistoryElement.scrollHeight +
      chatElement.offsetHeight -
      chatHistoryPadding -
      sampleTripsOffset;

    (chatHistoryElement as HTMLDivElement).style.setProperty(
      "--min-height",
      `${newHeight}px`
    );
    setTimeout(
      () =>
        chatElement.scrollTo({
          top: newHeight,
          behavior: "smooth",
        }),
      0
    );
  }, [
    chatElement,
    chatHistoryElement,
    enableUpdatedScrollBehaviour,
    sampleTripsOffset,
  ]);
};
