import { useEffect, useState } from "react";
import dayjs from "dayjs";

export default function useIntervalTimestamp() {
  const [currentTimestamp, setCurrentTimestamp] = useState<string>(
    dayjs().toISOString()
  );

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTimestamp(dayjs().toISOString());
    }, 60000);

    const timeout = setTimeout(() => clearInterval(interval), 2700000); //clear after 45 minutes

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
    };
  }, []);

  return currentTimestamp;
}
