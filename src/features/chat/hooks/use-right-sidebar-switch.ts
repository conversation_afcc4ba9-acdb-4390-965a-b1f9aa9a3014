import { use<PERSON>tom } from "jotai";
import { rightSidebarAtom } from "@/features/chat/store/sidebar";
import RightSidebars from "@/features/chat/types/right-sidebars";

export default function useRightSidebarSwitch() {
  const [rightSidebar, setRightSidebar] = useAtom(rightSidebarAtom);

  function switchTo(sidebarToOpen: RightSidebars | null) {
    if (sidebarToOpen === rightSidebar) {
      return;
    }

    setTimeout(
      () => setRightSidebar(sidebarToOpen),
      !!rightSidebar && rightSidebar !== sidebarToOpen ? 300 : 0
    );
    setRightSidebar(null);
  }

  function close() {
    setRightSidebar(null);
  }

  function toggle(sidebarToToggle: RightSidebars) {
    if (!rightSidebar) {
      setRightSidebar(sidebarToToggle);
    } else if (rightSidebar === sidebarToToggle) {
      setRightSidebar(null);
    } else {
      switchTo(sidebarToToggle);
    }
  }

  return {
    closeRightSidebar: close,
    rightSidebar: rightSidebar,
    switchTo,
    toggleRightSidebar: toggle,
  };
}
