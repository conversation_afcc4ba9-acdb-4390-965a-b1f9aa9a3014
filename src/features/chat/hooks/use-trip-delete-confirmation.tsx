import defaultTripName from "@/features/chat/constants/default-trip-name";
import { useDeleteTrip } from "../../../common/hooks/api";
import useConfirmPopup from "@/common/hooks/use-confirm-popup";
import BackDrop from "@/common/components/backdrop";

export default function useTripDeleteConfirmation(id: number, title: string) {
  const { trigger: deleteTrip } = useDeleteTrip(id);
  const hasDestination = title !== defaultTripName;
  const deleteConfirmMessage = hasDestination
    ? `Are you sure you want to delete your trip to ${title}?`
    : "Are you sure you want to delete this trip?";

  const message = (
    <>
      <h2 className="font-medium pb-2 border-b border-neutral-250 dark:border-neutral-700">
        Delete this trip?
      </h2>
      <p className="leading-5 mt-6">
        {deleteConfirmMessage} <br />
        This action cannot be undone.
      </p>
    </>
  );

  const {
    open: openDeleteDialog,
    Popup,
    close,
    isOpen,
  } = useConfirmPopup({
    message,
    onConfirm: deleteTrip,
  });

  const DeleteDialog = (
    <>
      {Popup}
      {isOpen && <BackDrop onClick={close} usePortal className="z-20" />}
    </>
  );

  return {
    openDeleteDialog,
    DeleteDialog,
  };
}
