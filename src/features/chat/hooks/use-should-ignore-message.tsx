import { useAtomValue } from "jotai";
import { ROUTES } from "@/common/constants";
import { useTripsList } from "@/common/hooks/api";
import { useCurrentRoute } from "@/common/hooks/use-current-route";
import { currentTripAtom } from "@/features/chat/store/current-trip";

export default function useShouldIgnoreMessage() {
  const currentRoute = useCurrentRoute();
  const currentTripAtomValue = useAtomValue(currentTripAtom);
  const { data } = useTripsList();

  return (messageThreadId: number | undefined) => {
    if (!messageThreadId) {
      return false;
    }

    const {
      onboarding_thread_id,
      preferences_thread_id,
      travel_policy_thread_id,
    } = data ?? {};

    let currentTrip: number | null | undefined;

    switch (currentRoute) {
      case ROUTES.ONBOARDING:
        currentTrip = onboarding_thread_id as number;
        break;
      case ROUTES.PREFERENCES:
        currentTrip = preferences_thread_id as number;
        break;
      case ROUTES.TRAVEL_POLICY:
      case ROUTES.ADMIN_SETTINGS_TRAVEL_POLICY:
        currentTrip = travel_policy_thread_id as number;
        break;
      default:
        currentTrip = currentTripAtomValue as number;
    }

    return currentTrip !== messageThreadId;
  };
}
