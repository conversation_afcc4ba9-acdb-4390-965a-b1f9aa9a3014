import { usePathname, useRouter } from "next/navigation";
import { ROUTES } from "@/common/constants";
import { useCreateTrip } from "@/common/hooks/api";
import { useGetSessionHistory } from "@/features/user/hooks/api";
import SessionHistory from "@/features/user/types/session-history";
import { showLoginLoaderAtom } from "@/features/user/store/login";
import { useSetAtom } from "jotai";
import { useCallback } from "react";
import { useIsTryingMode } from "@/common/hooks/use-trying-mode";

export function useRedirectToLastTrip() {
  const router = useRouter();
  const { createTrip } = useCreateTrip();
  const { getSessionHistory } = useGetSessionHistory();

  const redirect = useCallback(
    async (sessionHistory?: SessionHistory) => {
      const session = sessionHistory || (await getSessionHistory());
      const { last_checkpoint_trip_id, current_trip_id } = session ?? {};

      if (!!current_trip_id) {
        router.push(`${ROUTES.TRIPS}/${current_trip_id}`);
      } else if (!!last_checkpoint_trip_id) {
        router.push(`${ROUTES.TRIPS}/${last_checkpoint_trip_id}`);
      } else {
        createTrip();
      }
    },
    [createTrip, getSessionHistory, router]
  );

  return redirect;
}

export function useOnboardingRedirect() {
  const showLoginLoader = useSetAtom(showLoginLoaderAtom);
  const { getSessionHistory, isLoading } = useGetSessionHistory();
  const redirectToLastTrip = useRedirectToLastTrip();
  const tryingMode = useIsTryingMode();
  const router = useRouter();
  const pathname = usePathname();

  const onboardingRedirect = useCallback(async () => {
    if (isLoading) {
      return;
    }

    const sessionHistory = await getSessionHistory();
    const { is_onboarding_completed, current_trip_id } = sessionHistory ?? {};
    const shouldRedirectToOnboarding = !is_onboarding_completed && !tryingMode;

    if (shouldRedirectToOnboarding) {
      router.push(ROUTES.ONBOARDING);
    } else {
      redirectToLastTrip(sessionHistory ?? undefined);
    }
    // Loader hidden after redirect from mobile
    if (pathname === ROUTES.LOGIN) {
      setTimeout(() => showLoginLoader(false), 100);
    }
  }, [
    getSessionHistory,
    isLoading,
    pathname,
    redirectToLastTrip,
    router,
    showLoginLoader,
    tryingMode,
  ]);

  return {
    isLoading,
    onboardingRedirect,
  };
}
