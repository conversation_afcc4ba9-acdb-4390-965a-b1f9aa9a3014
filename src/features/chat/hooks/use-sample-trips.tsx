import { fetcher } from "@/common/api/fetcher";
import { ApiPaths } from "@/common/constants";
import useSWRMutation from "swr/mutation";
import { OutgoingMessageTypes } from "../types/messages";
import { useAtomValue } from "jotai";
import { chatHistoryAtom } from "../store/chat";
import { useRef } from "react";


export function useSampleTrips() {
  const { data: sampleTrips, trigger: fetchSampleTrips, reset: resetSampleTrips } = useSWRMutation<{
    trips: Array<{ title: string; description: string; prompt: string }>;
  }>(ApiPaths.TRIP_SAMPLES, fetcher);
  
  // Track if we've already fetched sample trips
  const hasFetchedRef = useRef(false);
  
  // Get chat history to check if user has sent any messages
  const chatHistory = useAtomValue(chatHistoryAtom);
  
  // Check if user has sent any messages
  const hasUserSentMessages = chatHistory.some(
    (message) => 'type' in message && message.type === OutgoingMessageTypes.PROMPT
  );
  
  // Only fetch if we haven't fetched before, user hasn't sent messages, and a sample card hasn't been selected for this trip
  const shouldFetchSampleTrips = !hasFetchedRef.current && !hasUserSentMessages;
  
  // Update the ref after fetching
  const fetchWithTracking = () => {
    if (shouldFetchSampleTrips) {
      hasFetchedRef.current = true;
      return fetchSampleTrips();
    }
    return Promise.resolve();
  };

  // Clear sample trips and reset fetched state
  const clearSampleTrips = () => {
    resetSampleTrips();
    hasFetchedRef.current = false;
  };

  return {
    sampleTrips,
    fetchSampleTrips: fetchWithTracking,
    shouldFetchSampleTrips,
    clearSampleTrips,
  };
}
