import {
  IncomingMessage,
  IncomingMessageTypes,
  OutgoingMessage,
  OutgoingMessageTypes,
} from "@/features/chat/types/messages";

export const onboardingInitMessage: OutgoingMessage = {
  type: OutgoingMessageTypes.ONBOARDING_INIT,
};
export const preferencesInitMessage: OutgoingMessage = {
  type: OutgoingMessageTypes.PREFERENCES_INIT,
};

export const futureTripsInitMessage: OutgoingMessage = {
  type: OutgoingMessageTypes.FUTURE_TRIPS_INIT,
};

export const travelPolicyInitMessage: OutgoingMessage = {
  type: OutgoingMessageTypes.TRAVEL_POLICY_INIT,
};

export const refreshFlightsMessage: OutgoingMessage = {
  type: OutgoingMessageTypes.SILENT_PROMPT,
  text: "Can you please do the flights search again?",
};

export const refreshHotelsMessage: OutgoingMessage = {
  type: OutgoingMessageTypes.SILENT_PROMPT,
  text: "Can you please do the hotels search again?",
};

export const noTripsSelected: OutgoingMessage = {
  type: OutgoingMessageTypes.SILENT_PROMPT,
  text: "I don't want to create trips for any of these",
};

export const noPreferencesSelected: OutgoingMessage = {
  type: OutgoingMessageTypes.SILENT_PROMPT,
  text: "I don't want any of these preferences",
};

export const onboardingEndMessage: IncomingMessage = {
  type: IncomingMessageTypes.PROMPT,
  text: "You will now be redirected to the new trip creation.",
  isBotMessage: true,
};

export const renderedMessages = [
  IncomingMessageTypes.ERROR,
  IncomingMessageTypes.PROMPT,
  IncomingMessageTypes.SEARCH_UPDATE,
  IncomingMessageTypes.AI_HUMAN,
  IncomingMessageTypes.FORK_NEW_TRIP,
  IncomingMessageTypes.REDIRECT_TO_TRIP,
  OutgoingMessageTypes.PROMPT,
];
