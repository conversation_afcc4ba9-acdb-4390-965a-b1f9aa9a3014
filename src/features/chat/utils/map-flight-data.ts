import arrayHasElements from "@/common/utils/array-has-elements";
import { FlightData, MappedFlightData } from "@/features/chat/types/flights";
import { airlineLogos } from "@/common/constants/airline-logos";
import objectHasEntries from "@/common/utils/object-has-entries";
import {
  formatFlightDuration,
  formatFlightNumbers,
  formatFlightTimes,
  getArrivalDays,
} from "@/common/utils/flight";
import { FlightStop } from "../types/api";

export function mapFlightCardData(data: FlightData): MappedFlightData {
  const {
    action,
    cabin,
    cancellation_policy,
    exchange_policy,
    flight_segments,
    keyword,
    id,
    price,
    credits,
    net_price,
    recommendationReasons,
    type,
    within_or_out_policy_reason,
    within_policy,
    seat_selection_policy,
    boarding_policy,
    booking_code,
    total_distance_miles,
    operating_airline_code,
    operating_flight_number,
    source,
    is_red_eye,
  } = data;

  const firstSegment = arrayHasElements(flight_segments)
    ? flight_segments[0]
    : null;
  const hasFlightStops =
    !!firstSegment && arrayHasElements(firstSegment.flight_stops);
  const firstStop = hasFlightStops ? firstSegment.flight_stops[0] : null;
  const lastStop = hasFlightStops
    ? firstSegment.flight_stops[firstSegment.flight_stops.length - 1]
    : null;
  const {
    airline_code,
    airline_name,
    departure_timezone,
    origin_code,
    origin_name,
  } = firstStop ?? {};
  const { arrival_timezone, destination_code, destination_name } =
    lastStop ?? {};
  const hasMultipleAirlines =
    new Set(firstSegment?.flight_stops.map(({ airline_code }) => airline_code))
      .size > 1;
  const { departureTime, arrivalTime, departureDate, arrivalDate } =
    formatFlightTimes(data);
  const arrivalDays = getArrivalDays(departureDate, arrivalDate);
  const middleStops = firstSegment?.flight_stops.slice(1);

  return {
    userAction: action,
    airlineImg: !hasMultipleAirlines
      ? {
          alt: `Logo image of ${airline_name}`,
          src: !!airline_code ? airlineLogos[airline_code] : null,
        }
      : undefined,
    airlineName: hasMultipleAirlines ? "Multiple airlines" : airline_name,
    arrival: {
      airport: {
        name: destination_name ?? "",
        code: destination_code ?? "",
      },
      time: arrivalTime,
      timezone: departure_timezone ?? "",
    },
    cancellation_policy,
    exchange_policy,
    departure: {
      airport: {
        name: origin_name ?? "",
        code: origin_code ?? "",
      },
      time: departureTime,
      timezone: arrival_timezone ?? "",
    },
    flightNumbers: formatFlightNumbers(data),
    duration: formatFlightDuration(data),
    keyword,
    id,
    price: price ?? undefined,
    credits: credits ?? undefined, // Already correctly typed as { amount: string; currency: string }
    net_price: net_price ?? undefined,
    cabin: cabin ?? "Unknown",
    recommendationReasons: objectHasEntries(recommendationReasons)
      ? Object.values(recommendationReasons)
      : [],
    stops: hasFlightStops ? formatStopsString(middleStops) : "",
    middleStops,
    type,
    within_or_out_policy_reason,
    within_policy,
    seat_selection_policy,
    boarding_policy,
    booking_code,
    total_distance_miles,
    operating_airline_code,
    operating_flight_number,
    source,
    is_red_eye,
    arrivalDays: arrivalDays || undefined,
  };
}

export function formatStopsString(stops?: FlightStop[]) {
  return stops?.length
    ? `${stops.length} ${stops.length > 1 ? "stops" : "stop"} in ${stops
        .map((stop) => stop.origin_code)
        .join(" and ")}`
    : "Nonstop";
}
