import dayjs from "dayjs";

export default function Timestamp({
  timestamp,
}: {
  timestamp: number | string;
}) {
  const formattedTime = dayjs
    .utc(timestamp)
    .tz(dayjs.tz.guess())
    .format("dddd, MMMM Do h:mm a");

  return (
    <p className="flex items-center gap-x-2.5 my-8 text-sm text-neutral-500 first:mt-0">
      <span>{formattedTime}</span>
      <span className="flex-1 h-px bg-neutral-250 dark:bg-neutral-700" />
    </p>
  );
}
