import Image from "next/image";
import { useAtom } from "jotai";
import clsx from "clsx";
import { ScrollPanel } from "primereact/scrollpanel";
import { TabPanel, TabView } from "primereact/tabview";
import { airlineLogos } from "@/common/constants/airline-logos";
import { rawToolOutputAtom } from "@/features/chat/store/debugging";
import Popup from "@/common/components/popup";

export default function RawOutputPopup() {
  const [rawToolOutput, setRawToolOutput] = useAtom(rawToolOutputAtom);
  const { flight_choices, hotel_choices } = rawToolOutput ?? {};

  if (!rawToolOutput) {
    return null;
  }

  return (
    <Popup
      className="h-3/4 w-2/3 overflow-hidden"
      onClose={() => setRawToolOutput(null)}
      pt={{
        content: { className: "h-full" },
        toggleableContent: { className: "h-[calc(100%-1.5rem)]" },
      }}
    >
      <TabView
        pt={{
          root: { className: "flex flex-col h-full" },
          panelContainer: { className: "overflow-hidden" },
        }}
      >
        <TabPanel header="Table" pt={{ content: { className: "h-full" } }}>
          <ScrollPanel
            pt={{
              root: {
                className: "h-full",
              },
            }}
          >
            <table className="border border-neutral-900 w-full">
              <tbody>
                {flight_choices?.map((choice: any, choiceIndex: number) =>
                  choice.split(" | ").map((flight: any, rowIndex: number) => (
                    <tr
                      className={clsx("border-t border-x border-neutral-300", {
                        "bg-neutral-100 dark:bg-gray-800": choiceIndex % 2 === 1,
                        "border-neutral-900": rowIndex === 0,
                      })}
                      key={rowIndex}
                    >
                      {flight
                        .split("\t")
                        .map((item: string, colIndex: number) => (
                          <td
                            className="border-x border-neutral-300 p-4 text-center"
                            key={colIndex}
                          >
                            {
                              (rowIndex === 0 || rowIndex === 2) &&
                                item
                                  .replace(/_/g, " ") // Replace underscores with spaces
                                  .replace(/\b\w/g, (char) =>
                                    char.toUpperCase()
                                  ) // Capitalize first letter of each word
                            }
                            {(rowIndex === 1 || rowIndex > 2) && item}
                          </td>
                        ))}
                    </tr>
                  ))
                )}
              </tbody>
            </table>

            <table className="border border-neutral-900 w-full">
              <tbody>
                {hotel_choices?.map((choice, index) => {
                  const { name, rate_per_night } = choice;

                  return (
                    <tr
                      className="border-t border-x border-neutral-300 even:bg-neutral-100 dark:even:bg-gray-900"
                      key={index}
                    >
                      <td className="border-x border-neutral-300 p-4">
                        {name}
                      </td>
                      <td className="border-x border-neutral-300 p-4 text-center">
                        {rate_per_night}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </ScrollPanel>
        </TabPanel>

        <TabPanel header="JSON" pt={{ content: { className: "h-full" } }}>
          <ScrollPanel
            pt={{
              root: {
                className: "h-full",
              },
            }}
          >
            <pre className="block mt-4 text-wrap">
              {JSON.stringify(rawToolOutput, null, 2)}
            </pre>
          </ScrollPanel>
        </TabPanel>
      </TabView>
    </Popup>
  );
}
