import clsx from "clsx";
import { forwardRef, PropsWithChildren, RefObject, useEffect } from "react";

type WithMessagesContainerProps = PropsWithChildren<{
  className?: string;
  forwardRef?: RefObject<HTMLDivElement>;
}>;

export default function WithMessagesContainer({
  children,
  className,
  forwardRef,
}: WithMessagesContainerProps) {
  return (
    <div
      className={clsx("box-content max-w-218 mx-auto px-4 md:px-8", className)}
      ref={forwardRef}
    >
      {children}
    </div>
  );
}
