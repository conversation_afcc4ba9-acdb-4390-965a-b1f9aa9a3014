import IconCheck from "@/common/components/icons/check";
import IconClose from "@/common/components/icons/close";
import colors from "@/common/constants/colors";
import RecommendationPanel from "@/features/chat/components/recommendation-reasons/recommendation-panel";
import { twMerge } from "tailwind-merge";

interface WithinPolicyProps {
  className?: string;
  reason?: string;
  overlayClassName?: string;
  withinPolicy: boolean | null;
}

export default function WithinPolicy({
  className,
  reason,
  overlayClassName,
  withinPolicy,
}: WithinPolicyProps) {
  if (withinPolicy === null) return null;

  const PolicyStatus = () => (
    <p className="flex items-center gap-x-2 text-nowrap">
      Within policy:
      {withinPolicy ? (
        <IconCheck className="flex-0 basis-4 text-primary-500" size={12} />
      ) : (
        <IconClose className="flex-0 basis-3 h-3" fill={colors.red[500]} />
      )}
    </p>
  );

  if (!reason) {
    return (
      <div className={twMerge("mt-4 text-sm", className)}>
        <PolicyStatus />
      </div>
    );
  }

  return (
    <RecommendationPanel
      title={<PolicyStatus />}
      reasons={[reason]}
      showCheckmark={false}
      pt={{
        popup: { className: overlayClassName },
      }}
    />
  );
}
