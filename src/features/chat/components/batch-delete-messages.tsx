import IconTrash from "@/common/components/icons/trash-icon";
import { ApiRestMethod } from "@/common/constants";
import { isDevelopment, isStaging } from "@/common/utils/env";
import {
  chatHistoryAtom,
  messageSelectionModeAtom,
  selectedMessagesAtom
} from "@/features/chat/store/chat";
import {
  enableDeleteMessageFlagAtom,
} from "@/features/chat/store/debugging";
import { useUserProfile } from "@/features/user/hooks/api";
import { UserRole } from "@/features/user/types/user";
import { useAtomValue, useSetAtom } from "jotai";
import { Button } from "primereact/button";
import { Toast } from "primereact/toast";
import { useEffect, useRef } from "react";

export default function BatchDeleteMessages() {
  const { role } = useUserProfile();
  const messages = useAtomValue(chatHistoryAtom);
  const isSelectionMode = useAtomValue(messageSelectionModeAtom);
  const setIsSelectionMode = useSetAtom(messageSelectionModeAtom);
  const selectedMessages = useAtomValue(selectedMessagesAtom);
  const setSelectedMessages = useSetAtom(selectedMessagesAtom);
  const isAdminAndNonProd = role === UserRole.ADMIN && (isDevelopment() || isStaging());
  const isDeleteEnabled = useAtomValue(enableDeleteMessageFlagAtom);
  const toastRef = useRef<Toast>(null);

  useEffect(() => {
    return () => {
      setIsSelectionMode(false);
      setSelectedMessages([]);
    };
  }, [messages.length, setIsSelectionMode, setSelectedMessages]);

  if (!isAdminAndNonProd || !isDeleteEnabled) {
    return null;
  }

  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedMessages([]);
  };

  const handleBatchDelete = async () => {
    if (selectedMessages.length === 0) {
      toastRef.current?.show({
        severity: 'warn',
        summary: 'No messages selected',
        detail: 'Please select at least one message to delete',
        life: 3000
      });
      return;
    }

    try {
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || '';
      const response = await fetch(`${backendUrl}/api/admin/messages/batch`, {
        method: ApiRestMethod.DELETE,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ checkpoint_ids: selectedMessages }),
      });

      if (response.ok) {
        window.location.reload();
      } else {
        toastRef.current?.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to delete messages',
          life: 3000
        });
      }
    } catch (error) {
      console.error('Error deleting messages:', error);
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred while deleting messages',
        life: 3000
      });
    }
  };

  return (
    <>
      <Toast ref={toastRef} position="top-right" />
      <div className="fixed bottom-20 right-4 z-10 flex flex-col gap-2">
        {isSelectionMode && (
          <Button
            className="p-2"
            disabled={selectedMessages.length === 0}
            icon={<IconTrash className="size-5" />}
            onClick={handleBatchDelete}
            severity="danger"
            tooltip="Delete selected messages"
            tooltipOptions={{ position: 'left' }}
          />
        )}
        <Button
          className="p-2"
          icon={
            isSelectionMode ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 6h18"></path>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
              </svg>
            )
          }
          onClick={toggleSelectionMode}
          outlined={!isSelectionMode}
          severity={isSelectionMode ? "secondary" : "danger"}
          tooltip={isSelectionMode ? "Cancel selection" : "Select messages to delete"}
          tooltipOptions={{ position: 'left' }}
        />
      </div>
      {isSelectionMode && (
        <style jsx global>{`
          .message-selectable {
            position: relative;
          }
          .message-selectable::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.05);
            z-index: 1;
            border-radius: 0.5rem;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
          }
          .message-selectable:hover::before {
            opacity: 1;
          }
          .message-selected::before {
            background-color: rgba(59, 130, 246, 0.1);
            opacity: 1;
            border: 2px solid #3b82f6;
          }
          .message-checkbox {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            z-index: 2;
            display: none;
          }
          .message-selectable:hover .message-checkbox,
          .message-selected .message-checkbox {
            display: block;
          }
        `}</style>
      )}
    </>
  );
}

export const useMessageSelection = (messageId?: number) => {
  const isSelectionMode = useAtomValue(messageSelectionModeAtom);
  const selectedMessages = useAtomValue(selectedMessagesAtom);
  const setSelectedMessages = useSetAtom(selectedMessagesAtom);

  const isSelected = messageId ? selectedMessages.includes(messageId) : false;

  const toggleSelection = () => {
    if (!messageId) return;

    if (isSelected) {
      setSelectedMessages(selectedMessages.filter((id: number) => id !== messageId));
    } else {
      setSelectedMessages([...selectedMessages, messageId]);
    }
  };

  return {
    isSelectionMode,
    isSelected,
    toggleSelection
  };
};
