import { SVGProps } from "react";

export default function OttoAvatar(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 40 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="20" cy="20.969" r="20" fill="url(#paint0_linear_585_3369)" />
      <path
        d="M20.1017 32.5459C24.2899 32.5459 27.963 30.3642 30.0225 27.087H32.8513C30.562 31.7699 25.7132 35 20.1017 35C14.4903 35 9.64149 31.7699 7.35213 27.087H10.181C12.2405 30.3642 15.9135 32.5459 20.1017 32.5459Z"
        fill="url(#paint1_linear_585_3369)"
      />
      <path
        d="M20.1017 9.45406C15.1765 9.45406 10.9637 12.4713 9.24952 16.7391H6.61538C8.43784 11.0906 13.7868 7 20.1017 7C26.4166 7 31.7656 11.0906 33.5881 16.7391H30.954C29.2398 12.4713 25.027 9.45406 20.1017 9.45406Z"
        fill="url(#paint2_linear_585_3369)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M26.1904 21.9081C26.1904 24.2054 27.7291 25.5551 30.0952 25.5551C32.4613 25.5551 34 24.2054 34 21.9081C34 19.5964 32.4613 18.2324 30.0952 18.2324C27.7291 18.2324 26.1904 19.5964 26.1904 21.9081ZM31.7936 21.9081C31.7936 23.1573 31.1694 23.8895 30.0952 23.8895C29.021 23.8895 28.3969 23.1573 28.3969 21.9081C28.3969 20.6446 29.021 19.898 30.0952 19.898C31.1694 19.898 31.7936 20.6446 31.7936 21.9081Z"
        fill="url(#paint3_linear_585_3369)"
      />
      <path
        d="M25.5269 20.0415H23.2334V22.8844C23.2334 23.6167 23.5818 23.8464 24.4527 23.8464C24.7576 23.8464 25.2076 23.8034 25.4543 23.7459V25.3971C25.2656 25.4402 24.656 25.555 24.0608 25.555C21.8254 25.555 21.027 24.5787 21.027 22.9993V20.0415H17.6738V22.8844C17.6738 23.6167 18.0222 23.8464 18.8931 23.8464C19.198 23.8464 19.648 23.8034 19.8947 23.7459V25.3971C19.706 25.4402 19.0964 25.555 18.5012 25.555C16.2658 25.555 15.4674 24.5787 15.4674 22.9993V20.0415H14.4077V18.3473H15.2642C15.598 18.3473 15.7142 18.1606 15.7577 17.7298L15.8593 16.7391H17.6738V18.3473H20.8238C21.1576 18.3473 21.2737 18.1606 21.3173 17.7298L21.4189 16.7391H23.2334V18.3473H25.5269V20.0415Z"
        fill="url(#paint4_linear_585_3369)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6 21.9081C6 24.2054 7.53869 25.5551 9.90478 25.5551C12.2709 25.5551 13.8096 24.2054 13.8096 21.9081C13.8096 19.5964 12.2709 18.2324 9.90478 18.2324C7.53869 18.2324 6 19.5964 6 21.9081ZM11.6031 21.9081C11.6031 23.1573 10.979 23.8895 9.90478 23.8895C8.8306 23.8895 8.20642 23.1573 8.20642 21.9081C8.20642 20.6446 8.8306 19.898 9.90478 19.898C10.979 19.898 11.6031 20.6446 11.6031 21.9081Z"
        fill="url(#paint5_linear_585_3369)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_585_3369"
          x1="-4.23932"
          y1="-5.83101"
          x2="33.8823"
          y2="1.54842"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#014B6E" />
          <stop offset="1" stopColor="#030D12" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_585_3369"
          x1="7.07692"
          y1="10.3478"
          x2="25.3148"
          y2="30.9372"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3DDFD6" />
          <stop offset="1" stopColor="#36B5F3" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_585_3369"
          x1="7.07692"
          y1="10.3478"
          x2="25.3148"
          y2="30.9372"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3DDFD6" />
          <stop offset="1" stopColor="#36B5F3" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_585_3369"
          x1="7.07692"
          y1="10.3478"
          x2="25.3148"
          y2="30.9372"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3DDFD6" />
          <stop offset="1" stopColor="#36B5F3" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_585_3369"
          x1="7.07692"
          y1="10.3478"
          x2="25.3148"
          y2="30.9372"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3DDFD6" />
          <stop offset="1" stopColor="#36B5F3" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_585_3369"
          x1="7.07692"
          y1="10.3478"
          x2="25.3148"
          y2="30.9372"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3DDFD6" />
          <stop offset="1" stopColor="#36B5F3" />
        </linearGradient>
      </defs>
    </svg>
  );
}
