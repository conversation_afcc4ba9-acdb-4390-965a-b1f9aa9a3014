import React from "react";
import { use<PERSON>tom } from "jotai";
import { But<PERSON> } from "primereact/button";
import { useRouter } from "next/navigation";
import Checklist from "../../../common/components/checklist";
import { calendarIntegrationModalAtom } from "../store/chat";
import { ROUTES } from "../../../common/constants";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCheck,
  faLightbulbExclamationOn,
} from "@fortawesome/pro-regular-svg-icons";
import { Panel } from "primereact/panel";
import BackDrop from "@/common/components/backdrop";

export default function CalendarIntegrationModal() {
  const [showModal, setShowModal] = useAtom(calendarIntegrationModalAtom);
  const router = useRouter();

  const handleClose = () => {
    setShowModal(false);
  };

  const handleConnectCalendar = () => {
    router.push(ROUTES.SETTINGS);
    setShowModal(false);
  };

  if (!showModal) {
    return null;
  }

  return (
    <>
      <BackDrop className="z-50" onClick={handleClose} />
      <Panel className="fixed left-0 pt-14 rounded-none size-full top-0 z-50 md:flex md:flex-col md:justify-center md:left-1/2 md:max-w-[63.75rem] md:pt-6 md:-translate-x-1/2 md:w-9/12 dark:bg-neutral-800">
        <div className="flex flex-col items-center w-full">
          <div className="bg-gray-100 flex items-center justify-center mb-8 rounded-full size-[3.875rem] md:mb-13 dark:bg-neutral-700">
            <FontAwesomeIcon
              className="size-[2.25rem]"
              icon={faLightbulbExclamationOn}
            />
          </div>
          <h2 className="font-medium mb-8 text-center text-xl md:mb-6">
            Want to see how smart I am?
          </h2>
        </div>
        <div className="flex flex-col items-center justify-center h-full">
          <p className="text-center mb-8 md:mb-6">
            Connect your calendar, and I&apos;ll take care of the rest.
          </p>

          <Checklist
            className="max-w-100 mb-8 space-y-4 w-full md:mb-6"
            items={[
              {
                checked: true,
                icon: <FontAwesomeIcon className="size-full" icon={faCheck} />,
                text: "I'll learn from your past trips to deliver personalized travel recommendations.",
              },
              {
                checked: true,
                icon: <FontAwesomeIcon className="size-full" icon={faCheck} />,
                text: "I'll analyze your upcoming plans to help you prepare and plan future trips more efficiently.",
              },
            ]}
            pt={{
              iconWrapper: { className: "inline-flex mr-2 !size-3.5" },
              li: { className: "block text-center" },
            }}
          />

          <div className="flex flex-col gap-2">
            <Button
              label="Connect calendar"
              className="w-full"
              onClick={handleConnectCalendar}
            />
            <Button
              label="Not now"
              className="w-full"
              text
              onClick={handleClose}
            />
          </div>
        </div>
      </Panel>
    </>
  );
}
