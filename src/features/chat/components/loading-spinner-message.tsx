import LoadingSpinner from "@/common/components/loading-spinner";
import ChatMessage from "@/features/chat/components/chat-message";
import WithMessagesContainer from "@/features/chat/components/hoc/with-messages-container";
import { twMerge } from "tailwind-merge";

type LoadingSpinnerMessageProps = {
  className?: string;
};

export default function LoadingSpinnerMessage({
  className,
}: LoadingSpinnerMessageProps) {
  return (
    <WithMessagesContainer className={twMerge("pb-4 md:pb-8", className)}>
      <ChatMessage className="mt-4" isBotMessage={true}>
        <LoadingSpinner className="mt-2" />
      </ChatMessage>
    </WithMessagesContainer>
  );
}
