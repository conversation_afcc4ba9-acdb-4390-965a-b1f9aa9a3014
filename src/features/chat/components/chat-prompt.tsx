import { useAtomValue, use<PERSON>et<PERSON><PERSON> } from "jotai";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "primereact/button";
import Link from "next/link";
import {
  IncomingMessageTypes,
  PromptProps,
} from "@/features/chat/types/messages";
import {
  rawToolOutputAtom,
  rawToolOutputDebugAtom,
} from "@/features/chat/store/debugging";
import arrayHasElements from "@/common/utils/array-has-elements";
import objectHasEntries from "@/common/utils/object-has-entries";
import ChatMessage from "@/features/chat/components/chat-message";
import Flights from "@/features/chat/components/rich-controls/flights";
import Hotels from "@/features/chat/components/rich-controls/hotels";
import Timestamp from "@/features/chat/components/chat-timestamp";
import IconDebug from "@/common/components/icons/debug-icon";
import HotelRooms from "./rich-controls/hotels/hotel-rooms";
import FlightFares from "@/features/chat/components/rich-controls/flights/flight-fares";
import { SuggestedPreferences } from "./rich-controls/suggested-preferences";
import SampleTrips from "./rich-controls/sample-trips/sample-trips";
import { useWebSocketContext } from "../context/websocket-context";

export default function ChatPrompt({
  accommodations,
  avatar,
  eventAttended,
  expireTimestamp,
  fares,
  flights,
  forwardRef,
  isBotMessage,
  isEnabled,
  isLastSearch,
  location_lat_long,
  newTripId,
  lastHumanMessage,
  partial,
  rawToolOutput,
  roomTypes,
  text,
  textColor,
  timestamp,
  type,
  currentOttoUser,
  isStopped,
  messageId,
  suggested_preferences,
  sampleTrips,
}: PromptProps) {
  const setRawToolOutput = useSetAtom(rawToolOutputAtom);
  const rawToolOutputDebug = useAtomValue(rawToolOutputDebugAtom);
  const router = useRouter();
  const { sendMessageToTrip } = useWebSocketContext();

  useEffect(() => {
    if (type === IncomingMessageTypes.REDIRECT_TO_TRIP && newTripId) {
      const timer = setTimeout(() => {
        sendMessageToTrip(newTripId, lastHumanMessage ?? "");
      }, 3000);

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [newTripId, router]);

  if (!!timestamp) {
    return <Timestamp timestamp={timestamp} />;
  }

  const { latitude, longitude, radius } = location_lat_long ?? {};

  return (
    <>
      <ChatMessage
        avatar={avatar}
        isBotMessage={isBotMessage}
        partial={partial}
        text={text}
        textColor={textColor}
        type={type}
        currentOttoUser={currentOttoUser}
        isStopped={isStopped}
        messageId={messageId || (text as any)?.id}
        wrapperRef={forwardRef}
      >
        {arrayHasElements(suggested_preferences?.options) && (
          <SuggestedPreferences suggested_preferences={suggested_preferences} />
        )}

        {arrayHasElements(flights) && (
          <Flights
            disabled={!isEnabled}
            items={flights}
            timeLimit={expireTimestamp as string}
            isLastSearch={isLastSearch}
          />
        )}

        {objectHasEntries(fares) && (
          <FlightFares {...fares} isDisabled={!isEnabled} />
        )}

        {arrayHasElements(accommodations) && (
          <Hotels
            disabled={!isEnabled}
            eventAttended={eventAttended}
            items={accommodations}
            radius={radius}
            isLastSearch={isLastSearch}
            rawToolOutput={rawToolOutput}
            searchLocation={
              { lat: latitude, lng: longitude } as google.maps.LatLngLiteral
            }
            timeLimit={expireTimestamp as string}
          />
        )}

        {arrayHasElements(roomTypes) && (
          <HotelRooms
            items={roomTypes}
            disabled={!isEnabled}
            timeLimit={expireTimestamp}
          />
        )}

        {rawToolOutput && rawToolOutputDebug && (
          <Button
            className="absolute border border-neutral-100 box-border hidden -left-4 p-2 rounded-full -translate-x-full top-0 hover:bg-neutral-100 xl:block"
            onClick={() => setRawToolOutput(rawToolOutput)}
            plain
          >
            <IconDebug />
          </Button>
        )}

        {type === IncomingMessageTypes.FORK_NEW_TRIP && newTripId && (
          <div className="mt-2">
            <Link
              href={`/trips/${newTripId}`}
              className="text-blue-500 dark:text-blue-400 hover:underline text-sm"
            >
              Click here to switch to the new thread.
            </Link>
          </div>
        )}
      </ChatMessage>

      {arrayHasElements(sampleTrips?.trips) && (
        <SampleTrips samples={sampleTrips} />
      )}
    </>
  );
}
