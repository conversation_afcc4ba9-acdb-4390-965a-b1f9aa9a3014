"use client";

import React, { ReactNode } from "react";
import { useAtom, useAtomValue } from "jotai";
import IconClose from "@/common/components/icons/close";
import { But<PERSON> } from "primereact/button";
import {
  hideHeader<PERSON>tom,
  suggestedCapabilityAtom,
} from "@/features/chat/store/chat";
import clsx from "clsx";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faHotel, faPlane } from "@fortawesome/pro-regular-svg-icons";
import { CapabilityPromptsTypes } from "../types/capability-prompts";
import Link from "next/link";
import { ROUTES } from "@/common/constants";
import Markdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkBreaks from "remark-breaks";
import { useCreateTrip } from "@/common/hooks/api";
import { useSetAtom } from "jotai";
import { openingMessageAtom } from "@/features/chat/store/chat";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";

export default function SuggestedCapabilityBanner() {
  const [suggestedCapability, setSuggestedCapability] = useAtom(
    suggestedCapabilityAtom
  );
  const hideHeader = useAtomValue(hideHeaderAtom);
  const { createTrip } = useCreateTrip();
  const setOpeningMessage = useSetAtom(openingMessageAtom);
  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);

  const { capability_type, text } = suggestedCapability ?? {};

  if (!suggestedCapability) {
    return null;
  }

  let icon: ReactNode;

  switch (capability_type) {
    case CapabilityPromptsTypes.FLIGHT_BOOKING:
    case CapabilityPromptsTypes.FLIGHT_CHANGE:
    case CapabilityPromptsTypes.FLIGHT_CANCELLATION:
      icon = (
        <FontAwesomeIcon
          className="max-md:hidden -rotate-30 size-4"
          icon={faPlane}
        />
      );
      break;
    case CapabilityPromptsTypes.HOTEL_BOOKING:
    case CapabilityPromptsTypes.HOTEL_CANCELLATION:
      icon = (
        <FontAwesomeIcon className="max-md:hidden size-4" icon={faHotel} />
      );
      break;
    default:
      break;
  }

  const isTravelPolicy =
    capability_type === CapabilityPromptsTypes.COMPANY_TRAVEL_POLICY;
  const isLoyaltyPrograms =
    capability_type === CapabilityPromptsTypes.LOYALTY_PROGRAMS;
  const isFlightBooking = capability_type === CapabilityPromptsTypes.FLIGHT_BOOKING;
  const isHotelBooking = capability_type === CapabilityPromptsTypes.HOTEL_BOOKING;
  const isFlightChangeOrCancellation =
    capability_type === CapabilityPromptsTypes.FLIGHT_CHANGE;
  const isFlightCancellation = capability_type === CapabilityPromptsTypes.FLIGHT_CANCELLATION;
  const isHotelCancellation =
    capability_type === CapabilityPromptsTypes.HOTEL_CANCELLATION;

  const isEntryRequirment = capability_type === CapabilityPromptsTypes.ENTRY_REQUIREMENTS;

  const handleFlightBookingClick = () => {
    setOpeningMessage("I see you want to try out a flight search. Just give me a destination and dates of arrival/departure");
    createTrip();
    setSuggestedCapability(null);
  };

  const handleHotelBookingClick = () => {
    setOpeningMessage("I see you want to try out a hotel search. Just give me a destination and dates of arrival/departure");
    createTrip();
    setSuggestedCapability(null);
  };

  const handleFlightChangeClick = () => {
    send({
      text: "I need to change this flight.",
      type: OutgoingMessageTypes.PROMPT,
    });
    setSuggestedCapability(null);
  };

  const handleHotelCancelClick = () => {
    send({
      text: "I need help with hotel cancellation",
      type: OutgoingMessageTypes.PROMPT,
    });
    setSuggestedCapability(null);
  };

  const handleFlightCancelClick = () => {
    send({
      text: "I need help with flight cancellation",
      type: OutgoingMessageTypes.PROMPT,
    });
    setSuggestedCapability(null);
  };

  const handleEntryRequirmentClick = () => {
    send({
      text: "What are the entry requirements for this trip?",
      type: OutgoingMessageTypes.PROMPT,
    });
    setSuggestedCapability(null);
  };

  return (
    <div
      className={clsx(
        "bg-blue-100 border-y border-blue-400 flex gap-2 sticky items-center justify-between py-2 px-4 shadow-sm w-full z-[20]",
        "dark:bg-blue-900/20 dark:border-blue-800",
        hideHeader ? "top-0" : "top-15"
      )}
    >
      <div className="flex-1">
        <div className="gap-2 items-center lg:flex">
          {icon}
          <Markdown
            className="max-lg:inline text-sm dark:text-white [&>p]:max-lg:inline"
            remarkPlugins={[remarkGfm, remarkBreaks]}
          >
            {text}
          </Markdown>
          {isTravelPolicy && (
            <Link
              className="shrink-0 w-max lg:ml-3 max-lg:ml-1 "
              href={ROUTES.TRAVEL_POLICY}
            >
              <Button
                className={clsx(
                  "max-lg:bg-transparent max-lg:border-0 max-lg:h-max max-lg:inline max-lg:p-0 max-lg:text-blue-500 max-lg:text-sm",
                  "hover:max-lg:underline hover:max-lg:bg-transparent"
                )}
                outlined
              >
                Add it now
              </Button>
            </Link>
          )}
          {isLoyaltyPrograms && (
            <Link
              className="shrink-0 w-max lg:ml-3 max-lg:ml-1 "
              href={ROUTES.LOYALTY_PROGRAMS}
            >
              <Button
                className={clsx(
                  "max-lg:bg-transparent max-lg:border-0 max-lg:h-max max-lg:inline max-lg:p-0 max-lg:text-blue-500 max-lg:text-sm",
                  "hover:max-lg:underline hover:max-lg:bg-transparent"
                )}
                outlined
              >
                Add it now
              </Button>
            </Link>
          )}
          {isFlightBooking && (
            <Button
              className={clsx(
                "shrink-0 w-max lg:ml-3 max-lg:ml-1",
                "max-lg:bg-transparent max-lg:border-0 max-lg:h-max max-lg:inline max-lg:p-0 max-lg:text-blue-500 max-lg:text-sm",
                "hover:max-lg:underline hover:max-lg:bg-transparent"
              )}
              outlined
              onClick={handleFlightBookingClick}
            >
              Try it now
            </Button>
          )}
          {isHotelBooking && (
            <Button
              className={clsx(
                "shrink-0 w-max lg:ml-3 max-lg:ml-1",
                "max-lg:bg-transparent max-lg:border-0 max-lg:h-max max-lg:inline max-lg:p-0 max-lg:text-blue-500 max-lg:text-sm",
                "hover:max-lg:underline hover:max-lg:bg-transparent"
              )}
              outlined
              onClick={handleHotelBookingClick}
            >
              Try it now
            </Button>
          )}
          {isFlightChangeOrCancellation && (
            <Button
              className={clsx(
                "shrink-0 w-max lg:ml-3 max-lg:ml-1",
                "max-lg:bg-transparent max-lg:border-0 max-lg:h-max max-lg:inline max-lg:p-0 max-lg:text-blue-500 max-lg:text-sm",
                "hover:max-lg:underline hover:max-lg:bg-transparent"
              )}
              outlined
              onClick={handleFlightChangeClick}
            >
              Try it now
            </Button>
          )}
          {isFlightCancellation && (
            <Button
              className={clsx(
                "shrink-0 w-max lg:ml-3 max-lg:ml-1",
                "max-lg:bg-transparent max-lg:border-0 max-lg:h-max max-lg:inline max-lg:p-0 max-lg:text-blue-500 max-lg:text-sm",
                "hover:max-lg:underline hover:max-lg:bg-transparent"
              )}
              outlined
              onClick={handleFlightCancelClick}
            >
              Try it now
            </Button>
          )}
          {isEntryRequirment && (
            <Button
              className={clsx(
                "shrink-0 w-max lg:ml-3 max-lg:ml-1",
                "max-lg:bg-transparent max-lg:border-0 max-lg:h-max max-lg:inline max-lg:p-0 max-lg:text-blue-500 max-lg:text-sm",
                "hover:max-lg:underline hover:max-lg:bg-transparent"
              )}
              outlined
              onClick={handleEntryRequirmentClick}
            >
              Try it now
            </Button>
          )}
          {isHotelCancellation && (
            <Button
              className={clsx(
                "shrink-0 w-max lg:ml-3 max-lg:ml-1",
                "max-lg:bg-transparent max-lg:border-0 max-lg:h-max max-lg:inline max-lg:p-0 max-lg:text-blue-500 max-lg:text-sm",
                "hover:max-lg:underline hover:max-lg:bg-transparent"
              )}
              outlined
              onClick={handleHotelCancelClick}
            >
              Try it now
            </Button>
          )}
        </div>
      </div>

      <Button
        onClick={() => setSuggestedCapability(null)}
        className="p-1 text-neutral-350 dark:text-neutral-600"
        aria-label="Close banner"
        rounded
        text
      >
        <IconClose width={14} height={14} />
      </Button>
    </div>
  );
}
