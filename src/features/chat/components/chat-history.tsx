import { useEffect, useRef } from "react";
import { use<PERSON>tomValue, useSet<PERSON><PERSON> } from "jotai";
import {
  IncomingMessage,
  IncomingMessageTypes,
  OutgoingMessageTypes,
} from "@/features/chat/types/messages";
import { renderedMessages } from "@/features/chat/constants/messages";
import { useUserProfile } from "@/features/user/hooks/api";
import { chatHistoryAtom, ottoReplyingAtom } from "@/features/chat/store/chat";
import { chatHistoryRefAtom } from "@/features/chat/store/components";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import arrayHasElements from "@/common/utils/array-has-elements";
import ChatPrompt from "@/features/chat/components/chat-prompt";
import WithMessagesContainer from "@/features/chat/components/hoc/with-messages-container";
import Skeleton from "@/features/chat/components/skeletons/skeleton";
import BatchDeleteMessages from "@/features/chat/components/batch-delete-messages";
import { Avatar } from "primereact/avatar";
import { UserResponse } from "@/features/admin/types/api";
import OttoAvatar from "./otto-avatar";
import { UserRole } from "@/features/user/types/user";
import { agentsDebugAtom } from "@/features/chat/store/debugging";
import TopOfThreadBanner from "./skeletons/top-of-thread-banner";
import LoadingSpinnerMessage from "./loading-spinner-message";
import useScrollBehaviour from "../hooks/use-scroll-behaviour";

type ChatHistoryProps = {
  user?: UserResponse;
};

export default function ChatHistory({ user: userProp }: ChatHistoryProps) {
  const { profile } = useUserProfile();
  const foreignUser = useAtomValue(foreignUserAtom);
  const isOttoReplying = useAtomValue(ottoReplyingAtom);
  const history = useAtomValue(chatHistoryAtom);
  const agentsDebug = useAtomValue(agentsDebugAtom);

  const lastMessage = history.at(-1) as IncomingMessage;
  const chatHistoryRef = useRef<HTMLDivElement>(null);
  const setChatHistoryRef = useSetAtom(chatHistoryRefAtom);
  const lastMessageRef = useScrollBehaviour();

  useEffect(() => setChatHistoryRef(chatHistoryRef), [setChatHistoryRef]);

  if (!arrayHasElements(history)) {
    return null;
  }

  const isPartialMessage = lastMessage?.partial;

  const impersonatedUser = userProp ?? foreignUser;
  // Server side user_name is already resolve between preferred name, first name.
  const userNameFirstLetter = !!impersonatedUser
    ? impersonatedUser?.preferred_name?.[0] ||
      impersonatedUser?.first_name?.[0] ||
      impersonatedUser?.email?.[0]
    : profile?.user_name[0];
  let userProfilePicture = !!impersonatedUser
    ? impersonatedUser?.profile_picture
    : profile?.profile_picture;
  if (
    !!userNameFirstLetter &&
    userProfilePicture?.includes("static/default-avatar-icon.jpg")
  ) {
    // let's not show the default avatar if we have some form of name
    userProfilePicture = undefined;
  }
  const avatar = (
    <Avatar
      image={userProfilePicture}
      label={userNameFirstLetter}
      shape="circle"
      size="normal"
    />
  );
  const currentUser = {
    firstName: !!impersonatedUser
      ? impersonatedUser.first_name
      : profile?.first_name,
    lastName: !!impersonatedUser
      ? impersonatedUser.last_name
      : profile?.last_name,
  };

  return (
    <>
      <WithMessagesContainer
        className="py-4 max-lg:pt-18 md:pt-6 md:pb-8 [--min-height:calc(100%-88px)] !min-h-[var(--min-height)]"
        forwardRef={chatHistoryRef}
      >
        <TopOfThreadBanner />
        {history.map((message) => {
          const prompts =
            message?.type === IncomingMessageTypes.HISTORY
              ? message.messages
              : [message];

          if (!arrayHasElements(prompts)) {
            return null;
          }

          const filteredPrompts = (prompts as IncomingMessage[]).filter(
            (prompt) =>
              renderedMessages.includes(prompt.type) ||
              (!!foreignUser &&
                String(prompt.type) === OutgoingMessageTypes.SILENT_PROMPT) ||
              (profile?.role === UserRole.ADMIN &&
                agentsDebug &&
                String(prompt.type) === OutgoingMessageTypes.SILENT_PROMPT)
          );

          return filteredPrompts.map((prompt, index) => (
            <ChatPrompt
              {...prompt}
              avatar={
                !prompt.isBotMessage ? (
                  avatar
                ) : (
                  <OttoAvatar className="max-xl:hidden shrink-0 size-6" />
                )
              }
              currentOttoUser={currentUser}
              forwardRef={lastMessageRef}
              key={index}
            />
          ));
        })}
        {isOttoReplying && !isPartialMessage && (
          <LoadingSpinnerMessage className="!p-0 mt-4" />
        )}
        <Skeleton />
      </WithMessagesContainer>
      <BatchDeleteMessages />
    </>
  );
}
