import { ArrowUpIcon } from "@/common/components/icons/arrow-up";
import MicrophoneIcon from "@/common/components/icons/microphone";
import IconStopSolid from "@/common/components/icons/stop-solid";
import { useOnClickOutside } from "@/common/hooks/use-on-click-outside";
import { isNativeBridge<PERSON>tom } from "@/common/store/nativebridge";
import {
  messageRendering<PERSON><PERSON>,
  ottoReplying<PERSON>tom,
  showSkeleton<PERSON>tom,
  stopDisabledAtom,
} from "@/features/chat/store/chat";
import {
  IncomingMessageTypes,
  OutgoingMessageTypes,
} from "@/features/chat/types/messages";
import clsx from "clsx";
import { useAtom, useAtomValue } from "jotai";
import { Button } from "primereact/button";
import { Editor, EditorTextChangeEvent } from "primereact/editor";
import { DomHandler } from "primereact/utils";
import { Delta, Range } from "quill";
import { KeyboardEventHandler, useLayoutEffect, useRef, useState } from "react";
import TurndownService from "turndown";
import { MAX_RECORDING_SECONDS } from "../../constants/transcriptions";
import useAudioRecording from "../../hooks/use-audio-recording";
import { WebSocketSender } from "../../types/web-socket";
import useThrottle from "@/common/hooks/use-throttle";

const turndownService = new TurndownService();

export default function ChatInput({ send }: { send: WebSocketSender }) {
  const isTouchDevice = DomHandler.isTouchDevice();
  const defaultEditorHeight = isTouchDevice ? 72 : 64;
  const editorModules = {
    keyboard: {
      bindings: {
        enter: {
          key: ["Enter"],
          // Override default: Return alone shouldn't change the text on web
          handler: function (range: Range) {
            if (isTouchDevice) {
              // @ts-ignore
              this.quill.insertText(range.index, "\n");
            }
          },
        },
        // Add keyboard bindings for Option+Return (Alt+Enter)
        custom: {
          key: ["Enter"],
          altKey: true,
          handler: function (range: Range) {
            // @ts-ignore
            this.quill.insertText(range.index, "\n");
          },
        },
      },
    },
  };

  const isOttoReplying = useAtomValue(ottoReplyingAtom);
  const isMessageRendering = useAtomValue(messageRenderingAtom);
  const skeletonType = useAtomValue(showSkeletonAtom);
  const [stopDisabled, setStopDisabled] = useAtom(stopDisabledAtom);
  const isSkeletonRendered =
    skeletonType == IncomingMessageTypes.FARES_SKELETON ||
    skeletonType == IncomingMessageTypes.FLIGHTS_SKELETON ||
    skeletonType == IncomingMessageTypes.HOTELS_SKELETON;

  const isNativeBridge = useAtomValue(isNativeBridgeAtom);

  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [htmlContent, setHtmlContent] = useState("");
  const [editorHeight, setEditorHeight] = useState(defaultEditorHeight);

  const editorContainerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<Editor>(null);

  const onNotAllowedOrFound = () => {
    if (!isNativeBridge) {
      showError("Microphone not available");
    }
  };

  const {
    isRecording,
    isLoading,
    elapsedSeconds,
    isMostlySilent,
    startRecording,
    stopRecording,
    MicrophonePopup,
  } = useAudioRecording({
    onNotAllowedOrFound,
    onTranscriptionError: () => {
      if (isMostlySilent) {
        showError("Did you say anything?");
      } else if (elapsedSeconds < 1) {
        showError("Recording too short.");
      } else {
        showError("Something went wrong. Please try again.");
      }
    },
  });

  const isDisabled =
    isOttoReplying || isMessageRendering || isSkeletonRendered || isLoading;
  const shouldDisableInput = isDisabled || isRecording;

  const handleOnTouchOutside = () => {
    editorRef.current?.getQuill()?.blur();
  };

  useOnClickOutside(editorContainerRef, handleOnTouchOutside, true);

  const showError = (message: string) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(null), 3000);
  };

  const autoResize = () => {
    const quill = editorRef.current?.getQuill();
    if (quill?.root) {
      const contentHeight = quill.root.scrollHeight;
      const newHeight = Math.min(
        contentHeight + (isTouchDevice ? 48 : 40),
        isTouchDevice ? 120 : 160
      );
      setEditorHeight(newHeight);
    }
  };

  const handleEditorChange = (e: EditorTextChangeEvent) => {
    if (!e.textValue.trim()) {
      // Workaround to clear the editor
      editorRef.current?.getQuill()?.setText("");
      setHtmlContent("");
    } else {
      setHtmlContent(e.htmlValue || "");
    }
    setTimeout(autoResize);
  };

  const handleKeyDown: KeyboardEventHandler<HTMLDivElement> = (e) => {
    if (e.key === "Enter" && !e.shiftKey && !e.altKey && !isTouchDevice) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleOnRecordClick = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const handleSubmit = () => {
    const markdownContent = turndownService.turndown(htmlContent);

    if (markdownContent && !shouldDisableInput) {
      send({
        text: markdownContent,
        type: OutgoingMessageTypes.PROMPT,
      });
    }
    setEditorHeight(defaultEditorHeight);
    editorRef.current?.getQuill()?.setText("");
  };

  const handleStop = useThrottle(() => {
    setStopDisabled(true);
    send({
      type: OutgoingMessageTypes.STOP,
    });
    editorRef.current?.getQuill()?.setText("");
  });

  useLayoutEffect(() => {
    const quill = editorRef.current?.getQuill();
    if (quill) {
      quill.clipboard.addMatcher(Node.ELEMENT_NODE, (_: Node, delta: Delta) => {
        delta.forEach((e) => {
          if (e.attributes) {
            delete e.attributes.color;
            delete e.attributes.background;
          }
        });
        return delta;
      });
    }
    quill?.enable(!shouldDisableInput);

    if (!isNativeBridge && !shouldDisableInput) {
      quill?.focus();
    }
  }, [isNativeBridge, shouldDisableInput]);

  return (
    <div
      ref={editorContainerRef}
      className={clsx("flex items-center justify-center gap-4", {
        "mb-6 px-4 lg:px-8": !isTouchDevice,
      })}
    >
      <div
        style={{ height: editorHeight }}
        className={clsx(
          "relative flex items-center gap-4 w-full transition-[height] duration-150",
          "ease-out bg-white min-h-16 max-w-218 shadow-lg ",
          {
            "[clip-path:inset(-16px_-16px_0_0)] rounded-t-2xl p-4":
              isTouchDevice,
            "rounded-2xl p-3 pl-6 outline outline-2 outline-gray-300 -outline-offset-2 dark:outline-gray-900":
              !isTouchDevice,
          },
          "dark:bg-gray-800"
        )}
      >
        <Editor
          ref={editorRef}
          value={htmlContent}
          placeholder="Respond to Otto..."
          className="w-full peer"
          pt={{
            content: {
              className: clsx({
                "max-h-18": isTouchDevice,
                "max-h-30": !isTouchDevice,
              }),
            },
          }}
          modules={editorModules}
          onTextChange={handleEditorChange}
          onKeyDown={handleKeyDown}
          showHeader={false}
          disabled={isDisabled || isRecording}
          data-testid="chat-input"
        />
        {!isDisabled && htmlContent && (
          <Button
            rounded
            className="size-10 mt-auto bg-primary-300 peer-focus-within:bg-primary-500 /
            text-primary-500 peer-focus-within:text-white"
            onClick={handleSubmit}
            data-testid="chat-input-send-button"
          >
            <ArrowUpIcon />
          </Button>
        )}
        {!isDisabled && !htmlContent && (
          <Button
            rounded
            className="size-10 bg-primary-500 mt-auto dark:bg-neutral-400"
            onClick={handleOnRecordClick}
            data-testid="chat-input-record-button"
          >
            {isRecording ? (
              MAX_RECORDING_SECONDS - elapsedSeconds
            ) : (
              <MicrophoneIcon />
            )}
          </Button>
        )}
        {isDisabled && (
          <Button
            rounded
            className="bg-primary-500 size-10 dark:bg-neutral-400"
            onClick={stopDisabled ? undefined : handleStop}
            data-testid="chat-input-stop-button"
          >
            <IconStopSolid className="size-10 shrink-0 cursor-pointer" />
          </Button>
        )}
      </div>

      {errorMessage && (
        <small className="absolute text-red-600 text-center bottom-1 w-full">
          {errorMessage}
        </small>
      )}

      {MicrophonePopup}
    </div>
  );
}
