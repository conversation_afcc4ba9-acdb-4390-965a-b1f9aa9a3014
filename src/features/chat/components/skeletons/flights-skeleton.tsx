import clsx from "clsx";
import FlightCardSkeleton from "./flight-card-skeleton";
import { twMerge } from "tailwind-merge";

type FlightsSkeletonProps = {
  skeletonClassName?: string;
  text?: string | string[] | null;
  textClassName?: string;
};

export default function FlightsSkeleton({
  skeletonClassName,
  text,
  textClassName,
}: FlightsSkeletonProps) {
  return (
    <>
      {!!text && (
        <div className={clsx("italic text-neutral-500", textClassName)}>
          {text}
        </div>
      )}
      <div
        className={twMerge(
          "grid gap-y-4 mt-4 w-full xs:gap-x-2 xs:grid-cols-2 sm:!grid-cols-3 sm:max-w-full md:gap-x-4 lg:items-stretch lg:mt-8",
          skeletonClassName
        )}
      >
        {Array.from(Array(3)).map((x, index) => (
          <FlightCardSkeleton
            className={clsx({
              "hidden xs:block": index === 1,
              "hidden sm:block": index > 1,
            })}
            key={index}
          />
        ))}
      </div>
    </>
  );
}
