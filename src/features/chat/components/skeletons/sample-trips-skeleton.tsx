import clsx from "clsx";
import React from "react";
import SampleTripCardSkeleton from "./sample-trip-card-skeleton";
import { twMerge } from "tailwind-merge";

type SampleTripsSkeletonProps = {
  skeletonClassName?: string;
  text?: string | string[] | null;
  textClassName?: string;
};

export default function SampleTripsSkeleton({
  skeletonClassName,
  text,
  textClassName,
}: SampleTripsSkeletonProps) {
  return (
    <>
      {!!text && (
        <div className={clsx("italic text-neutral-500", textClassName)}>
          {text}
        </div>
      )}
      <div
        className={twMerge(
          "grid sm:grid-cols-2 lg:grid-cols-4 max-w-72 mx-auto gap-y-4 mt-4 w-full sm:gap-x-2 sm:max-w-full md:gap-x-4 lg:items-stretch lg:mt-8",
          skeletonClassName
        )}
      >
        {Array.from(Array(4)).map((_, index) => (
          <SampleTripCardSkeleton key={index} />
        ))}
      </div>
    </>
  );
}
