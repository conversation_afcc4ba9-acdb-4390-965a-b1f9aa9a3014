import clsx from "clsx";
import { Card } from "primereact/card";

type FlightCardSkeletonProps = {
  className?: string;
};

export default function FlightCardSkeleton({
  className,
}: FlightCardSkeletonProps) {
  return (
    <Card className={clsx("flex-auto", className)}>
      <div className="bg-skeleton bg-neutral-150 rounded-full h-7 w-28"></div>
      <div className="flex gap-x-3 items-center mt-4">
        <div className="bg-skeleton rounded-full size-6"></div>
        <div className="bg-skeleton h-4 w-30"></div>
      </div>
      <div className="bg-skeleton h-3 mt-5 w-36"></div>
      <div className="bg-skeleton h-3 mt-2 w-40"></div>
      <div className="bg-skeleton h-3 mt-2 w-32"></div>

      <div className="bg-skeleton h-3 mt-6 w-30"></div>
      <div className="bg-skeleton h-3 mt-6 w-40"></div>

      <div className="bg-skeleton h-9 mt-4.5 rounded-lg w-24"></div>
    </Card>
  );
}
