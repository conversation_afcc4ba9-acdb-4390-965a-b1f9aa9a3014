import clsx from "clsx";
import { Card } from "primereact/card";

type HotelRoomSkeletonProps = {
  className?: string;
};

export default function HotelRoomSkeleton({
  className,
}: HotelRoomSkeletonProps) {
  return (
    <Card className={clsx("flex-auto relative sm:basis-1/3", className)}>
      <div className="bg-skeleton h-7 rounded-full w-28" />
      <div className="aspect-[9/3] md:aspect-[9/5] bg-skeleton mt-4 rounded-lg w-full" />
      <div className="bg-skeleton h-3.5 mt-[1.1875rem] w-4/5" />
      <div className="bg-skeleton h-3.5 mt-[0.8125rem] w-3/5" />
      <div className="bg-skeleton border border-neutral-150 h-9 mt-5 rounded-lg w-2/5 dark:border-gray-600" />
    </Card>
  );
}
