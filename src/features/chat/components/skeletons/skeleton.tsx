import { ReactNode } from "react";
import { useAtomValue } from "jotai";
import { IncomingMessageTypes } from "@/features/chat/types/messages";
import { showSkeletonAtom, skeletonTextAtom } from "@/features/chat/store/chat";
import ChatMessage from "@/features/chat/components/chat-message";
import FlightsSkeleton from "./flights-skeleton";
import HotelRoomsSkeleton from "./hotel-rooms-skeleton";
import FaresSkeleton from "./fares-skeleton";
import SampleTripsSkeleton from "./sample-trips-skeleton";

export default function Skeleton() {
  const showSkeleton = useAtomValue(showSkeletonAtom);
  const skeletonText = useAtomValue(skeletonTextAtom);

  if (!showSkeleton) {
    return null;
  }

  let skeleton: ReactNode;

  switch (showSkeleton) {
    case IncomingMessageTypes.FLIGHTS_SKELETON:
    case IncomingMessageTypes.FLIGHTS_SKELETON_ASYNC:
      skeleton = (
        <FlightsSkeleton
          skeletonClassName="xl:pl-8"
          textClassName="xl:pl-8"
          text={skeletonText}
        />
      );
      break;
    case IncomingMessageTypes.HOTELS_SKELETON_ASYNC:
    case IncomingMessageTypes.HOTELS_SKELETON:
      skeleton = (
        <HotelRoomsSkeleton
          skeletonClassName="xl:pl-8"
          text={skeletonText}
          textClassName="xl:pl-8"
        />
      );
      break;
    case IncomingMessageTypes.FARES_SKELETON:
      skeleton = <FaresSkeleton />;
      break;
  }

  return <ChatMessage isBotMessage={true}>{skeleton}</ChatMessage>;
}
