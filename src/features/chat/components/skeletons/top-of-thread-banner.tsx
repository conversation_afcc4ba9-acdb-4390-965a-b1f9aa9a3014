import { IncomingMessageTypes } from "../../types/messages";
import SampleTripsSkeleton from "./sample-trips-skeleton";
import { useAtomValue } from "jotai";
import { showSkeletonAtom, skeletonTextAtom } from "../../store/chat";
import ChatMessage from "../chat-message";

export default function TopOfThreadBanner() {
  const showSkeleton = useAtomValue(showSkeletonAtom);
  const skeletonText = useAtomValue(skeletonTextAtom);

  if (showSkeleton !== IncomingMessageTypes.SAMPLE_TRIPS_SKELETON) {
    return null;
  }

  return (
    <ChatMessage isBotMessage={true}>
      <SampleTripsSkeleton
        skeletonClassName="xl:pl-8"
        text={skeletonText}
        textClassName="xl:pl-8"
      />
    </ChatMessage>
  );
}
