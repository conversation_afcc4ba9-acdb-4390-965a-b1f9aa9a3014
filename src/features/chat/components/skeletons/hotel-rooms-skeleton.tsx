import clsx from "clsx";
import HotelRoomSkeleton from "./hotel-room-skeleton";
import { twMerge } from "tailwind-merge";

type HotelRoomsSkeletonProps = {
  skeletonClassName?: string;
  text?: string | string[] | null;
  textClassName?: string;
};

export default function HotelRoomsSkeleton({
  skeletonClassName,
  text,
  textClassName,
}: HotelRoomsSkeletonProps) {
  return (
    <>
      {!!text && (
        <div className={twMerge("italic text-neutral-500", textClassName)}>
          {text}
        </div>
      )}
      <div
        className={twMerge(
          "flex flex-col gap-y-4 mt-4 mx-auto lg:mt-8 lg:flex-row lg:gap-x-4 lg:items-stretch",
          skeletonClassName
        )}
      >
        <div
          className="grid gap-4 w-full sm:grid-cols-2 sm:gap-x-4 md:gap-x-4 lg:basis-2/3 /
        lg:shrink-0"
        >
          {Array.from(Array(4)).map((x, index) => (
            <HotelRoomSkeleton
              className={clsx({ "hidden sm:block": index > 0 })}
              key={index}
            />
          ))}
        </div>
        <div
          className="bg-skeleton flex-auto h-96 /
        hidden rounded-lg w-full md:block lg:h-auto"
        />
      </div>
    </>
  );
}
