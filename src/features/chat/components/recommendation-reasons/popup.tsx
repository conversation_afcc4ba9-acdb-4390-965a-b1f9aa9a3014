import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useRef } from "react";
import clsx from "clsx";
import { Panel } from "primereact/panel";
import Checklist from "@/common/components/checklist";
import BackDrop from "@/common/components/backdrop";
import IconCheck from "@/common/components/icons/check";
import { createPortal } from "react-dom";

type RecommendationPanelProps = {
  className?: string;
  items: string[];
  onClose: MouseEventHandler<HTMLDivElement>;
  showCheckmark?: boolean;
  triggerRect: DOMRect;
};

export default function RecommendationPopup({
  className,
  items,
  onClose,
  showCheckmark = true,
  triggerRect,
}: RecommendationPanelProps) {
  const panelRef = useRef<Panel>(null);

  useEffect(() => {
    const panelElement = panelRef?.current?.getElement();

    if (!!panelElement) {
      const panelRect = panelElement.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Calculate initial position
      let left = triggerRect.left;
      let top = triggerRect.bottom;

      // Check if panel would overflow right edge
      if (left + panelRect.width > viewportWidth) {
        left = viewportWidth - panelRect.width - 16; // 16px padding
      }

      // Check if panel would overflow bottom edge
      if (top + panelRect.height > viewportHeight) {
        top = triggerRect.top - panelRect.height;
      }

      // Apply the position
      panelElement.style.left = `${left}px`;
      panelElement.style.top = `${top}px`;
    }
  }, [triggerRect]);

  const content = (
    <>
      <Panel
        ref={panelRef}
        className={clsx(
          "max-w-120 w-[80vw] sm:w-120 z-30 cursor-default fixed",
          className
        )}
        header="Reasoning"
        onClick={(e) => e.stopPropagation()}
      >
        <Checklist
          pt={{
            icon: <IconCheck className="text-primary-500" />,
            iconWrapper: { className: clsx({ hidden: !showCheckmark }) },
          }}
          items={items.map((item) => ({
            checked: true,
            text: item,
          }))}
        />
      </Panel>
      <BackDrop onClick={onClose} className="cursor-default z-20" />
    </>
  );

  return createPortal(content, document.body);
}
