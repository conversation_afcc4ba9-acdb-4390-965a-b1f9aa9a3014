import Checklist from "@/common/components/checklist";
import IconCheck from "@/common/components/icons/check";
import { Chip } from "primereact/chip";

export default function RecommendationChecklist({
  reasons,
}: {
  reasons: string[];
}) {
  return (
    <div className="flex flex-col gap-y-4">
      <Chip label="Why I recommend it" />
      <Checklist
        className="space-y-4"
        items={reasons.map((item) => ({
          checked: true,
          text: item,
        }))}
        pt={{
          icon: <IconCheck className="text-primary-500" />,
        }}
      />
    </div>
  );
}
