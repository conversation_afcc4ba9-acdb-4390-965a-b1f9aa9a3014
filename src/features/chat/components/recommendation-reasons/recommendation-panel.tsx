import { But<PERSON> } from "primereact/button";
import { ReactNode, useState, useRef } from "react";
import RecommendationPopup from "./popup";
import clsx from "clsx";
import arrayHasElements from "@/common/utils/array-has-elements";

type RecommendationPanelProps = {
  pt?: {
    popup?: {
      className?: string;
    };
    root?: {
      className?: string;
    };
  };
  reasons: string[];
  title?: ReactNode;
  showCheckmark?: boolean;
};

export default function RecommendationPanel({
  pt,
  reasons,
  title = "Why I recommend it",
  showCheckmark = true,
}: RecommendationPanelProps) {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const triggerRef = useRef<HTMLDivElement>(null);

  if (!arrayHasElements(reasons)) {
    return null;
  }

  return (
    <div
      className={clsx("relative w-max overflow-visible", pt?.root?.className)}
    >
      <div ref={triggerRef}>
        <Button
          link
          onClick={(event) => {
            event.stopPropagation();
            setIsOpen(!isOpen);
          }}
          className="p-0"
        >
          {title}
        </Button>
      </div>
      {isOpen && triggerRef.current && (
        <RecommendationPopup
          className={pt?.popup?.className}
          items={reasons}
          onClose={(event) => {
            event.stopPropagation();
            setIsOpen(false);
          }}
          showCheckmark={showCheckmark}
          triggerRect={triggerRef.current.getBoundingClientRect()}
        />
      )}
    </div>
  );
}
