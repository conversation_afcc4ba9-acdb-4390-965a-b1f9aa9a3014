import Image from "next/image";
import { useAtom } from "jotai";
import clsx from "clsx";
import { But<PERSON> } from "primereact/button";
import { Chip } from "primereact/chip";
import ImageSizes from "@/common/types/image-sizes";
import RightSidebars from "@/features/chat/types/right-sidebars";
import { hotelRoomDetailsAtom } from "@/features/chat/store/sidebar";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import arrayHasElements from "@/common/utils/array-has-elements";
import generateSizesString from "@/common/utils/generate-sizes-string";
import WithSidebar from "@/common/components/sidebar/with-right-sidebar";
import RecommendationChecklist from "@/features/chat/components/recommendation-reasons/recommendation-checklist";
import IconCheck from "@/common/components/icons/check";
import { formatPrice } from "@/common/utils/format-price";
import { useState } from "react";

const imageSizes: ImageSizes = {
  "524px": "435px",
  base: "calc(91.6667vw - 3rem)",
};

export default function HotelRoomDetails() {
  const [hotelRoomDetails, setHotelRoomDetails] = useAtom(hotelRoomDetailsAtom);
  const { closeRightSidebar, rightSidebar } = useRightSidebarSwitch();
  const [showAllAmenities, setShowAllAmenities] = useState(false);

  const {
    isDisabled,
    isExpired,
    isSubmitted,
    onSubmit,
    option_title,
    options,
    price,
    pricePerNight,
    recommendation_reason,
    room_photo,
    amenities,
  } = hotelRoomDetails ?? {};
  const amenitiesItems = Array.isArray(amenities)
    ? amenities
    : amenities?.split(",");
  const displayedAmenities = showAllAmenities
    ? amenitiesItems
    : amenitiesItems?.slice(0, 5);
  const hasMoreAmenities = amenitiesItems && amenitiesItems.length > 8;

  const submitHandler = () => {
    if (typeof onSubmit === "function" && !isDisabled && !isExpired) {
      onSubmit();

      !!hotelRoomDetails &&
        setHotelRoomDetails({
          ...hotelRoomDetails,
          isDisabled: true,
          isSubmitted: true,
        });
    }
  };

  return (
    <WithSidebar
      header="Hotel room details"
      isOpen={rightSidebar === RightSidebars.HOTEL_ROOM_DETAILS}
      onClose={closeRightSidebar}
    >
      <div className="flex flex-col gap-y-6">
        {!!option_title && (
          <p className="font-semibold leading-5.5">{option_title}</p>
        )}

        <div className="flex items-center gap-x-3">
          <Button
            className={clsx({
              "disabled:bg-gray-950 disabled:text-white": isSubmitted,
            })}
            disabled={isDisabled || isExpired}
            onClick={submitHandler}
            outlined={!isSubmitted}
            data-testid="select-button"
          >
            {isSubmitted ? (
              <>
                <IconCheck />
                Selected
              </>
            ) : (
              "Select"
            )}
          </Button>
          <div className="text-sm">
            {!!pricePerNight && (
              <div className="text-neutral-500">
                ${formatPrice(pricePerNight)}
                {` /night`}
              </div>
            )}
            {!!price && (
              <div className="font-medium">
                <span className="font-bold">${formatPrice(price)}</span>
                {` total including taxes & fees`}
              </div>
            )}
          </div>
        </div>

        {!!room_photo && (
          <div className="relative w-full h-[12.5rem] rounded-lg overflow-hidden">
            <Image
              alt="Hotel room image"
              fill
              sizes={generateSizesString(imageSizes)}
              src={room_photo}
              className="object-cover"
            />
          </div>
        )}

        {!!recommendation_reason && (
          <RecommendationChecklist reasons={[recommendation_reason]} />
        )}

        {arrayHasElements(amenities) && (
          <div>
            <p className="font-semibold mb-4">Amenities that matter to you</p>
            <div className="flex flex-wrap gap-2">
              {displayedAmenities?.map((item, index) => (
                <Chip
                  className="bg-gray-100 dark:bg-white/5"
                  key={index}
                  label={item}
                  pt={{
                    label: {
                      className: "font-normal text-base leading-5.5",
                    },
                  }}
                />
              ))}
              {hasMoreAmenities && (
                <Button
                  className="text-base py-0 px-1 rounded-full text-neutral-500 self-center"
                  plain
                  onClick={() => setShowAllAmenities(!showAllAmenities)}
                >
                  {showAllAmenities ? "View less" : "View more"}
                </Button>
              )}
            </div>
          </div>
        )}

        {arrayHasElements(options) &&
          options?.map((option, index) => (
            <p className="text-neutral-500 text-sm" key={index}>
              {option}
            </p>
          ))}
      </div>
    </WithSidebar>
  );
}
