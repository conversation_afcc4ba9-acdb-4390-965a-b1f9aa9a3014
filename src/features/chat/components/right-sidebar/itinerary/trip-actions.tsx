import { useAtomValue } from "jotai";
import { Button } from "primereact/button";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  currentTripAtom,
  tripDetailsAtom,
} from "@/features/chat/store/current-trip";
import { useWebSocketContext } from "@/features/chat/context/websocket-context";
import clsx from "clsx";

export function TripActions({ showActions }: { showActions?: boolean }) {
  const currentTripId = useAtomValue(currentTripAtom);
  const tripDetails = useAtomValue(tripDetailsAtom);

  const trip =
    currentTripId && tripDetails ? tripDetails[currentTripId] : undefined;
  const changeTripAction = trip?.change_trip_action;
  const cancelTripAction = trip?.cancel_trip_action;
  const buttonClassName = clsx(
    "outline -outline-offset-1 outline-1 outline-neutral-250 py-3 px-4 font-semibold dark:font-semibold h-fit flex-1",
    "text-nowrap"
  );

  const { sendMessageToTrip } = useWebSocketContext();

  const handleChangeTrip = () => {
    if (changeTripAction && currentTripId) {
      sendMessageToTrip(currentTripId, changeTripAction);
    }
  };

  const handleCancelTrip = () => {
    if (cancelTripAction && currentTripId) {
      sendMessageToTrip(currentTripId, cancelTripAction);
    }
  };

  return (
    <div className="flex flex-col xs:flex-row gap-3">
      <Button text className={buttonClassName}>
        <FontAwesomeIcon icon={["far", "download"]} />
        View receipt
      </Button>
      {changeTripAction && showActions && (
        <Button text onClick={handleChangeTrip} className={buttonClassName}>
          <FontAwesomeIcon icon={["far", "pen"]} />
          Change trip
        </Button>
      )}
      {cancelTripAction && showActions && (
        <Button
          text
          onClick={handleCancelTrip}
          className={clsx(buttonClassName, "text-red-550 dark:text-red-550")}
        >
          <FontAwesomeIcon icon={["far", "ban"]} />
          Cancel trip
        </Button>
      )}
    </div>
  );
}
