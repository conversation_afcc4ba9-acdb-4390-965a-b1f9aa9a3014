import clsx from "clsx";
import colors from "@/common/constants/colors";
import IconCheckSolid from "@/common/components/icons/check-solid";
import { BookingStatuses } from "@/features/itineraries/types/itinerary";
import IconClose from "@/common/components/icons/close";
import { ReactNode } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faClock, faCircleQuestion } from "@fortawesome/free-regular-svg-icons";

type BookingStatusProps = {
  bookedTitle?: string;
  cancelledTitle?: string;
  status: BookingStatuses;
  unbookedTitle?: string;
};

export default function BookingStatus({
  bookedTitle = "Flight booked",
  cancelledTitle = "Flight cancelled",
  status,
  unbookedTitle = "Flight not booked yet",
}: BookingStatusProps) {
  const isCancelled = status === BookingStatuses.CANCELLED;

  let data: { icon: ReactNode; text: string };

  switch (status) {
    case BookingStatuses.BOOKED:
      data = {
        icon: <IconCheckSolid fill={colors.gray[950]} height={24} width={24} />,
        text: bookedTitle,
      };
      break;
    case BookingStatuses.CANCELLED:
      data = {
        icon: (
          <div className="bg-red-600 flex items-center justify-center h-6 rounded-full w-6">
            <IconClose
              className="block"
              fill={colors.white}
              height={12}
              width={12}
            />
          </div>
        ),
        text: cancelledTitle,
      };
      break;
    case BookingStatuses.UNBOOKED:
      data = {
        icon: <FontAwesomeIcon icon={faClock} />,
        text: unbookedTitle,
      };
      break;
    default:
      data = {
        icon: <FontAwesomeIcon icon={faCircleQuestion} />,
        text: "Status unknown",
      };
  }

  return (
    <div className="flex items-center gap-x-3">
      {data.icon}

      <div>
        <p className={clsx("font-bold", { "text-red-700": isCancelled })}>
          {data.text}
        </p>
      </div>
    </div>
  );
}
