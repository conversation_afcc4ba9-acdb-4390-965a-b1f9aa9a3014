import { use<PERSON><PERSON>, use<PERSON>tomValue, use<PERSON>et<PERSON><PERSON> } from "jotai";
import { Tab<PERSON><PERSON><PERSON>, Tab<PERSON>iew } from "primereact/tabview";
import ItineraryType, {
  Accommodation,
  BookingStatuses,
  ItineraryTab,
} from "@/features/itineraries/types/itinerary";
import RightSidebars from "@/features/chat/types/right-sidebars";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import {
  travelContextAtom,
  travelContextDebugAtom,
} from "@/features/chat/store/debugging";
import {
  currentTripAtom,
  tripDetailsAtom,
} from "@/features/chat/store/current-trip";
import WithSidebar from "@/common/components/sidebar/with-right-sidebar";
import FlightsTab from "./flights-tab";
import HotelsTab from "./hotels-tab";
import {
  activeTabAtom,
  focusedTripEventIdAtom,
} from "@/features/itineraries/store/itinerary";
import useOnNavigation from "@/common/hooks/use-on-navigation";
import { useCallback, useMemo } from "react";
import LoadingSpinner from "@/common/components/loading-spinner";
import JsonView from "@uiw/react-json-view";
import {
  unbookedFlightAtom,
  unbookedHotelAtom,
} from "@/features/itineraries/store/unbooked";
import arrayHasElements from "@/common/utils/array-has-elements";
import { mapUnbookedHotelToItinerary } from "@/features/itineraries/utils/unbooked";
import { useTripsList } from "@/common/hooks/api";

export default function Itinerary() {
  const currentTrip = useAtomValue(currentTripAtom);
  const tripDetails = useAtomValue(tripDetailsAtom);
  const [activeTab, setActiveTab] = useAtom(activeTabAtom);
  const travelContextDebug = useAtomValue(travelContextDebugAtom);
  const travelContext = useAtomValue(travelContextAtom);
  const setFocusedTripEventId = useSetAtom(focusedTripEventIdAtom);

  const { data: tripsData } = useTripsList();

  const { closeRightSidebar, rightSidebar } = useRightSidebarSwitch();

  const isOpen = rightSidebar === RightSidebars.ITINERARY;

  useOnNavigation(
    useCallback(() => {
      setActiveTab(ItineraryTab.Flights);
    }, [setActiveTab])
  );

  const onClose = useCallback(() => {
    setFocusedTripEventId(undefined);
    closeRightSidebar();
  }, [closeRightSidebar, setFocusedTripEventId]);

  const isLoading = tripDetails?.isLoading;
  const { itinerary } = tripDetails?.[currentTrip as number] ?? {};
  const { accommodations, flight } = itinerary ?? {};

  const unbookedFlight = useAtomValue(unbookedFlightAtom);
  const unbookedHotel = useAtomValue(unbookedHotelAtom);

  const hotelData: Accommodation[] = arrayHasElements(accommodations)
    ? accommodations
    : !!unbookedHotel
    ? mapUnbookedHotelToItinerary(unbookedHotel)
    : [];

  const showTripActions = useMemo(
    () => !tripsData?.past?.some((trip) => trip.id === currentTrip),
    [tripsData, currentTrip]
  );

  const flightItineraryStatus = useMemo(() => {
    if (!flight) {
      return BookingStatuses.UNBOOKED;
    }

    return [
      ...(!!flight?.legs?.length
        ? flight.legs
        : [flight.outbound, flight.return]),
    ].some((leg) => !!leg && leg.cancelled)
      ? BookingStatuses.CANCELLED
      : BookingStatuses.BOOKED;
  }, [flight]);

  const hasFlight = !!flight || !!unbookedFlight;
  const statusDeterminedFlight = {
    ...(!!flight ? flight : unbookedFlight),
    status: flightItineraryStatus,
  };

  if (isNaN(currentTrip as number)) {
    return null;
  }

  const hasBothTabs = !!accommodations?.length && hasFlight;
  const showTravelContext = travelContextDebug && !!travelContext;

  return (
    <WithSidebar header="Your itinerary" isOpen={isOpen} onClose={onClose}>
      {isLoading ? (
        <LoadingSpinner className="mx-auto mt-2" />
      ) : hasBothTabs || showTravelContext ? (
        <TabView
          activeIndex={activeTab}
          onTabChange={(e) => setActiveTab(e.index)}
        >
          <TabPanel header="Flights">
            {itinerary === undefined ? (
              <LoadingSpinner className="my-2 mx-auto" />
            ) : (
              <FlightsTab
                {...(statusDeterminedFlight as ItineraryType["flight"] & {
                  status: BookingStatuses;
                })}
                showTripActions={showTripActions}
              />
            )}
          </TabPanel>

          <TabPanel header="Hotels">
            <HotelsTab
              accommodations={hotelData}
              showTripActions={showTripActions}
            />
          </TabPanel>

          {showTravelContext && (
            <TabPanel header="Travel Context">
              <h2 className="font-bold text-xl">Travel context</h2>
              <div className="mt-4">
                <JsonView
                  value={travelContext}
                  collapsed={false}
                  displayDataTypes={false}
                />
              </div>
            </TabPanel>
          )}
        </TabView>
      ) : !!hasFlight ? (
        <FlightsTab
          {...(statusDeterminedFlight as ItineraryType["flight"] & {
            status: BookingStatuses;
          })}
          showTripActions={showTripActions}
        />
      ) : (
        <HotelsTab
          accommodations={hotelData as ItineraryType["accommodations"]}
          showTripActions={showTripActions}
        />
      )}
    </WithSidebar>
  );
}
