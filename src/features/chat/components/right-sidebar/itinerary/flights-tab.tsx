import React from "react";
import Itinerary, {
  BookingStatuses,
} from "@/features/itineraries/types/itinerary";
import arrayHasElements from "@/common/utils/array-has-elements";
import PriceSummary from "./price-summary";
import BookingStatus from "./booking-status";
import { ChangeReservationContact } from "@/features/itineraries/components/change-reservation-contact";
import FlightDetails from "@/features/chat/components/right-sidebar/flight-details/flight-details";
import RecommendationChecklist from "@/features/chat/components/recommendation-reasons/recommendation-checklist";
import { TripActions } from "./trip-actions";

function FlightsTab(
  props: Itinerary["flight"] & {
    status: BookingStatuses;
    showTripActions?: boolean;
  }
) {
  const {
    outbound: outboundFlight,
    return: returnFlight,
    status,
    legs,
    price_summary,
    oldOutbound,
    oldReturn,
    old_legs,
    showTripActions = false,
  } = props;

  const hasOutboundFlight = arrayHasElements(
    outboundFlight?.flight_segments[0]?.flight_stops
  );
  const hasReturnFlight = arrayHasElements(
    returnFlight?.flight_segments[0]?.flight_stops
  );

  if (!hasOutboundFlight && !hasReturnFlight && !arrayHasElements(legs)) {
    return <h2>No flight booked.</h2>;
  }

  const recommendationReasons =
    arrayHasElements(legs) &&
    legs.map((leg) => leg?.recommendationReasons?.[0]).filter(Boolean);

  return (
    <div className="space-y-6">
      <BookingStatus status={status} />

      {arrayHasElements(legs) ? (
        <div className="space-y-6">
          {legs.map((leg, index) => (
            <FlightDetails
              {...leg}
              isSelected={index === 0 && status === BookingStatuses.UNBOOKED}
              key={`flight-itinerary-leg-${index}`}
              oldData={old_legs && old_legs[index]}
            />
          ))}
        </div>
      ) : (
        <>
          {!!outboundFlight && (
            <FlightDetails {...outboundFlight} oldData={oldOutbound} />
          )}

          {!!returnFlight && (
            <FlightDetails {...returnFlight} oldData={oldReturn} />
          )}
        </>
      )}

      {price_summary &&
        (status === BookingStatuses.UNBOOKED ? (
          <div>
            <span className="text-neutral-500">From:</span>
            <span className="font-semibold ml-2">
              ${price_summary.total.amount}
            </span>
          </div>
        ) : (
          <PriceSummary
            prices={getPriceBreakdown(price_summary)}
            total={
              price_summary?.total?.amount ??
              price_summary.base.amount +
                price_summary.tax.amount +
                (price_summary.total_seat_price?.amount ?? 0)
            }
          />
        ))}

      {arrayHasElements(recommendationReasons) && (
        <RecommendationChecklist reasons={recommendationReasons as string[]} />
      )}

      {status === BookingStatuses.BOOKED && (
        <TripActions showActions={showTripActions} />
      )}
      <ChangeReservationContact />
    </div>
  );
}

function getPriceBreakdown(
  price_summary: Itinerary["flight"]["price_summary"]
): { [key: string]: number } {
  const priceBreakdown: { [key: string]: number } = {};

  if (price_summary) {
    priceBreakdown["Base fare"] = price_summary.base.amount;
    priceBreakdown["Tax & fees"] = price_summary.tax.amount;
    if (price_summary.total_seat_price?.amount) {
      priceBreakdown["Paid seat"] = price_summary.total_seat_price.amount;
    }
  }

  return priceBreakdown;
}

const MemoizedFlightsTab = React.memo(FlightsTab);
export default MemoizedFlightsTab;
