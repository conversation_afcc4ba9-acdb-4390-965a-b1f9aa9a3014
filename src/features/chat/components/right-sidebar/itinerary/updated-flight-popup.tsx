import { useAtom } from "jotai";
import { updatedFlightPopupAtom } from "@/features/itineraries/store/itinerary";
import Popup from "@/common/components/popup";
import FlightStops from "@/features/chat/components/right-sidebar/flight-details/flight-stops";
import IconArrowSolid from "@/common/components/icons/arrow-solid";
import { useToggle } from "@/common/hooks/toggles";
import clsx from "clsx";
import { Button } from "primereact/button";

export default function UpdatedFlightPopup() {
  const { toggle, value: isCurrent } = useToggle(false);
  const [updatedFlightPopup, setUpdatedFlight] = useAtom(
    updatedFlightPopupAtom
  );

  if (!updatedFlightPopup?.flight || !updatedFlightPopup?.oldFlight) {
    return null;
  }

  return (
    <Popup
      className="w-11/12 max-w-max lg:max-w-5xl"
      header="Updated flight data"
      onClose={() => setUpdatedFlight(undefined)}
    >
      <Button
        className="bg-teal-100 mb-4 p-2 text-gray-900 md:hidden dark:text-white"
        onClick={toggle}
      >
        {`Show ${isCurrent ? "old" : "current"} data`}
      </Button>
      <div className="flex items-center justify-evenly gap-x-10 relative">
        <div className="absolute bg-neutral-150 h-full hidden left-1/2 w-0.5 md:block"></div>
        <div
          className={clsx(
            "grow shrink basis-1/2 max-w-96 grayscale opacity-70 md:block",
            { hidden: isCurrent }
          )}
        >
          <FlightStops stops={updatedFlightPopup?.oldFlight} />
        </div>
        <IconArrowSolid className="hidden rotate-90 md:block [&_rect]:fill-white [&_path]:fill-amber-500" />
        <div
          className={clsx("grow shrink basis-1/2 max-w-96 md:block", {
            hidden: !isCurrent,
          })}
        >
          <FlightStops stops={updatedFlightPopup?.flight} />
        </div>
      </div>
    </Popup>
  );
}
