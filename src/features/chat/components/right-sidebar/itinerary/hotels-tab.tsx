import Link from "next/link";
import Image from "next/image";
import clsx from "clsx";
import { UserRole } from "@/features/user/types/user";
import Itinerary, {
  BookingStatuses,
} from "@/features/itineraries/types/itinerary";
import ImageSizes from "@/common/types/image-sizes";
import { useUserProfile } from "@/features/user/hooks/api";
import arrayHasElements from "@/common/utils/array-has-elements";
import objectHasEntries from "@/common/utils/object-has-entries";
import generateSizesString from "@/common/utils/generate-sizes-string";
import PriceSummary from "./price-summary";
import BookingStatus from "./booking-status";
import { formatPrice } from "@/common/utils/format-price";
import { ChangeReservationContact } from "@/features/itineraries/components/change-reservation-contact";
import { ConfirmationCodeDisplay } from "@/features/itineraries/components/itineraries-table/trip-event-confirmation-code-body";
import { useAtomValue } from "jotai";
import { focusedTripEventIdAtom } from "@/features/itineraries/store/itinerary";
import { useEffect } from "react";
import { TripActions } from "./trip-actions";

const imageSizes: ImageSizes = {
  "524px": "435px",
  base: "calc(91.6667vw - 3rem)",
};

export default function HotelsTab({
  accommodations,
  showTripActions = false,
}: {
  accommodations: Itinerary["accommodations"];
  showTripActions?: boolean;
}) {
  const focusedTripEventId = useAtomValue(focusedTripEventIdAtom);

  const { role } = useUserProfile();

  const atLeastOneBooked = accommodations.some(
    ({ status }) => status === BookingStatuses.BOOKED
  );

  useEffect(() => {
    const elementToScrollTo = document.getElementById(focusedTripEventId ?? "");
    if (elementToScrollTo) {
      setTimeout(() => {
        elementToScrollTo.scrollIntoView({ behavior: "smooth" });
      }, 300);
    }
  }, [focusedTripEventId]);

  if (!arrayHasElements(accommodations)) {
    return <h2>No hotel booked.</h2>;
  }

  return (
    <div className="space-y-6">
      {accommodations.map(
        (
          {
            hotel,
            img,
            checkIn,
            checkOut,
            manageBookingURL,
            mapMarker,
            phone,
            reservation_number,
            room,
            status,
            website,
            id,
          },
          index
        ) => {
          const {
            no_nights,
            options,
            option_title,
            payment_policy,
            price,
            priceExcludingFees,
            pricePerNight,
            taxAndFees,
          } = room ?? {};

          return (
            <div
              className={clsx("flex flex-col gap-y-6 relative", {
                "pt-8 border-t border-gray-200": index > 0,
              })}
              id={id}
              key={index}
            >
              <BookingStatus
                bookedTitle="Hotel booked"
                cancelledTitle="Hotel cancelled"
                status={status}
                unbookedTitle="Hotel not booked yet"
              />

              {objectHasEntries(img) && (
                <div className="aspect-[2.185] relative w-full">
                  <Image
                    alt={img.alt}
                    className="object-cover rounded-lg"
                    fill
                    sizes={generateSizesString(imageSizes)}
                    src={img.src}
                  />
                </div>
              )}

              <div>
                {hotel && <p className="font-semibold leading-5.5">{hotel}</p>}
                {mapMarker?.address && (
                  <p className="leading-5.5 text-neutral-500">
                    {mapMarker.address}
                  </p>
                )}
                {phone && (
                  <p className="leading-5.5 text-neutral-500">Phone {phone}</p>
                )}
                {website && (
                  <Link
                    href={website}
                    className="text-blue-500 hover:underline block"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Hotel website
                  </Link>
                )}
                {role === UserRole.ADMIN && manageBookingURL && (
                  <Link
                    href={manageBookingURL}
                    className="text-blue-500 hover:underline block"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Manage booking
                  </Link>
                )}
              </div>

              <div>
                {checkIn && (
                  <p className="leading-5.5">
                    <span className="font-semibold mr-1">Check in:</span>
                    {checkIn}
                  </p>
                )}
                {checkOut && (
                  <p className="leading-5.5">
                    <span className="font-semibold mr-1">Check out:</span>
                    {checkOut}
                  </p>
                )}
              </div>

              <div>
                {option_title && (
                  <p className="leading-5.5">
                    <span className="font-semibold mr-1">Room type:</span>
                    {option_title}
                  </p>
                )}
                {reservation_number && (
                  <div className="leading-5.5">
                    <span className="font-semibold mr-1">Confirmation:</span>
                    <ConfirmationCodeDisplay code={reservation_number} />
                  </div>
                )}
              </div>

              {arrayHasElements(options) &&
                options?.map((option, idx) => (
                  <p className="text-neutral-500" key={idx}>
                    {option}
                  </p>
                ))}

              {price && (
                <PriceSummary
                  prices={{
                    [`${no_nights} nights x $${formatPrice(pricePerNight)}`]:
                      priceExcludingFees,
                    "Taxes & fees": taxAndFees,
                  }}
                  total={price}
                />
              )}

              {!!payment_policy?.display_label && (
                <p className="text-neutral-500">
                  {payment_policy.display_label}
                </p>
              )}
            </div>
          );
        }
      )}
      {atLeastOneBooked && <TripActions showActions={showTripActions} />}
      <ChangeReservationContact />
    </div>
  );
}
