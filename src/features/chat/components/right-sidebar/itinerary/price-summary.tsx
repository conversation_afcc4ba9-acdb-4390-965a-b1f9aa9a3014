import { formatPrice } from "@/common/utils/format-price";
import objectHasEntries from "@/common/utils/object-has-entries";

interface PriceSummaryProps {
  className?: string;
  prices?: { [key: string]: number };
  total: number;
  credits?: number;
}

export default function PriceSummary({
  className,
  prices,
  total,
  credits,
}: PriceSummaryProps) {
  return (
    <div className={className}>
      <div className="font-semibold text-start">Price summary</div>
      <div className="text-neutral-500 pb-1 dark:text-neutral-400">
        {prices &&
          objectHasEntries(prices) &&
          Object.entries(prices).map(([description, value]) => (
            <div key={description} className="flex justify-between">
              <div className="w-full">{description}</div>
              <div className="float-right">{`$${formatPrice(value)}`}</div>
            </div>
          ))}
        {!!credits && (
          <div className="flex justify-between text-green-550">
            <div className="w-full">Available credits</div>
            <div className="float-right">-${formatPrice(credits)}</div>
          </div>
        )}
      </div>
      <div className="border-t border-neutral-250 flex justify-between pt-1 dark:border-gray-600">
        <div className="w-full text-neutral-500 dark:text-neutral-400">
          {credits ? "Net price" : "Total price"}
        </div>
        <div className="float-right font-semibold">{`$${formatPrice(
          total
        )}`}</div>
      </div>
    </div>
  );
}
