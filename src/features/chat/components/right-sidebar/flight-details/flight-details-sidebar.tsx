import { useAtomValue } from "jotai";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { flightDetailsAtom } from "@/features/chat/store/sidebar";
import RightSidebars from "@/features/chat/types/right-sidebars";
import arrayHasElements from "@/common/utils/array-has-elements";
import WithSidebar from "@/common/components/sidebar/with-right-sidebar";
import RecommendationChecklist from "@/features/chat/components/recommendation-reasons/recommendation-checklist";
import FlightDetailsSubmit from "./submit-btn";
import FlightDetails from "./flight-details";
import PriceSummary from "../itinerary/price-summary";

export default function FlightDetailsSidebar() {
  const flightDetails = useAtomValue(flightDetailsAtom);
  const { closeRightSidebar, rightSidebar } = useRightSidebarSwitch();

  if (!flightDetails) return;

  const {
    arrival,
    credits,
    departure,
    flightStops,
    net_price,
    price,
    recommendationReasons,
  } = flightDetails ?? {};
  const initialPrice = parseFloat(price?.amount ?? "0");
  const totalPrice = parseFloat(net_price?.amount ?? "0") || initialPrice;
  const creditsAmount = parseFloat(credits?.amount ?? "0");

  return (
    <WithSidebar
      header="Flight details"
      isOpen={rightSidebar === RightSidebars.FLIGHT_DETAILS}
      onClose={closeRightSidebar}
    >
      <FlightDetails
        action={flightDetails.userAction}
        flight_segments={[
          {
            destination_code: arrival.airport.code,
            destination_name: arrival.airport.name,
            flight_stops: flightStops,
            origin_code: departure.airport.code,
            origin_name: departure.airport.name,
          },
        ]}
      />

      {!!initialPrice && (
        <div className="mt-6">
          <PriceSummary
            prices={
              creditsAmount
                ? {
                    "Total price": initialPrice,
                  }
                : undefined
            }
            credits={creditsAmount}
            total={totalPrice}
          />
        </div>
      )}

      {arrayHasElements(recommendationReasons) && (
        <div className="mt-8">
          <RecommendationChecklist
            reasons={recommendationReasons as string[]}
          />
        </div>
      )}
      <FlightDetailsSubmit />
    </WithSidebar>
  );
}
