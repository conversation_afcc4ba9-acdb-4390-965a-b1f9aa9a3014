import getDuration from "@/common/utils/get-duration";
import { LocalizedTimestamp } from "@/features/chat/types/flights";

type LayoverProps = {
  start: LocalizedTimestamp;
  end: LocalizedTimestamp;
  airport: string;
};

export default function Layover({ airport, end, start }: LayoverProps) {
  return (
    <p className="mt-6">
      {getDuration(start, end)}
      <span className="mx-2">•</span>
      Change planes in {airport}
    </p>
  );
}
