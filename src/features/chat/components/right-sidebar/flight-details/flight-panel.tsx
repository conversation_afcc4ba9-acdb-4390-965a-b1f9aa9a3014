import Image from "next/image";
import clsx from "clsx";
import { Panel } from "primereact/panel";
import { <PERSON><PERSON> } from "primereact/button";
import { FlightStop } from "@/features/chat/types/api";
import colors from "@/common/constants/colors";
import { airlineLogos } from "@/common/constants/airline-logos";
import arrayHasElements from "@/common/utils/array-has-elements";
import formatFlightPanelTimestamps from "@/features/chat/utils/format-flight-panel-timestamps";
import IconExclamationSolid from "@/common/components/icons/exclamation-solid";
import { twMerge } from "tailwind-merge";
import { ConfirmationCodeDisplay } from "@/features/itineraries/components/itineraries-table/trip-event-confirmation-code-body";
import { capitalizeFirstLetter } from "@/common/utils/capitalize-first-letter";

type FlightDetailsProps = {
  airport: string;
  className?: string;
  date: string;
  header: string;
  time: string;
};

function FlightDetails({
  airport,
  className,
  date,
  header,
  time,
}: FlightDetailsProps) {
  return (
    <div className={twMerge("flex-1", className)}>
      <p className="font-semibold">{header}</p>
      <p>{airport}</p>
      <p>{date}</p>
      <p>{time}</p>
    </div>
  );
}

type FlightPanelProps = FlightStop & {
  changed?: boolean;
  className?: string;
  openHandler?: VoidFunction;
};

export default function FlightPanel({
  airline_code,
  airline_name,
  arrival,
  arrival_timezone,
  changed,
  className,
  confirmation,
  departure,
  departure_timezone,
  destination_code,
  destination_name,
  flight_number,
  openHandler,
  origin_code,
  origin_name,
  seat,
  cabin,
}: FlightPanelProps) {
  if (!arrival || !departure) {
    return null;
  }

  const { date: arrivalDay, time: arrivalTime } = formatFlightPanelTimestamps(
    arrival,
    arrival_timezone
  );

  const { date: departureDay, time: departureTime } =
    formatFlightPanelTimestamps(departure, departure_timezone);

  const cardBottom = [
    cabin ? capitalizeFirstLetter(cabin) : "",
    seat ? seat : "",
    confirmation ? confirmation : "",
  ].filter(Boolean);

  const header = (
    <>
      <div className="flex items-center gap-x-3">
        <Image
          alt={"Airline logo"}
          height={32}
          src={!!airline_code ? airlineLogos[airline_code] : ""}
          width={32}
        />
        <span>{`${airline_name} ${flight_number}`}</span>
      </div>

      {!!changed && (
        <div className="flex items-center gap-x-4 mt-4">
          <IconExclamationSolid
            className="basis-[16px] grow-0 ml-auto shrink-0"
            fill={colors.amber[500]}
          />
          <span className="text-sm leading-4">
            Flight details have been changed
            <Button
              className="inline text-primary-500 px-1.5"
              link
              onClick={openHandler}
            >
              click here
            </Button>
            to view the changes.
          </span>
        </div>
      )}
    </>
  );

  return (
    <>
      <Panel
        pt={{
          header: {
            className: "pb-3",
          },
          root: {
            className: clsx(
              "border border-neutral-250 shadow-none dark:border-gray-600",
              className
            ),
          },
          title: { className: "font-normal" },
        }}
        header={header}
      >
        <FlightDetails
          airport={`${origin_name} (${origin_code})`}
          date={departureDay}
          header="Departs"
          time={departureTime}
        />

        <FlightDetails
          airport={`${destination_name} (${destination_code})`}
          className="mt-2.5"
          date={arrivalDay}
          header="Arrives"
          time={arrivalTime}
        />
        {arrayHasElements(cardBottom) && (
          <div className="border-t border-neutral-250 mt-3 pt-3 text-neutral-500 dark:border-gray-600 dark:text-neutral-400">
            {!!cabin && capitalizeFirstLetter(cabin)}
            {!!cabin && (!!seat || !!confirmation) && " • "}
            {!!seat && `Seat ${seat}`}
            {!!seat && !!confirmation && " • "}
            {!!confirmation && <ConfirmationCodeDisplay code={confirmation} />}
          </div>
        )}
      </Panel>
    </>
  );
}
