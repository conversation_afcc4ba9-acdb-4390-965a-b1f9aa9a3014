import { FlightStop } from "@/features/chat/types/api";
import Layover from "./layover";
import FlightPanel from "./flight-panel";

type FlightStopsProps = {
  stops: FlightStop[];
  openHandler?: VoidFunction;
};

export default function FlightStops({ openHandler, stops }: FlightStopsProps) {
  return stops?.map((stop, index) => {
    const { departure, departure_timezone, origin_code, origin_name } =
      stop ?? {};
    const { arrival, arrival_timezone } = stops[index - 1] ?? {};

    return (
      <div className="mt-6" key={index}>
        {index > 0 && (
          <Layover
            airport={`${origin_name} (${origin_code})`}
            end={{ timestamp: departure, timezone: departure_timezone }}
            start={{ timestamp: arrival, timezone: arrival_timezone }}
          />
        )}
        <FlightPanel
          className={index > 0 ? "mt-6" : undefined}
          {...stop}
          openHandler={openHandler}
          key={index}
        />
      </div>
    );
  });
}
