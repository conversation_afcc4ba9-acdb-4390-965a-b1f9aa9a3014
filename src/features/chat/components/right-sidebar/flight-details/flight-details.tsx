import FlightStops from "./flight-stops";
import { Flight } from "@/features/chat/types/api";
import { useAtomValue, useSetAtom } from "jotai";
import {
  focusedTripEventIdAtom,
  updatedFlightPopupAtom,
} from "@/features/itineraries/store/itinerary";
import { airportCityMapping } from "@/features/itineraries/utils/airport-city-mapping";
import { dateWithTimezone } from "@/common/utils";
import { formatFlightSegmentDuration } from "@/common/utils/flight";
import { useEffect, useRef } from "react";
import arrayHasElements from "@/common/utils/array-has-elements";
import { Button } from "primereact/button";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheck } from "@fortawesome/free-solid-svg-icons";

type FlightDetailsProps = Pick<Flight, "action" | "flight_segments"> & {
  isSelected?: boolean;
  oldData?: Flight;
};

export default function FlightDetails({
  flight_segments,
  isSelected,
  oldData,
}: FlightDetailsProps) {
  const focusedTripEventId = useAtomValue(focusedTripEventIdAtom);
  const setUpdatedFlightPopup = useSetAtom(updatedFlightPopupAtom);

  const ref = useRef<HTMLDivElement>(null);

  const {
    destination_code,
    destination_name,
    flight_stops,
    origin_code,
    origin_name,
  } = flight_segments[0] ?? {};

  useEffect(() => {
    if (focusedTripEventId === origin_code) {
      setTimeout(() => {
        ref.current?.scrollIntoView({ behavior: "smooth" });
      }, 300);
    }
  }, [focusedTripEventId, origin_code]);

  if (!arrayHasElements(flight_segments)) {
    return null;
  }

  const origin = airportCityMapping[origin_code] ?? origin_name;
  const destination = airportCityMapping[destination_code] ?? destination_name;
  const { departure, departure_timezone } = flight_stops?.[0] ?? {};
  const departureDate =
    !!departure &&
    !!departure_timezone &&
    dateWithTimezone(departure, departure_timezone).format("MMMM DD, YYYY");
  const totalDuration = formatFlightSegmentDuration(flight_segments[0]);

  const openHandler = () =>
    setUpdatedFlightPopup({
      flight: flight_stops,
      oldFlight: oldData?.flight_segments?.[0]?.flight_stops,
    });

  const getStopsCount = () => {
    const count = flight_stops.length - 1;
    if (!count) {
      return "Non-stop";
    }
    if (count === 1) {
      return "1 stop";
    }
    return `${count} stops`;
  };

  return (
    <div ref={ref}>
      <div className="flex items-start justify-between">
        <div>
          <h2 className="font-bold lg:text-xl">{`${origin} to ${destination}`}</h2>
          {!!departureDate && (
            <p className="mt-1 text-neutral-500">{departureDate}</p>
          )}
          <p className="mt-3 text-neutral-500">{`${getStopsCount()} • Total duration ${totalDuration}`}</p>
        </div>
        {isSelected && (
          <Button
            className="disabled:bg-gray-950 disabled:text-white"
            disabled={true}
          >
            <FontAwesomeIcon icon={faCheck} />
            Selected
          </Button>
        )}
      </div>
      <FlightStops
        stops={flight_stops.map((stop) => ({ ...stop, changed: !!oldData }))}
        openHandler={openHandler}
      />
    </div>
  );
}
