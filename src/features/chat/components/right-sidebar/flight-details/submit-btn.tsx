import { use<PERSON>tom } from "jotai";
import clsx from "clsx";
import { But<PERSON> } from "primereact/button";
import { flightDetailsAtom } from "@/features/chat/store/sidebar";
import IconCheck from "@/common/components/icons/check";

export default function FlightDetailsSubmit() {
  const [flightDetails, setFlightDetails] = useAtom(flightDetailsAtom);
  const { isDisabled, isExpired, isSubmitted, onSubmit } = flightDetails ?? {};

  if (isExpired) {
    return null;
  }

  const submitHandler = () => {
    if (typeof onSubmit === "function" && !isDisabled && !isExpired) {
      onSubmit();

      !!flightDetails &&
        setFlightDetails({
          ...flightDetails,
          isDisabled: true,
          isSubmitted: true,
        });
    }
  };

  return (
    <Button
      className={clsx("mt-4", {
        "disabled:bg-gray-950 disabled:text-white": isSubmitted,
      })}
      disabled={isDisabled}
      onClick={submitHand<PERSON>}
      outlined={!isSubmitted}
      data-testid="select-button"
    >
      {isSubmitted ? (
        <>
          <IconCheck />
          Selected
        </>
      ) : (
        "Select"
      )}
    </Button>
  );
}
