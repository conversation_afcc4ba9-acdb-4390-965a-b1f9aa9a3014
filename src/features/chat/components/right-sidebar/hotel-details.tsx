import Image from "next/image";
import { use<PERSON>tom } from "jotai";
import clsx from "clsx";
import { <PERSON><PERSON> } from "primereact/button";
import { Chip } from "primereact/chip";
import { useState } from "react";
import RightSidebars from "@/features/chat/types/right-sidebars";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { hotelDetailsAtom } from "@/features/chat/store/sidebar";
import arrayHasElements from "@/common/utils/array-has-elements";
import WithSidebar from "@/common/components/sidebar/with-right-sidebar";
import IconStar from "@/common/components/icons/star";
import IconCheck from "@/common/components/icons/check";
import RecommendationChecklist from "../recommendation-reasons/recommendation-checklist";
import { getCheapestRoom } from "../../utils/cheapest-room";
import { formatPrice } from "@/common/utils/format-price";
import { Tag } from "primereact/tag";
import { Galleria } from "primereact/galleria";
import generateSizesString from "@/common/utils/generate-sizes-string";
import ImageSizes from "@/common/types/image-sizes";
import { ArrowRight } from "@/common/components/icons/arrow-right";
import WithinPolicy from "../within-policy";
import HotelsMap from "../rich-controls/hotels/hotels-map";
import { capitalizeFirstLetter } from "@/common/utils/capitalize-first-letter";

const imageSizes: ImageSizes = {
  "524px": "435px",
  base: "calc(91.6667vw - 3rem)",
};

export default function HotelDetails() {
  const [hotelDetails, setHotelDetails] = useAtom(hotelDetailsAtom);
  const { closeRightSidebar, rightSidebar } = useRightSidebarSwitch();
  const [showAllAmenities, setShowAllAmenities] = useState(false);
  const {
    amenities,
    hotel,
    hotel_class,
    photos,
    isDisabled,
    isExpired,
    isSubmitted,
    mapMarker,
    onSubmit,
    rating,
    recommendationReasons,
    rooms,
    rating_description,
    within_policy,
    within_or_out_policy_reason,
    allHotels,
    searchLocation,
  } = hotelDetails ?? {};
  const { address } = mapMarker ?? {};
  const reasons = Object.values(recommendationReasons ?? {});
  const amenitiesItems = Array.isArray(amenities)
    ? amenities
    : amenities?.split(",");
  const displayedAmenities = showAllAmenities
    ? amenitiesItems
    : amenitiesItems?.slice(0, 5);
  const hasMoreAmenities = amenitiesItems && amenitiesItems.length > 8;
  const isSidebarOpen = rightSidebar === RightSidebars.HOTEL_DETAILS;
  const cheapestRoom = getCheapestRoom(rooms);

  const submitHandler = () => {
    if (typeof onSubmit === "function" && !isDisabled && !isExpired) {
      onSubmit();

      !!hotelDetails &&
        setHotelDetails({
          ...hotelDetails,
          isDisabled: true,
          isSubmitted: true,
        });
    }
  };

  const galleryItem = (item: string) => {
    if (!item) return null;
    return (
      <Image
        alt="Hotel image"
        fill
        sizes={generateSizesString(imageSizes)}
        src={item}
        className="object-cover"
      />
    );
  };

  return (
    <WithSidebar
      header="Hotel details"
      isOpen={isSidebarOpen}
      onClose={closeRightSidebar}
    >
      <div className="flex flex-col gap-y-6">
        <div>
          {hotel_class != null && (
            <div className="flex gap-x-1.5 items-center mb-2">
              {Array.from(Array(Math.floor(hotel_class))).map((x, index) => (
                <IconStar className="w-3.5" key={index} />
              ))}
            </div>
          )}
          {hotel && <p className="font-semibold leading-5.5">{hotel}</p>}
          {address && (
            <p className="leading-5.5 text-neutral-500 font-normal">
              {address}
            </p>
          )}
        </div>
        {rating != null && (
          <div className="flex items-center gap-x-1.5">
            <Tag
              value={
                <>
                  <span className="font-semibold">{rating}</span>
                  <span className="font-normal">/10</span>
                </>
              }
            />
            <p>{capitalizeFirstLetter(rating_description)}</p>
          </div>
        )}

        <div className="flex items-center gap-x-3">
          <Button
            className={clsx({
              "disabled:bg-gray-950 disabled:text-white": isSubmitted,
            })}
            disabled={isDisabled || isExpired}
            onClick={submitHandler}
            outlined={!isSubmitted}
            data-testid="select-button"
          >
            {isSubmitted ? (
              <>
                <IconCheck />
                Selected
              </>
            ) : (
              "Select"
            )}
          </Button>
          <div className="text-sm">
            {!!cheapestRoom?.pricePerNight && (
              <div className="text-neutral-500">
                ${formatPrice(cheapestRoom.pricePerNight)}
                {` /night`}
              </div>
            )}
            {!!cheapestRoom?.price && (
              <div className="font-medium">
                <span className="font-bold">${formatPrice(cheapestRoom.price)}</span>
                {` total including taxes & fees`}
              </div>
            )}
          </div>
        </div>

        {arrayHasElements(photos) && (
          <div className="relative w-full h-[12.5rem]">
            <Galleria
              value={photos}
              item={galleryItem}
              circular
              showThumbnails={false}
              showItemNavigators
              itemNextIcon={<ArrowRight className="max-md:w-4" />}
              itemPrevIcon={<ArrowRight className="rotate-180 max-md:w-4" />}
            />
          </div>
        )}

        {within_policy != null && (
          <div className="flex flex-col gap-y-3 [&_p]:text-base">
            <WithinPolicy
              withinPolicy={within_policy}
              reason={within_or_out_policy_reason}
              overlayClassName="!transform-none max-w-96"
            />
          </div>
        )}

        <RecommendationChecklist reasons={reasons.slice(1)} />

        {arrayHasElements(amenities) && (
          <div>
            <p className="font-semibold mb-4">Amenities that matter to you</p>
            <div className="flex flex-wrap gap-2">
              {displayedAmenities?.map((item, index) => (
                <Chip
                  className="bg-gray-100 dark:bg-neutral-700 dark:text-white"
                  key={index}
                  label={item}
                  pt={{
                    label: {
                      className: "font-normal text-base leading-5.5",
                    },
                  }}
                />
              ))}
              {hasMoreAmenities && (
                <Button
                  className="text-base py-0 px-1 rounded-full text-neutral-500 self-center"
                  plain
                  onClick={() => setShowAllAmenities(!showAllAmenities)}
                >
                  {showAllAmenities ? "View less" : "View more"}
                </Button>
              )}
            </div>
          </div>
        )}

        {address && allHotels && isSidebarOpen && (
          <div className="space-y-4">
            <p className="font-semibold">Location</p>
            <div className="h-full aspect-square">
              <HotelsMap
                hotels={allHotels}
                eventAttended={mapMarker}
                searchLocation={searchLocation}
                activePointId={hotelDetails?.id}
              />
            </div>
          </div>
        )}
      </div>
    </WithSidebar>
  );
}
