import { ReactNode, RefObject, useEffect, useMemo, useRef } from "react";
import { useAtomValue, useSet<PERSON><PERSON> } from "jotai";
import { twMerge } from "tailwind-merge";
import clsx from "clsx";
import Markdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkBreaks from "remark-breaks";
import Optional from "@/common/types/optional";
import arrayHasElements from "@/common/utils/array-has-elements";
import {
  messageSelectionModeAtom,
  selectedMessagesAtom,
  showSkeletonAtom,
} from "@/features/chat/store/chat";
import { chatRefAtom } from "@/features/chat/store/components";
import { agentsDebugAtom } from "@/features/chat/store/debugging";
import {
  IncomingMessage,
  IncomingMessageStatus,
  IncomingMessageTypes,
  OutgoingMessageTypes,
} from "@/features/chat/types/messages";
import { User } from "@/features/user/types/user";

type ChatMessageProps = Optional<IncomingMessage, "type"> & {
  avatar?: ReactNode;
  children: ReactNode;
  className?: string;
  currentOttoUser?: User;
  messageId?: number;
  wrapperRef?: RefObject<HTMLDivElement>;
};

export default function ChatMessage({
  avatar,
  children,
  className,
  isBotMessage,
  partial,
  text,
  textColor,
  type,
  currentOttoUser,
  isStopped,
  status,
  messageId,
  wrapperRef,
}: ChatMessageProps) {
  const textNodes = !!text ? (Array.isArray(text) ? text : [text]) : [];

  const chatElement = useAtomValue(chatRefAtom)?.current;
  const isSelectionMode = useAtomValue(messageSelectionModeAtom);
  const selectedMessages = useAtomValue(selectedMessagesAtom);
  const showSkeleton = useAtomValue(showSkeletonAtom);
  const agentsDebug = useAtomValue(agentsDebugAtom);
  const setSelectedMessages = useSetAtom(selectedMessagesAtom);
  const hasMessageId = messageId !== undefined && messageId !== null;
  const isSelected = hasMessageId && selectedMessages.includes(messageId);

  useEffect(() => {
    if (!wrapperRef?.current || !chatElement) {
      return;
    }

    const userHasScrolled =
      chatElement.getAttribute("data-scrolled") === "true";

    const resizeObserver = new ResizeObserver(() => {
      if (!userHasScrolled) {
        chatElement.removeAttribute("data-scrolled");
      }
    });

    resizeObserver.observe(wrapperRef.current, {});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const isError =
    type === IncomingMessageTypes.ERROR ||
    status === IncomingMessageStatus.ERROR;
  const isSearchUpdate = type === IncomingMessageTypes.SEARCH_UPDATE;
  const isAiHuman = type === IncomingMessageTypes.AI_HUMAN;
  const isSilentPrompt = String(type) === OutgoingMessageTypes.SILENT_PROMPT;
  const isRedirectToTrip = type === IncomingMessageTypes.REDIRECT_TO_TRIP;
  const textColorClass = clsx(
    (agentsDebug || isError) && !!textColor
      ? `text-${textColor}`
      : "text-gray-900 dark:text-white",
    isSearchUpdate && "italic text-neutral-500 dark:!text-neutral-500",
    isRedirectToTrip && "italic text-neutral-500 dark:!text-neutral-500",
    isAiHuman && "italic text-neutral-600 dark:!text-neutral-400",
    isSilentPrompt && "italic text-indigo-500"
  );

  const messageTestId = useMemo(() => {
    if (isError) {
      return "error-message";
    }
    if (showSkeleton) {
      return "skeleton";
    }
    if (isSearchUpdate) {
      return "search-update";
    }
    if (isAiHuman) {
      return "ai-human-message";
    }
    if (isBotMessage) {
      return "otto-message";
    }
    return "user-message";
  }, [isError, showSkeleton, isBotMessage, isSearchUpdate, isAiHuman]);

  const handleToggleSelection = () => {
    if (!hasMessageId || !isSelectionMode) return;

    if (isSelected) {
      setSelectedMessages(
        selectedMessages.filter((id: number) => id !== messageId)
      );
    } else {
      setSelectedMessages([...selectedMessages, messageId]);
    }
  };

  return (
    <div
      className={twMerge(
        clsx("flex gap-x-2 items-start mt-8 relative first:mt-0", {
          "bg-gray-100 max-w-[90%] ml-auto p-2.5 pr-5 rounded-lg w-fit md:max-w-[75%] dark:bg-white/5 dark:text-white":
            !isBotMessage,
          "message-selectable": isSelectionMode && hasMessageId,
          "message-selected": isSelectionMode && isSelected,
        }),
        className
      )}
      data-testid={`chat-message-${messageId}`}
      onClick={isSelectionMode ? handleToggleSelection : undefined}
      ref={wrapperRef}
    >
      {avatar}
      {isSelectionMode && hasMessageId && (
        <div className="message-checkbox absolute right-2 top-2 z-10">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={handleToggleSelection}
            className="h-5 w-5 accent-blue-500"
          />
        </div>
      )}
      <div className="w-full break-words" data-testid={messageTestId}>
        {arrayHasElements(textNodes) &&
          textNodes.map((node, index) => (
            <Markdown
              className={clsx(
                "leading-6 [&>ol]:list-[auto] [&>ul]:list-[auto] [&>ol]:list-inside [&>ul]:list-inside [&_li_p]:inline",
                "[&_table]:border [&_th]:border [&_td]:border [&_th]:p-2 [&_td]:p-2",
                textColorClass,
                {
                  "font-semibold": isError,
                  "text-fade-out": !!partial,
                }
              )}
              key={index}
              remarkPlugins={[remarkGfm, remarkBreaks]}
            >
              {`${
                isSilentPrompt
                  ? "this is a silent prompt: "
                  : isAiHuman
                  ? "Otto thinking: "
                  : ""
              } ${node}`}
            </Markdown>
          ))}
        {isStopped && (
          <Markdown
            className={clsx(
              "leading-6 [&>ol]:list-[auto] [&>ul]:list-[auto] [&>ol]:list-inside [&>ul]:list-inside [&_li_p]:inline",
              "[&_table]:border [&_th]:border [&_td]:border [&_th]:p-2 [&_td]:p-2 italic text-neutral-500",
              {
                "font-semibold": isError,
                "text-fade-out": !!partial,
              }
            )}
            key={"stop"}
            remarkPlugins={[remarkGfm, remarkBreaks]}
          >
            {`${
              currentOttoUser?.firstName
                ? `${currentOttoUser.firstName}, you`
                : "You"
            } stopped this response.`}
          </Markdown>
        )}
        {children}
      </div>
    </div>
  );
}
