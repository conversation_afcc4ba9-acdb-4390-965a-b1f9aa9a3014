import { useRef, useState } from "react";
import { use<PERSON>tom, useAtomValue } from "jotai";
import { <PERSON><PERSON><PERSON><PERSON> } from "primereact/utils";
import clsx from "clsx";

import { IncomingMessage, RawToolOutput } from "@/features/chat/types/messages";
import { HotelData } from "@/features/chat/types/hotels";
import { refreshHotelsMessage } from "@/features/chat/constants/messages";
import { useSilentPromptScroll } from "@/features/chat/hooks/use-scroll-behaviour";
import useExpiry from "@/features/chat/hooks/use-expiry";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import { currentTripAtom } from "@/features/chat/store/current-trip";
import { accommodationSelectionsAtom } from "@/features/chat/store/chat";
import arrayHasElements from "@/common/utils/array-has-elements";
import HotelCard from "@/features/chat/components/rich-controls/hotels/hotel-card";
import SearchAgain from "@/features/chat/components/rich-controls/search-again";
import HotelRooms from "@/features/chat/components/rich-controls/hotels/hotel-rooms";
import HotelsMap from "@/features/chat/components/rich-controls/hotels/hotels-map";

type HotelsProps = {
  disabled?: boolean;
  eventAttended: IncomingMessage["eventAttended"];
  items: HotelData[];
  radius?: number;
  rawToolOutput?: RawToolOutput;
  searchLocation?: google.maps.LatLngLiteral;
  timeLimit: string;
  isLastSearch?: boolean;
};

export default function Hotels({
  disabled,
  eventAttended,
  items,
  radius,
  rawToolOutput,
  searchLocation,
  timeLimit,
  isLastSearch,
}: HotelsProps) {
  const [accommodationSelections, setAccommodationSelections] = useAtom(
    accommodationSelectionsAtom
  );
  const currentTrip = useAtomValue(currentTripAtom);

  const [activeItem, setActiveItem] = useState<string | null>(null);

  const roomsWrapper = useRef<HTMLDivElement>(null);
  const expired = useExpiry(timeLimit);
  const canSearchAgain = !disabled && expired && isLastSearch;
  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);
  const triggerScroll = useSilentPromptScroll();

  const searchKey = `${currentTrip}-${timeLimit}`;
  const selectedAccommodation = accommodationSelections[searchKey];
  const isTouchDevice = DomHandler.isTouchDevice();
  const hotelsToShow = items;

  if (!arrayHasElements(items)) {
    return null;
  }

  const onSelectHotel = (hotel: HotelData) => {
    setAccommodationSelections((prevSelections) => ({
      ...prevSelections,
      [searchKey]: {
        selectedHotelId: hotel.id,
        rooms: hotel.rooms.map((room) => ({
          ...room,
          amenities: hotel.amenities,
          recommendationReasons: hotel.recommendationReasons,
        })),
      },
    }));

    // Scroll on next render
    setTimeout(() =>
      roomsWrapper?.current?.scrollIntoView({
        behavior: "smooth",
      })
    );
  };

  return (
    <>
      <div
        className={clsx(
          "lg:flex lg:gap-x-4 lg:mt-8 grid grid-cols-1 lg:grid-cols-2 gap-4",
          {
            "w-full": isTouchDevice,
            "w-fit lg:w-full": !isTouchDevice,
          }
        )}
      >
        <div
          className="w-full hide-scrollbar max-w-full overflow-scroll overflow-y-visible pb-4 pt-8 lg:basis-2/3 /
        lg:shrink-0 lg:py-0 lg:overflow-visible"
        >
          <div
            className={clsx("gap-4.5 md:gap-4", {
              flex: isTouchDevice,
              "grid grid-cols-1 sm:grid-cols-2 sm:w-fit": !isTouchDevice,
            })}
          >
            {hotelsToShow.map((hotel, index) => (
              <HotelCard
                {...hotel}
                isDisabled={disabled}
                isExpired={expired}
                isActive={hotel.id === activeItem}
                key={index}
                mapMarker={hotel.mapMarker}
                onMouseEnter={() => setActiveItem(hotel.id)}
                onMouseLeave={() => setActiveItem(null)}
                onSubmit={() => onSelectHotel(hotel)}
                isSubmitted={
                  selectedAccommodation?.selectedHotelId === hotel.id
                }
                allHotels={hotelsToShow}
                searchLocation={searchLocation}
              />
            ))}
          </div>
        </div>

        <div className="aspect-[3] rounded-lg overflow-hidden w-full lg:h-auto lg:aspect-[unset] lg:mt-0">
          <HotelsMap
            hotels={hotelsToShow}
            eventAttended={eventAttended}
            radius={radius}
            rawToolOutput={rawToolOutput}
            searchLocation={searchLocation}
            activePointId={activeItem}
            onActiveItemChange={setActiveItem}
            onSelectHotel={onSelectHotel}
          />
        </div>
      </div>
      {canSearchAgain && (
        <SearchAgain
          onSubmit={() => {
            triggerScroll();
            send(refreshHotelsMessage);
          }}
        >
          This hotel data may not be current. Try running a new search.
        </SearchAgain>
      )}

      <div className="scroll-mb-8 w-full" ref={roomsWrapper}>
        {!expired && arrayHasElements(selectedAccommodation?.rooms) && (
          <HotelRooms
            items={selectedAccommodation?.rooms}
            timeLimit={timeLimit}
          />
        )}
      </div>
    </>
  );
}
