import { PropsWithChildren, useState, useMemo, useEffect } from "react";
import { Map } from "@vis.gl/react-google-maps";
import { IncomingMessage, RawToolOutput } from "@/features/chat/types/messages";
import { HotelData } from "@/features/chat/types/hotels";
import { getFurthestPointCoordinates, getMapBounds } from "@/common/utils/maps";
import objectHasEntries from "@/common/utils/object-has-entries";
import { MapMarker, MapSearchLocation } from "@/common/components/maps";
import { darkModeAtom } from "@/common/store/dark-mode";
import { DarkModeOptions } from "@/common/constants/theme";
import { useAtomValue, useSetAtom } from "jotai";
import { hotelDetailsAtom } from "@/features/chat/store/sidebar";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import RightSidebars from "@/features/chat/types/right-sidebars";

type HotelsMapProps = PropsWithChildren<{
  activePointId?: string | null;
  eventAttended?: IncomingMessage["eventAttended"];
  hotels: HotelData[];
  radius?: number;
  rawToolOutput?: RawToolOutput;
  searchLocation?: google.maps.LatLngLiteral;
  onActiveItemChange?: (id: string | null) => void;
  onSelectHotel?: (hotel: HotelData) => void;
}>;

export default function HotelsMap({
  eventAttended,
  hotels,
  radius,
  searchLocation,
  activePointId,
  onActiveItemChange,
  onSelectHotel,
}: HotelsMapProps) {
  const [activeItem, setActiveItem] = useState<string | null>(null);
  const setHotelDetails = useSetAtom(hotelDetailsAtom);
  const darkMode = useAtomValue(darkModeAtom);

  const mapBoundsPositions = useMemo(() => {
    const positions = hotels.map((item) => ({
      lat: parseFloat(item.mapMarker.coordinates.lat),
      lng: parseFloat(item.mapMarker.coordinates.lng),
    }));

    if (searchLocation?.lat && searchLocation?.lng) {
      const furthestFromCenter = getFurthestPointCoordinates(
        searchLocation,
        positions
      );

      const minimumDistanceCoordinates: google.maps.LatLngLiteral[] = [
        {
          lat: searchLocation.lat + 0.0005,
          lng: searchLocation.lng + 0.002,
        },
        {
          lat: searchLocation.lat - 0.0005,
          lng: searchLocation.lng - 0.002,
        },
      ];
      positions.push(...minimumDistanceCoordinates);

      const diametricallyOpposite = {
        lat: searchLocation.lat + (searchLocation.lat - furthestFromCenter.lat),
        lng: searchLocation.lng + (searchLocation.lng - furthestFromCenter.lng),
      };
      positions.push(diametricallyOpposite as google.maps.LatLngLiteral);
    }

    return positions;
  }, [hotels, searchLocation]);

  useEffect(() => {
    onActiveItemChange?.(activeItem);
  }, [activeItem, onActiveItemChange]);

  const getMapColorScheme = () => {
    if (darkMode === DarkModeOptions.DARK) {
      return "DARK";
    } else if (darkMode === DarkModeOptions.LIGHT) {
      return "LIGHT";
    } else {
      return "FOLLOW_SYSTEM";
    }
  };

  const { switchTo } = useRightSidebarSwitch();

  return (
    <Map
      defaultBounds={getMapBounds(mapBoundsPositions)}
      disableDefaultUI
      mapId={process.env.NEXT_PUBLIC_MAP_ID}
      colorScheme={getMapColorScheme()}
    >
      <MapSearchLocation coordinates={searchLocation} radius={radius} />
      {objectHasEntries(eventAttended?.coordinates) && (
        <MapMarker
          className="text-black"
          position={{
            lat: parseFloat(eventAttended?.coordinates.lat as string),
            lng: parseFloat(eventAttended?.coordinates.lng as string),
          }}
        />
      )}
      {hotels.map((hotel, index) => {
        const isActive = hotel.id === activeItem || hotel.id === activePointId;
        const markerPosition = {
          lat: parseFloat(hotel.mapMarker.coordinates.lat),
          lng: parseFloat(hotel.mapMarker.coordinates.lng),
        };

        return (
          <MapMarker
            isActive={isActive}
            key={index}
            onMouseEnter={() => setActiveItem(hotel.id)}
            onMouseLeave={() => setActiveItem(null)}
            onClick={() => {
              setActiveItem(isActive ? null : hotel.id);
              setHotelDetails({
                ...hotel,
                allHotels: hotels,
                onSubmit: () => onSelectHotel?.(hotel),
              });

              switchTo(RightSidebars.HOTEL_DETAILS);
            }}
            position={markerPosition}
            zIndex={isActive ? 2 : 1}
          >
            {hotel.mapMarker?.text}
          </MapMarker>
        );
      })}
    </Map>
  );
}
