import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react";
import Image from "next/image";
import { useAtom, useAtomValue } from "jotai";
import clsx from "clsx";
import { Card } from "primereact/card";
import { <PERSON><PERSON> } from "primereact/button";
import { Chip } from "primereact/chip";
import ImageSizes from "@/common/types/image-sizes";
import { HotelCard as HotelCardType } from "@/features/chat/types/hotels";
import RightSidebars from "@/features/chat/types/right-sidebars";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { isUserSelectedAtom } from "@/features/admin/store/foreign-user";
import { hotelDetailsAtom } from "@/features/chat/store/sidebar";
import generateSizesString from "@/common/utils/generate-sizes-string";
import RecommendationPanel from "@/features/chat/components/recommendation-reasons/recommendation-panel";
import IconCheck from "@/common/components/icons/check";
import { getCheapestRoom } from "@/features/chat/utils/cheapest-room";
import { formatPrice } from "@/common/utils/format-price";
import WithinPolicy from "../../within-policy";
import { Tag } from "primereact/tag";

export default function HotelCard(props: HotelCardType) {
  const {
    cancellation,
    hotel,
    id,
    img,
    isActive,
    isDisabled,
    isExpired,
    isSubmitted,
    mapMarker,
    onSubmit,
    onMouseEnter,
    onMouseLeave,
    payment,
    rating,
    recommendationReasons,
    rooms,
    within_or_out_policy_reason,
    within_policy,
    allHotels,
    searchLocation,
    ...cardProps
  } = props;

  const isUserSelected = useAtomValue(isUserSelectedAtom);
  const { rightSidebar, switchTo } = useRightSidebarSwitch();
  const [hotelDetails, setHotelDetails] = useAtom(hotelDetailsAtom);
  const { choice_characteristic, ...reasons } = recommendationReasons ?? {};
  const cheapestRoom = getCheapestRoom(rooms);

  const imageSizes: ImageSizes = {
    "1380px": "249px",
    "992px": "394px",
    "768px": "calc(50vw - 6.375rem)",
    "640px": "calc(50vw - 3.5rem)",
    base: "calc(100vw - 4rem)",
  };

  const submitHandler: MouseEventHandler<HTMLButtonElement> = (event) => {
    event.stopPropagation();

    if (isUserSelected) {
      return;
    }

    if (!isDisabled && !isExpired) {
      onSubmit();

      if (!!hotelDetails) {
        setHotelDetails({
          ...hotelDetails,
          isSubmitted: hotelDetails.id === id,
          isDisabled: true,
        });
      }
    }
  };

  return (
    <Card
      className={clsx(
        "cursor-pointer p-0.5 relative w-full min-w-[269px] max-w-96 sm:max-w-86 shrink-0 flex-1",
        {
          "!border-primary-500":
            isActive ||
            (hotelDetails?.id === id &&
              rightSidebar === RightSidebars.HOTEL_DETAILS),
        }
      )}
      onClick={() => {
        setHotelDetails(props);
        switchTo(RightSidebars.HOTEL_DETAILS);
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      data-testid="hotel-card"
    >
      <div className="flex flex-col gap-2">
        <Chip
          label={choice_characteristic}
          pt={{
            label: { className: "text-2xs leading-5" },
            root: {
              className: "absolute left-1.5 py-0 top-1.5 z-10",
            },
          }}
        />
        {!!img?.src && (
          <div className="aspect-[5/3] relative">
            <Image
              alt={img?.alt ?? "Hotel room picture"}
              className="object-cover rounded-md"
              fill
              sizes={generateSizesString(imageSizes)}
              src={img.src}
            />
          </div>
        )}
        <div className="px-2.5 pb-3.5 flex flex-col gap-y-2">
          <div className="flex gap-x-4 items-start justify-between">
            <div>
              <header className="font-bold text-sm line-clamp-1" title={hotel}>
                {hotel}
              </header>

              <RecommendationPanel
                pt={{ root: { className: "mt-2 *:leading-5" } }}
                reasons={Object.values(reasons)}
              />
            </div>
            {rating != null && (
              <Tag
                value={
                  <>
                    <span className="font-semibold">{rating}</span>
                    <span className="font-normal">/10</span>
                  </>
                }
              />
            )}
          </div>

          {within_policy != null && (
            <WithinPolicy
              className="hidden xl:flex xl:mt-1"
              withinPolicy={within_policy}
              reason={within_or_out_policy_reason}
            />
          )}

          <div
            className={clsx("flex gap-3 items-center", {
              hidden: isExpired,
            })}
          >
            <Button
              className={clsx({
                "disabled:bg-gray-950 disabled:text-white": isSubmitted,
              })}
              disabled={isDisabled || isUserSelected}
              onClick={!isUserSelected ? submitHandler : undefined}
              outlined={!isSubmitted}
              data-testid="select-button"
            >
              {isSubmitted ? (
                <>
                  <IconCheck />
                  Selected
                </>
              ) : (
                "Select"
              )}
            </Button>

            <div className="text-sm">
              {!!cheapestRoom?.pricePerNight && (
                <p className="text-neutral-500">
                  ${formatPrice(cheapestRoom.pricePerNight)}
                  /night
                </p>
              )}

              {!!cheapestRoom?.price && (
                <p className="font-medium">
                  <span className="font-bold">${formatPrice(cheapestRoom.price)}</span> total
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
