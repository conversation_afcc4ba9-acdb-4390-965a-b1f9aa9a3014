import useWebSocket from "@/features/chat/hooks/use-web-socket";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import {
  HotelRoomCard as HotelRoomCardType,
  HotelRoomData,
} from "@/features/chat/types/hotels";
import HotelRoomCard from "./hotel-room-card";
import Optional from "@/common/types/optional";
import { useAtom, useAtomValue } from "jotai";
import { accommodationSelectionsAtom } from "@/features/chat/store/chat";
import { currentTripAtom } from "@/features/chat/store/current-trip";
import { DomHandler } from "primereact/utils";
import clsx from "clsx";
import { useSilentPromptScroll } from "@/features/chat/hooks/use-scroll-behaviour";

type RoomTypesProps = {
  items?: Optional<HotelRoomCardType, "onSubmit">[];
  disabled?: boolean;
  timeLimit?: string;
};

export default function HotelRooms({
  items,
  disabled,
  timeLimit,
}: RoomTypesProps) {
  const [accommodationSelections, setAccommodationSelections] = useAtom(
    accommodationSelectionsAtom
  );
  const currentTrip = useAtomValue(currentTripAtom);
  const triggerScroll = useSilentPromptScroll();

  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);

  const searchKey = `${currentTrip}-${timeLimit}`;
  const selectedRoomId = accommodationSelections[searchKey]?.selectedRoomId;
  const isTouchDevice = DomHandler.isTouchDevice();

  const onSelectRoom = (roomId: HotelRoomData["id"]) => {
    setAccommodationSelections((prevSelections) => ({
      ...prevSelections,
      [searchKey]: {
        ...prevSelections[searchKey],
        selectedRoomId: roomId,
      },
    }));
  };

  return (
    <>
      <h1 className="mt-6 text-xl lg:mt-8">Choose a room type:</h1>

      <div
        className={clsx(
          "w-full max-w-full mt-1 py-4 lg:basis-2/3 lg:shrink-0 lg:py-0",
          {
            "hide-scrollbar overflow-scroll": isTouchDevice,
            "overflow-visible": !isTouchDevice,
          }
        )}
      >
        <div
          className={clsx("gap-4.5 md:gap-4", {
            flex: isTouchDevice,
            "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 sm:w-fit":
              !isTouchDevice,
          })}
        >
          {items?.map((card, index) => (
            <HotelRoomCard
              {...card}
              key={index}
              onSubmit={() => {
                triggerScroll();

                send({
                  text: card.action,
                  type: OutgoingMessageTypes.SILENT_PROMPT,
                });
                onSelectRoom(card.id);
              }}
              isDisabled={
                !!disabled ||
                (selectedRoomId != null && selectedRoomId !== card.id)
              }
              isSubmitted={card.id === selectedRoomId}
            />
          ))}
        </div>
      </div>
    </>
  );
}
