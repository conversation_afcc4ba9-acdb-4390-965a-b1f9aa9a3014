import { Card } from "primereact/card";
import Image from "next/image";
import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import clsx from "clsx";
import { But<PERSON> } from "primereact/button";
import ImageSizes from "@/common/types/image-sizes";
import RightSidebars from "@/features/chat/types/right-sidebars";
import {
  CancellationType,
  HotelRoomCard as HotelRoomCardType,
} from "@/features/chat/types/hotels";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { hotelRoomDetailsAtom } from "@/features/chat/store/sidebar";
import generateSizesString from "@/common/utils/generate-sizes-string";
import IconCheck from "@/common/components/icons/check";
import { formatPrice } from "@/common/utils/format-price";
import WithinPolicy from "../../within-policy";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Panel } from "primereact/panel";
import { Tooltip } from "@/common/components/tooltip";
import { useMemo } from "react";

const imageSizes: ImageSizes = {
  "1380px": "249px",
  "992px": "394px",
  "768px": "calc(50vw - 6.375rem)",
  "640px": "calc(50vw - 3.5rem)",
  base: "calc(100vw - 4rem)",
};

export default function HotelRoomCard(props: HotelRoomCardType) {
  const {
    isDisabled,
    isSubmitted,
    onSubmit,
    option_title,
    price,
    pricePerNight,
    room_photo,
    within_policy,
    within_or_out_policy_reason,
    id,
    cancellation_policy,
    payment_policy,
  } = props ?? {};
  const { switchTo } = useRightSidebarSwitch();
  const setHotelRoomDetails = useSetAtom(hotelRoomDetailsAtom);

  const cancellationPolicyLabel = useMemo(() => {
    switch (cancellation_policy?.type) {
      case CancellationType.FREE_CANCELLATION:
        return "Free cancellation";
      case CancellationType.SPECIAL_CONDITIONS:
        return "Partial refund";
      case CancellationType.NON_REFUNDABLE:
        return "Non-refundable";
      default:
        return null;
    }
  }, [cancellation_policy]);

  return (
    <Card
      className="cursor-pointer p-0.5 relative w-full min-w-[269px] max-w-96 sm:max-w-86 shrink-0 flex-1"
      pt={{
        body: {
          className: "h-full",
        },
        content: {
          className: "h-full",
        },
      }}
      onClick={() => {
        setHotelRoomDetails(props);
        switchTo(RightSidebars.HOTEL_ROOM_DETAILS);
      }}
      data-testid="hotel-room-card"
    >
      <div className="flex flex-col gap-2 h-full">
        {!!room_photo && (
          <div className="aspect-[5/3] relative grow-0">
            <Image
              alt={"Hotel room picture"}
              className="object-cover rounded-md"
              fill
              sizes={generateSizesString(imageSizes)}
              src={room_photo}
            />
          </div>
        )}

        <div className="flex flex-col gap-2 px-2.5 pb-3.5 h-full flex-1">
          <header
            className="font-bold text-sm line-clamp-1"
            title={option_title}
          >
            {option_title}
          </header>

          <div>
            {cancellationPolicyLabel && (
              <div className="flex gap-x-1 items-center text-sm">
                {cancellation_policy?.type !==
                CancellationType.NON_REFUNDABLE ? (
                  <FontAwesomeIcon
                    icon={["far", "arrow-rotate-left"]}
                    className="text-neutral-500"
                    style={{ fontSize: "12px" }}
                  />
                ) : (
                  <FontAwesomeIcon
                    icon={["far", "ban"]}
                    className="text-neutral-500"
                    style={{ fontSize: "12px" }}
                  />
                )}
                <span className="text-neutral-500">
                  {cancellationPolicyLabel}
                </span>
                {cancellation_policy?.display_policy && (
                  <>
                    <FontAwesomeIcon
                      icon={["far", "info-circle"]}
                      className={`text-neutral-500 w-3 h-3`}
                      style={{ fontSize: "12px" }}
                      data-tooltip-id={`cancellation-info-${id}`}
                      onClick={(event) => event.stopPropagation()}
                    />
                    <Tooltip
                      id={`cancellation-info-${id}`}
                      className="z-50 !bg-white !p-0 !text-black !text-base !rounded-lg dark:!bg-gray-900"
                      openOnClick
                    >
                      <Panel header="Cancellation policy">
                        <p className="relative w-full flex items-center gap-x-2 dark:text-white">
                          <span
                            className={clsx("h-5 w-0.5 rounded-full", {
                              "bg-green-500":
                                cancellation_policy.type ===
                                CancellationType.FREE_CANCELLATION,
                              "bg-orange-500":
                                cancellation_policy.type ===
                                CancellationType.SPECIAL_CONDITIONS,
                              "bg-red-550":
                                cancellation_policy.type ===
                                CancellationType.NON_REFUNDABLE,
                            })}
                          />
                          {cancellation_policy.display_policy}
                        </p>
                      </Panel>
                    </Tooltip>
                  </>
                )}
              </div>
            )}

            {payment_policy?.display_label && (
              <div className="flex gap-x-1 items-center text-sm">
                <FontAwesomeIcon
                  icon={["far", "wallet"]}
                  className="text-neutral-500"
                  style={{ fontSize: "12px" }}
                />
                <span className="text-neutral-500">
                  {payment_policy.display_label}
                </span>
                {payment_policy?.policy && (
                  <>
                    <FontAwesomeIcon
                      icon={["far", "info-circle"]}
                      className="text-neutral-500 w-3 h-3"
                      style={{ fontSize: "12px" }}
                      data-tooltip-id={`payment-info-${id}`}
                      onClick={(event) => event.stopPropagation()}
                    />
                    <Tooltip
                      id={`payment-info-${id}`}
                      className="z-50 !bg-white !p-0 !text-black !text-base !rounded-lg dark:!bg-gray-900"
                      openOnClick
                    >
                      <Panel header="Payment policy">
                        <span className="dark:text-white">{payment_policy.policy}</span>
                      </Panel>
                    </Tooltip>
                  </>
                )}
              </div>
            )}
          </div>

          {within_policy != null && (
            <WithinPolicy
              withinPolicy={within_policy}
              reason={within_or_out_policy_reason}
            />
          )}

          <div className="flex gap-3 xl:items-start mt-auto flex-col xl:flex-row items-start">
            <Button
              className={clsx("order-1 xl:order-none", {
                "disabled:bg-gray-950 disabled:text-white": isSubmitted,
              })}
              disabled={isDisabled || isSubmitted}
              onClick={(event) => {
                event.stopPropagation();
                onSubmit?.();
              }}
              outlined={!isSubmitted}
              data-testid="select-button"
            >
              {isSubmitted ? (
                <>
                  <IconCheck />
                  Selected
                </>
              ) : (
                "Select"
              )}
            </Button>

            <div className="text-sm">
              {!!pricePerNight && (
                <p className="text-neutral-500">
                  ${formatPrice(pricePerNight)} /night
                </p>
              )}
              {!!price && (
                <div className="font-medium">
                  <span className="font-bold">${formatPrice(price)}</span> total
                  <span className="xl:hidden block text-xs">
                    including taxes & fees
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
