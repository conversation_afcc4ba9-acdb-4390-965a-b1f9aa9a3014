import { PropsWithChildren, useEffect, useRef } from "react";
import { useAtomValue } from "jotai";
import { But<PERSON> } from "primereact/button";
import { userScrolledAtom } from "@/features/chat/store/chat";
import { chatRefAtom } from "@/features/chat/store/components";

type SearchAgainProps = PropsWithChildren<{
  onSubmit: () => void;
}>;

export default function SearchAgain({ children, onSubmit }: SearchAgainProps) {
  const promptsRef = useAtomValue(chatRefAtom);
  const userHasScrolled = useAtomValue(userScrolledAtom);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current && promptsRef?.current) {
      if (!userHasScrolled) {
        promptsRef?.current?.scrollTo(0, promptsRef?.current?.scrollHeight);
      }
    }
  }, [promptsRef, userHasScrolled]);

  return (
    <div
      className="flex items-center gap-x-4 bg-gray-100 mt-4 p-4 rounded-lg w-full dark:bg-white/5"
      ref={ref}
    >
      <Button onClick={() => onSubmit()} outlined>
        Search again
      </Button>
      <span>{children}</span>
    </div>
  );
}
