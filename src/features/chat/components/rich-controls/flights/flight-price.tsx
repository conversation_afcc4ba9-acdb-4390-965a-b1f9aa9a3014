import {
  FlightCardTypes,
  MappedFlightData,
} from "@/features/chat/types/flights";
import { formatPrice } from "@/common/utils/format-price";
import { Panel } from "primereact/panel";
import { Tooltip } from "@/common/components/tooltip";
import { capitalizeFirstLetter } from "@/common/utils/capitalize-first-letter";
import { useMemo } from "react";
import { CircleInfoRegular } from "@/common/components/icons/circle-info-regular";

type FlightPriceProps = {
  price: MappedFlightData["price"];
  type?: FlightCardTypes;
  credits: MappedFlightData["credits"];
  net_price: MappedFlightData["net_price"];
  id: string;
};

export default function FlightPrice({
  price,
  type,
  credits,
  net_price,
  id,
}: FlightPriceProps) {
  const { amount } = price ?? {};

  const totalAmount = parseFloat(amount ?? "0");
  const creditsAmount = parseFloat(credits?.amount ?? "0");
  const netPriceAmount = parseFloat(net_price?.amount ?? "0");

  const priceLabel = useMemo(() => {
    switch (type) {
      case FlightCardTypes.CHANGE:
        return totalAmount >= 0 ? "cost of change" : "refund for change";
      case FlightCardTypes.ONE_WAY:
        return "for one-way";
      case FlightCardTypes.ROUND_TRIP:
        return "for round trip";
      default:
        return "";
    }
  }, [type, totalAmount]);

  const roundUpPrice = (price: number) => Math.ceil(price);

  if (!totalAmount) {
    return null;
  }

  return (
    <>
      <div className="leading-4.5 *:!leading-4.5">
        <span className="text-xs">
          from&nbsp;
          {!!creditsAmount && (
            <span className="line-through pr-1.5">
              ${roundUpPrice(totalAmount)}
            </span>
          )}
        </span>
        <span className="font-bold">
          ${roundUpPrice(creditsAmount ? netPriceAmount : totalAmount)}
        </span>
      </div>
      {!creditsAmount && priceLabel && (
        <div className="text-xs leading-4.5 text-neutral-500">{priceLabel}</div>
      )}
      {!!creditsAmount && (
        <div className="text-xs leading-4.5 text-green-550 flex items-center gap-1">
          after credit applied
          <CircleInfoRegular data-tooltip-id={`credits-info-${id}`} />
          <Tooltip
            id={`credits-info-${id}`}
            className="z-50 !bg-white !p-0 !text-black !text-base !rounded-lg dark:!bg-gray-900 dark:!border dark:!border-gray-600 dark:!text-white"
          >
            <Panel className="min-w-64">
              <div className="font-semibold mb-4.5">
                {capitalizeFirstLetter(priceLabel)}
              </div>
              <div className="flex flex-col gap-y-1">
                <div className="flex items-center justify-between gap-x-1">
                  <span>Total price</span>
                  <span className="font-bold">${formatPrice(totalAmount)}</span>
                </div>
                <div className="flex items-center justify-between gap-x-1 text-green-550">
                  <span>Available credits</span>
                  <span className="font-bold">
                    -${formatPrice(creditsAmount)}
                  </span>
                </div>
                <div className="flex items-center justify-between gap-x-1 border-t border-neutral-250 pt-1">
                  <span>Net price</span>
                  <span className="font-bold">
                    ${formatPrice(netPriceAmount)}
                  </span>
                </div>
              </div>
            </Panel>
          </Tooltip>
        </div>
      )}
    </>
  );
}
