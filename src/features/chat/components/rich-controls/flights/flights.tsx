import { useState } from "react";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import { FlightData } from "@/features/chat/types/flights";
import { refreshFlightsMessage } from "@/features/chat/constants/messages";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import useExpiry from "@/features/chat/hooks/use-expiry";
import arrayHasElements from "@/common/utils/array-has-elements";
import SearchAgain from "@/features/chat/components/rich-controls/search-again";
import FlightCard from "@/features/chat/components/rich-controls/flights/flight-card";
import { DomHand<PERSON> } from "primereact/utils";
import clsx from "clsx";
import { useSilentPromptScroll } from "@/features/chat/hooks/use-scroll-behaviour";

type FlightsProps = {
  disabled?: boolean;
  items: FlightData[];
  timeLimit: string;
  isLastSearch?: boolean;
};

export default function Flights({
  disabled,
  items,
  timeLimit,
  isLastSearch,
}: FlightsProps) {
  const [submittedCard, setSubmittedCard] = useState<string | null>(null);
  const expired = useExpiry(timeLimit);
  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);
  const triggerScroll = useSilentPromptScroll();

  const canSearchAgain = !disabled && expired && isLastSearch;
  const isTouchDevice = DomHandler.isTouchDevice();

  if (!arrayHasElements(items)) {
    return null;
  }

  return (
    <>
      <div className="w-full hide-scrollbar max-w-full overflow-scroll overflow-y-visible">
        <div
          className={clsx("gap-4 mt-4 w-full lg:items-stretch lg:mt-8", {
            flex: isTouchDevice,
            "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3": !isTouchDevice,
          })}
        >
          {items.map((item, index) => (
            <FlightCard
              className="flex-auto"
              data={item}
              id={item.id}
              isDisabled={disabled || !!submittedCard}
              isExpired={expired}
              isSubmitted={submittedCard === item.id}
              key={index}
              onSubmit={() => {
                triggerScroll();
                setSubmittedCard(item.id);
                send({
                  text: item?.action,
                  type: OutgoingMessageTypes.SILENT_PROMPT,
                  extra: item?.action?.toLowerCase().includes("outbound")
                    ? { outbound_flight_id: item.id }
                    : { return_flight_id: item.id },
                });
              }}
            />
          ))}
        </div>
      </div>

      {canSearchAgain && (
        <SearchAgain
          onSubmit={() => {
            triggerScroll();
            send(refreshFlightsMessage);
          }}
        >
          This flight data may not be current. Try running a new search.
        </SearchAgain>
      )}
    </>
  );
}
