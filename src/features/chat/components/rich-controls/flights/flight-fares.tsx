import { Fragment, useState } from "react";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import { FlightFareData } from "@/features/chat/types/flights";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import arrayHasElements from "@/common/utils/array-has-elements";
import FlightFareCard from "@/features/chat/components/rich-controls/flights/flight-fare-card";

type FaresProps = {
  cards?: FlightFareData[];
  flights?: string[];
  isDisabled?: boolean;
};

export default function FlightFares({
  cards,
  isDisabled,
  flights,
}: FaresProps) {
  const [submitted, setSubmitted] = useState<number | undefined>(undefined);
  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);

  const cardSubmitHandler = (action: string, cardIndex: number) => {
    send({
      text: action,
      type: OutgoingMessageTypes.SILENT_PROMPT,
    });
    setSubmitted(cardIndex);
  };

  return (
    <>
      <p>You selected the following flights:</p>
      {arrayHasElements(flights) && (
        <p className="mt-6">
          {flights?.map((flight, index) => (
            <Fragment key={index}>
              {`• ${flight}`}
              <br />
            </Fragment>
          ))}
        </p>
      )}
      <div className="font-medium leading-6 mt-8 text-xl">Choose a fare</div>
      <div className="grid sm:grid-cols-3 max-w-72 mx-auto gap-y-4 mt-4 w-full sm:gap-x-2 sm:max-w-full md:gap-x-4 lg:items-stretch">
        {cards?.map((card, index) => (
          <FlightFareCard
            {...card}
            key={index}
            onSubmit={() => cardSubmitHandler(card.action, index)}
            isDisabled={
              !!isDisabled ||
              (typeof submitted !== "undefined" && submitted !== index)
            }
            isSubmitted={index === submitted}
          />
        ))}
      </div>
    </>
  );
}
