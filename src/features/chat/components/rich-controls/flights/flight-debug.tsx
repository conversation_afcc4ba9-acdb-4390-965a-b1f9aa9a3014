import { FlightData } from "@/features/chat/types/flights";

export function FlightDebug({ flightDetails }: { flightDetails: FlightData }) {
  const {
    id,
    flight_segments,
    seat_selection_policy,
    boarding_policy,
    booking_code,
    total_distance_miles,
    operating_airline_code,
    operating_flight_number,
    source,
  } = flightDetails;

  return (
    <>
      <p className="break-all text-neutral-450 text-xs">
        <span className="font-semibold text-neutral-600">ID:</span> {id}
      </p>
      {flight_segments && flight_segments.length > 0 && (
        <p className="break-all mt-2 text-neutral-450 text-xs">
          <span className="font-semibold text-neutral-600">FLIGHT(S):</span>{" "}
          {flight_segments
            .map((segment) =>
              segment.flight_stops
                .map((stop) => "" + stop.airline_code + stop.flight_number)
                .filter(Boolean)
                .join(", ")
            )
            .filter(Boolean)
            .join(", ")}
        </p>
      )}
      {seat_selection_policy && (
        <p className="break-all mt-2 text-neutral-450 text-xs">
          <span className="font-semibold text-neutral-600">SEAT POLICY: </span>
          {seat_selection_policy}
        </p>
      )}
      {boarding_policy && (
        <p className="break-all mt-2 text-neutral-450 text-xs">
          <span className="font-semibold text-neutral-600">
            BOARDING POLICY:{" "}
          </span>
          {boarding_policy}
        </p>
      )}
      {booking_code && (
        <p className="break-all mt-2 text-neutral-450 text-xs">
          <span className="font-semibold text-neutral-600">BOOKING CODE: </span>
          {booking_code}
        </p>
      )}
      {total_distance_miles && (
        <p className="break-all mt-2 text-neutral-450 text-xs">
          <span className="font-semibold text-neutral-600">
            TOTAL DISTANCE:{" "}
          </span>
          {total_distance_miles} miles
        </p>
      )}
      {operating_airline_code && operating_flight_number && (
        <p className="break-all mt-2 text-neutral-450 text-xs">
          <span className="font-semibold text-neutral-600">OPERATED BY: </span>
          {operating_airline_code}
          {operating_flight_number}
        </p>
      )}
      {source && (
        <p className="break-all mt-2 text-neutral-450 text-xs">
          <span className="font-semibold text-neutral-600">SOURCE: </span>
          {source}
        </p>
      )}
    </>
  );
}
