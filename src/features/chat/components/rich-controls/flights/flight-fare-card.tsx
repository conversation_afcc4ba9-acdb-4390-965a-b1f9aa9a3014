import { useAtomValue } from "jotai";
import { Card } from "primereact/card";
import clsx from "clsx";
import { But<PERSON> } from "primereact/button";
import { FlightFareCard as FlightFareCardType } from "@/features/chat/types/flights";
import { isUserSelectedAtom } from "@/features/admin/store/foreign-user";
import arrayHasElements from "@/common/utils/array-has-elements";
import IconCheck from "@/common/components/icons/check";

export default function FlightFareCard({
  isDisabled,
  isSubmitted,
  onSubmit,
  option_title,
  options,
  price,
}: FlightFareCardType) {
  const isUserSelected = useAtomValue(isUserSelectedAtom);

  return (
    <Card>
      {!!option_title && (
        <div className="font-semibold leading-4.5 mb-4 text-sm">
          {option_title}
        </div>
      )}
      {!!price && (
        <p className="leading-4.5 text-sm">
          <span className="font-bold">${price}</span> total
        </p>
      )}
      <Button
        className={clsx("my-4", {
          "disabled:bg-gray-950 disabled:text-white": isSubmitted,
        })}
        disabled={isDisabled || isSubmitted || isUserSelected}
        onClick={!isUserSelected ? onSubmit : undefined}
        outlined={!isSubmitted}
      >
        {isSubmitted ? (
          <>
            <IconCheck /> Booked
          </>
        ) : (
          "Book it"
        )}
      </Button>
      {arrayHasElements(options) &&
        options?.map((option, index) => (
          <p className="text-neutral-500 text-sm" key={index}>
            {option}
          </p>
        ))}
    </Card>
  );
}
