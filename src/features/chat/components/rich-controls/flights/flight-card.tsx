import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react";
import Image from "next/image";
import { useAtom, useAtomValue } from "jotai";
import clsx from "clsx";
import { Card } from "primereact/card";
import { <PERSON><PERSON> } from "primereact/button";
import { Chip } from "primereact/chip";
import RightSidebars from "@/features/chat/types/right-sidebars";
import { FlightCard as FlightCardType } from "@/features/chat/types/flights";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { flightCardsDebugAtom } from "@/features/chat/store/debugging";
import { isUserSelectedAtom } from "@/features/admin/store/foreign-user";
import { flightDetailsAtom } from "@/features/chat/store/sidebar";
import arrayHasElements from "@/common/utils/array-has-elements";
import objectHasEntries from "@/common/utils/object-has-entries";
import { mapFlightCardData } from "@/features/chat/utils/map-flight-data";
import RecommendationPanel from "@/features/chat/components/recommendation-reasons/recommendation-panel";
import IconCheck from "@/common/components/icons/check";
import { capitalizeFirstLetter } from "@/common/utils/capitalize-first-letter";
import WithinPolicy from "../../within-policy";
import FlightPrice from "./flight-price";
import { FlightDebug } from "./flight-debug";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import FlightRoute from "./flight-route";

export default function FlightCard({
  className,
  data,
  id,
  isDisabled,
  isExpired,
  isSubmitted,
  onSubmit,
}: FlightCardType) {
  const isUserSelected = useAtomValue(isUserSelectedAtom);
  const flightCardsDebug = useAtomValue(flightCardsDebugAtom);
  const { rightSidebar, switchTo } = useRightSidebarSwitch();
  const [selectedFlight, setSelectedFlight] = useAtom(flightDetailsAtom);

  if (!objectHasEntries(data)) {
    return null;
  }

  const flightDetails = mapFlightCardData(data);
  const {
    airlineImg,
    airlineName,
    recommendationReasons,
    keyword,
    within_policy,
    within_or_out_policy_reason,
    cabin,
    cancellation_policy,
    exchange_policy,
    price,
    type,
    credits,
    net_price,
    is_red_eye,
    flightNumbers,
    duration,
    arrivalDays,
    departure,
    arrival,
    middleStops,
    stops,
  } = flightDetails;

  const submitHandler: MouseEventHandler<HTMLButtonElement> = (event) => {
    event.stopPropagation();

    if (!isDisabled && !isExpired) {
      onSubmit();

      if (!!selectedFlight) {
        setSelectedFlight({
          ...selectedFlight,
          isSubmitted: selectedFlight.id === id,
          isDisabled: true,
        });
      }
    }
  };

  return (
    <Card
      className={clsx(
        "relative cursor-pointer hover:border hover:!border-primary-500 w-full min-w-72 sm:min-w-fit",
        {
          "border border-primary-500":
            selectedFlight?.id === id &&
            rightSidebar === RightSidebars.FLIGHT_DETAILS,
        },
        className
      )}
      pt={{
        body: { className: "h-full" },
        content: { className: "flex flex-col gap-y-4 h-full" },
      }}
      onClick={() => {
        setSelectedFlight({
          ...flightDetails,
          flightLeg: "Outbound flight",
          flightStops: arrayHasElements(data?.flight_segments)
            ? data.flight_segments[0]?.flight_stops
            : [],
          isDisabled,
          isExpired,
          isSubmitted,
          onSubmit,
        });
        switchTo(RightSidebars.FLIGHT_DETAILS);
      }}
      data-testid="flight-card"
    >
      {flightCardsDebug && <FlightDebug flightDetails={data} />}

      <div className="flex gap-x-1.5 items-center">
        {airlineImg?.src && (
          <Image
            alt={airlineImg.alt ?? "Airline logo"}
            height={24}
            src={airlineImg.src}
            width={24}
          />
        )}
        {airlineName && (
          <span className="leading-4.5 text-sm">{airlineName}</span>
        )}
        {!!keyword && (
          <Chip
            label={capitalizeFirstLetter(keyword)}
            className="ml-auto py-0.5 px-2 shrink-0"
            pt={{ label: { className: "text-[0.688rem] leading-5" } }}
          />
        )}
      </div>

      <div className="flex flex-col gap-y-2">
        <div className="flex justify-between text-xs">
          <span>
            {flightNumbers}
            {is_red_eye && (
              <FontAwesomeIcon
                icon="eye"
                className="text-red-700 ml-1"
                width={16}
                height={12}
              />
            )}
          </span>

          <span>
            {duration}
            {arrivalDays && (
              <span className="text-red-650 text-2xs">
                {` + ${arrivalDays}`}
              </span>
            )}
          </span>
        </div>

        <FlightRoute
          departure={departure}
          arrival={arrival}
          middleStops={middleStops}
        />
      </div>

      <div className="hidden md:block lg:hidden text-sm text-neutral-500">
        {stops}
      </div>

      <div className="flex flex-col gap-y-1 mt-auto">
        {!!cabin && (
          <p className="text-sm">Cabin: {capitalizeFirstLetter(cabin)}</p>
        )}
        {!!cancellation_policy && (
          <p className="text-sm">{cancellation_policy}</p>
        )}
        {!!exchange_policy && <p className="text-sm">{exchange_policy}</p>}
      </div>

      <div className="flex flex-col gap-y-2 [&_button]:leading-5 flex-1 justify-end">
        <WithinPolicy
          withinPolicy={within_policy ?? null}
          reason={within_or_out_policy_reason}
        />
        <RecommendationPanel reasons={recommendationReasons} />
      </div>

      {!isExpired && (
        <div className="flex gap-x-4 items-center">
          <Button
            className={clsx("w-fit", {
              "disabled:bg-gray-950 disabled:text-white": isSubmitted,
            })}
            disabled={isDisabled || isExpired || isUserSelected}
            onClick={!isUserSelected ? submitHandler : undefined}
            outlined={!isSubmitted}
            data-testid="select-button"
          >
            {isSubmitted ? (
              <>
                <IconCheck />
                Selected
              </>
            ) : (
              "Select"
            )}
          </Button>
          {!!price && (
            <div className="text-sm">
              <FlightPrice
                price={price}
                type={type}
                credits={credits}
                net_price={net_price}
                id={id}
              />
            </div>
          )}
        </div>
      )}
    </Card>
  );
}
