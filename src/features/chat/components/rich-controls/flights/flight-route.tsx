import arrayHasElements from "@/common/utils/array-has-elements";
import { MappedFlightData } from "@/features/chat/types/flights";

export default function FlightRoute({
  departure,
  arrival,
  middleStops,
}: Pick<MappedFlightData, "departure" | "arrival" | "middleStops">) {
  return (
    <div className="flex items-center justify-between gap-x-3 w-full">
      <div className="flex flex-col min-w-[60px] text-base leading-5 gap-y-2">
        <span className="font-semibold">{departure.time}</span>
        {departure.airport.code}
      </div>

      <div className="relative w-full flex-1 text-xs">
        <div className="absolute left-0 right-0 h-px bg-neutral-250 z-0" />
        <div className="flex md:hidden lg:flex w-full justify-around items-center z-10 relative -top-px">
          {!arrayHasElements(middleStops) && (
            <span className="text-primary-700 mt-1 whitespace-nowrap">
              nonstop
            </span>
          )}
          {middleStops?.map((stop, idx) => (
            <div key={idx} className="flex flex-col items-center">
              <div className="size-[0.259rem] rounded-full bg-neutral-250" />
              <span className="text-primary-700 mt-1 whitespace-nowrap">
                {stop.origin_code}
              </span>
            </div>
          ))}
        </div>
      </div>

      <div className="flex flex-col min-w-[60px] text-base leading-5 items-end gap-y-2">
        <span className="font-semibold">{arrival.time}</span>
        {arrival.airport.code}
      </div>
    </div>
  );
}
