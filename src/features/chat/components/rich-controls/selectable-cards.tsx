"use client";

import React, { useEffect, useMemo, useState } from "react";
import { useAtomValue } from "jotai";
import clsx from "clsx";
import { Button } from "primereact/button";
import {
  OutgoingMessage,
  OutgoingMessageTypes,
} from "@/features/chat/types/messages";
import useSocketConnection from "@/features/chat/hooks/use-web-socket";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import arrayHasElements from "@/common/utils/array-has-elements";
import IconCheck from "@/common/components/icons/check";
import { SelectableCard } from "./selectable-card";

interface SelectableCardsProps {
  items: {
    title?: string;
    description?: string;
    action: string;
    selected?: boolean;
  }[];
  submitText?: string;
  emptyMessage: OutgoingMessage;
  submitted?: boolean;
}

export function SelectableCards({
  items,
  submitText = "I'm done",
  emptyMessage,
  submitted,
}: SelectableCardsProps) {
  const foreignUser = useAtomValue(foreignUserAtom);
  const { send } = useSocketConnection(
    process.env.NEXT_PUBLIC_WS_URL as string
  );
  const [selectedItems, setSelectedItems] = useState<
    Record<number, string | undefined>
  >({});
  const [hasSubmitted, setHasSubmitted] = useState(submitted);

  const submitMessage = useMemo(() => {
    const filteredItems = Object.values(selectedItems).filter(
      (item): item is string => !!item
    );

    if (!arrayHasElements(filteredItems)) {
      return emptyMessage;
    }

    return {
      type: OutgoingMessageTypes.SILENT_PROMPT,
      text: filteredItems.join(" "),
    };
  }, [emptyMessage, selectedItems]);

  useEffect(() => {
    const newSelectedItems = items.reduce<Record<number, string>>(
      (acc, { action, selected }, index) => {
        if (selected) {
          acc[index] = action;
        }
        return acc;
      },
      {}
    );

    setSelectedItems(newSelectedItems);
  }, [items]);

  return (
    <div className="bg-gray-100 mt-6 p-4 rounded-lg w-full dark:bg-white/5">
      <div className="gap-4 grid sm:grid-cols-2 md:grid-cols-3 items-stretch">
        {items.map(({ title, description, action }, index) => (
          <SelectableCard
            key={index}
            title={title}
            description={description}
            isSelected={!!selectedItems[index]}
            disabled={hasSubmitted}
            onClick={() => {
              setSelectedItems((prev) => ({
                ...prev,
                [index]: !!prev[index] ? undefined : action,
              }));
              setHasSubmitted(false);
            }}
          />
        ))}
      </div>
      <Button
        className={clsx("mt-4 w-full sm:w-max", {
          "disabled:bg-gray-950 disabled:text-white": hasSubmitted,
        })}
        onClick={
          !foreignUser
            ? () => {
                send(submitMessage);
                setHasSubmitted(true);
              }
            : undefined
        }
        disabled={hasSubmitted || !!foreignUser}
        outlined={!hasSubmitted}
      >
        {hasSubmitted && <IconCheck />}
        {submitText}
      </Button>
    </div>
  );
}
