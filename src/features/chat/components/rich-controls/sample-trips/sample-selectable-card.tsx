import { useAtomValue } from "jotai";
import clsx from "clsx";
import { Card } from "primereact/card";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import { WithEllipsis } from "@/common/components/with-ellipsis";

interface SampleSelectableCardProps {
  isSelected?: boolean;
  onClick?: VoidFunction;
  title?: string;
  description?: string;
  disabled?: boolean;
  isSticky?: boolean;
}

export function SampleSelectableCard({
  isSelected,
  onClick,
  title,
  description,
  disabled,
  isSticky,
}: SampleSelectableCardProps) {
  const foreignUser = useAtomValue(foreignUserAtom);
  const canSelect = !foreignUser && !disabled;

  return (
    <Card
      className={clsx("relative transition-all p-4 duration-300 text-sm", {
        "border border-primary-500": isSelected,
        "hover:cursor-pointer": canSelect,
        "min-w-40": isSticky,
        "h-[138px] md:h-[120px] lg:h-[120px]": !isSticky,
      })}
      onClick={canSelect ? onClick : undefined}
      data-testid="sample-trip-card"
    >
      <div className="flex flex-col h-full">
        <h4
          className={clsx("font-semibold leading-5 overflow-hidden", {
            "line-clamp-1": isSticky,
            "h-[63px]": !isSticky,
          })}
          title={title}
        >
          {title}
        </h4>
        {description && (
          <div
            className={clsx(
              "sample-card-description transition-all duration-300 flex-1",
              {
                "max-h-0 leading-none": isSticky,
                "flex-grow": !isSticky,
              }
            )}
          >
            <p
              className="line-clamp-2 leading-5 whitespace-normal overflow-hidden"
              title={description}
            >
              {description}
            </p>
          </div>
        )}
      </div>
    </Card>
  );
}
