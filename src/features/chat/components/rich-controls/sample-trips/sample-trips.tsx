import React, { useCallback, useState, useRef, useEffect } from "react";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import clsx from "clsx";
import { twMerge } from "tailwind-merge";

import { TripSamples } from "@/features/chat/types/trip";
import useThrottle from "@/common/hooks/use-throttle";
import { useUserProfile } from "@/features/user/hooks/api";
import { hideHeaderAtom } from "@/features/chat/store/chat";
import {
  chatRefAtom,
  sampleTripsRefAtom,
} from "@/features/chat/store/components";
import SampleTripsCards from "./sample-trips-cards";
import ControlButton from "./control-button";

type SampleTripsProps = {
  samples?: TripSamples;
};

export default function SampleTrips({ samples }: SampleTripsProps) {
  const { sampleTrips } = useUserProfile();

  const hideHeader = useAtomValue(hideHeader<PERSON>tom);
  const chatRef = useAtomValue(chatRef<PERSON>tom);
  const setSampleTripsRef = useSetAtom(sampleTripsRefAtom);

  const wrapperRef = useRef<HTMLDivElement>(null);
  const stickyWrapperRef = useRef<HTMLDivElement>(null);

  const [isSticky, setIsSticky] = useState(false);
  const [shouldStick, setShouldStick] = useState(true);
  const [stickyClassName, setStickyClassName] = useState<string>("");

  useEffect(() => {
    if (
      sampleTrips.shouldShowDontShowAgain &&
      !sampleTrips.shouldShowSampleTrips
    ) {
      setShouldStick(false);
    }
  }, [sampleTrips]);

  const scrollCallback = useCallback(() => {
    const wrapperElement = wrapperRef?.current;
    const stickyWrapperElement = stickyWrapperRef?.current;

    if (wrapperElement && stickyWrapperElement) {
      const wrapperBottom = wrapperElement.getBoundingClientRect().bottom;
      const stickyWrapperBottom =
        stickyWrapperElement.getBoundingClientRect().bottom;

      setIsSticky(wrapperBottom < stickyWrapperBottom + 10);
    }
  }, []);

  const throttledScrollCallback = useThrottle(scrollCallback, 80);

  useEffect(() => {
    const messagesWrapper = chatRef?.current;

    setTimeout(
      () => setStickyClassName("opacity-100 pointer-events-auto"),
      500
    );

    if (!!messagesWrapper) {
      messagesWrapper.addEventListener("scroll", throttledScrollCallback);
      messagesWrapper.addEventListener("scrollend", throttledScrollCallback);

      return () => {
        messagesWrapper?.removeEventListener("scroll", throttledScrollCallback);
        messagesWrapper?.removeEventListener(
          "scrollend",
          throttledScrollCallback
        );
      };
    }
  }, [chatRef, throttledScrollCallback]);

  useEffect(() => setSampleTripsRef(stickyWrapperRef), [setSampleTripsRef]);

  if (!samples?.trips) {
    return null;
  }

  const wrapperClassName = clsx(
    "bg-gray-100 border border-gray-300 pt-2 pb-4 rounded-lg",
    "dark:bg-neutral-700 dark:border-neutral-600",
    { "opacity-0": isSticky && shouldStick }
  );

  return (
    <>
      <div className={clsx("mt-6 w-full", wrapperClassName)} ref={wrapperRef}>
        <div className={clsx("flex justify-between px-4 mb-2 items-center")}>
          <h3 className="font-medium">Sample trips</h3>
          <ControlButton
            sampleTrips={sampleTrips}
            setIsSticky={(value) => setIsSticky(value)}
            setShouldStick={(value) => setShouldStick(value)}
            shouldStick={!!shouldStick}
          />
        </div>
        <SampleTripsCards samples={samples} />
      </div>
      <div
        className={twMerge(
          wrapperClassName,
          "duration-300 fixed max-w-218 opacity-0 pointer-events-none transition-all w-[calc(100%-2rem)] z-20",
          hideHeader ? "top-2" : "top-18",
          "md:w-[calc(100%-4rem)]",
          "lg:top-18",
          isSticky && shouldStick ? stickyClassName : ""
        )}
        ref={stickyWrapperRef}
      >
        <div className={clsx("flex justify-between px-4 mb-2 items-center")}>
          <h3 className="font-medium">Sample trips</h3>
          <ControlButton
            isSticky
            sampleTrips={sampleTrips}
            setIsSticky={(value) => setIsSticky(value)}
            setShouldStick={(value) => setShouldStick(value)}
            shouldStick={shouldStick}
          />
        </div>
        <SampleTripsCards
          className="sample-cards-animation"
          samples={samples}
          isSticky
        />
      </div>
    </>
  );
}
