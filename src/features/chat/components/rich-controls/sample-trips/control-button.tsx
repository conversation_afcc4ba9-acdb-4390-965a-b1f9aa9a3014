"use client";

import { But<PERSON> } from "primereact/button";
import {
  faThumbTack,
  faThumbTackSlash,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import clsx from "clsx";

import { useUserProfileMarkSampleTripsAsHidden } from "@/features/user/hooks/api";

type ControlButtonProps = {
  isSticky?: boolean;
  sampleTrips: {
    shouldShowSampleTrips: boolean | undefined;
    shouldShowDontShowAgain: boolean | undefined;
  };
  setIsSticky: (value: boolean) => void;
  setShouldStick: (value: boolean) => void;
  shouldStick: boolean;
};

export default function ControlButton({
  isSticky,
  sampleTrips,
  setIsSticky,
  setShouldStick,
  shouldStick,
}: ControlButtonProps) {
  const { markSampleTripsAsHidden, isHiding } =
    useUserProfileMarkSampleTripsAsHidden();
  const { shouldShowDontShowAgain, shouldShowSampleTrips } = sampleTrips;

  return shouldShowDontShowAgain ? (
    <Button
      className={clsx({
        "gap-x-1 pointer-events-none text-green-600": !shouldShowSampleTrips,
      })}
      disabled={isHiding}
      onClick={() => {
        setIsSticky(false);
        setShouldStick(false);
        markSampleTripsAsHidden();
      }}
      link
    >
      {shouldShowSampleTrips ? (
        "Don't show again"
      ) : (
        <>
          <FontAwesomeIcon icon="check" width={12} height={16} />
          Got it
        </>
      )}
    </Button>
  ) : (
    <Button
      className={clsx("p-0 size-4.5 text-gray-300 hover:text-gray-500", {
        "text-gray-500": isSticky,
      })}
      onClick={() => setShouldStick(!shouldStick)}
      plain
    >
      <FontAwesomeIcon icon={shouldStick ? faThumbTack : faThumbTackSlash} />
    </Button>
  );
}
