"use client";

import { useCallback, useState } from "react";
import { useAtomValue } from "jotai";
import { twMerge } from "tailwind-merge";
import clsx from "clsx";

import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import { TripSamples } from "@/features/chat/types/trip";
import { currentTripAtom } from "@/features/chat/store/current-trip";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import { SampleSelectableCard } from "./sample-selectable-card";

type SampleTripsCardsProps = {
  className?: string;
  isSticky?: boolean;
  samples: TripSamples;
};

export default function SampleTripsCards({
  className,
  isSticky,
  samples,
}: SampleTripsCardsProps) {
  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);
  const currentTrip = useAtomValue(currentTripAtom);
  const [selectedTrips, setSelectedTrips] = useState<Record<number, boolean>>(
    {}
  );

  const handleSelectTrip = useCallback(
    (prompt: string, index: number) => {
      if (currentTrip) {
        send({
          type: OutgoingMessageTypes.PROMPT,
          text: prompt,
          extra: {
            isSampleTrip: true,
          },
        });

        setSelectedTrips((prev) => ({
          ...prev,
          [index]: !prev[index],
        }));
      }
    },
    [send, currentTrip]
  );

  return (
    <div
      className={twMerge(
        clsx(
          "sample-trip-card transition-all px-4 duration-300 overflow-y-hidden md:grid-cols-4",
          {
            "gap-2 md:gap-4 flex md:grid w-full hide-scrollbar max-w-full overflow-scroll":
              isSticky,
            "grid gap-4 grid-cols-2": !isSticky,
          }
        ),
        className
      )}
    >
      {samples.trips.map(({ title, description, prompt }, index) => (
        <SampleSelectableCard
          key={index}
          title={title}
          description={description}
          onClick={() => {
            handleSelectTrip(prompt, index);
          }}
          isSelected={!!selectedTrips[index]}
          disabled={!!selectedTrips[index]}
          isSticky={isSticky}
        />
      ))}
    </div>
  );
}
