import { useAtomValue } from "jotai";
import clsx from "clsx";
import { Card } from "primereact/card";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import IconCheckSolid from "@/common/components/icons/check-solid";

interface SelectableCardProps {
  isSelected?: boolean;
  onClick?: VoidFunction;
  title?: string;
  description?: string;
  disabled?: boolean;
}

export function SelectableCard({
  isSelected,
  onClick,
  title,
  description,
  disabled,
}: SelectableCardProps) {
  const foreignUser = useAtomValue(foreignUserAtom);
  const canSelect = !foreignUser && !disabled;

  return (
    <Card
      className={clsx("border-box relative h-full", {
        "border border-primary-500": isSelected,
        "hover:cursor-pointer": canSelect,
      })}
      onClick={canSelect ? onClick : undefined}
    >
      {isSelected && <IconCheckSolid className="absolute top-4 right-4" />}
      <h4
        className="font-semibold leading-6 overflow-hidden text-base text-ellipsis whitespace-nowrap"
        title={title}
      >
        {title}
      </h4>
      {description && <p className="leading-6 text-base">{description}</p>}
    </Card>
  );
}
