import { useMemo } from "react";
import { noPreferencesSelected } from "../../constants/messages";
import { SuggestedPreferences as SuggestedPreferencesType } from "../../types/suggested-preferences";
import { SelectableCards } from "./selectable-cards";

export function SuggestedPreferences({
  suggested_preferences,
}: {
  suggested_preferences: SuggestedPreferencesType;
}) {
  const items = useMemo(
    () =>
      suggested_preferences.options.map(
        ({ title, detail, action, selected }) => ({
          title,
          description: detail,
          action,
          selected,
        })
      ),
    [suggested_preferences]
  );

  return (
    <SelectableCards
      items={items}
      emptyMessage={noPreferencesSelected}
      submitText={
        suggested_preferences.submitted ? "Added" : "Add to preferences"
      }
      submitted={suggested_preferences.submitted}
    />
  );
}
