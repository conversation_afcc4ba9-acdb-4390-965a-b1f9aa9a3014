"use client";

import { ReactNode } from "react";
import { useAtomValue } from "jotai";
import clsx from "clsx";
import { OutgoingMessage } from "@/features/chat/types/messages";
import { DivProps } from "@/common/types/html-elements-props";
import useSecondaryEffects from "./secondary-effects";
import {
  chatH<PERSON><PERSON><PERSON><PERSON>,
  hide<PERSON><PERSON>er<PERSON>tom,
  ottoReplyingAtom,
} from "@/features/chat/store/chat";
import { spotnanaSearchFlagsAtom } from "@/features/chat/store/debugging";
import { currentTripAtom } from "@/features/chat/store/current-trip";
import { foreignUserAtom } from "@/features/admin/store/foreign-user";
import ChatHistory from "@/features/chat/components/chat-history";
import HotelDetails from "@/features/chat/components/right-sidebar/hotel-details";
import HotelRoomDetails from "@/features/chat/components/right-sidebar/hotel-room-details";
import useMainWebSocketConnection from "./web-socket-connection";
import WSConnectionStatus from "@/common/components/ws-connection-status";
import arrayHasElements from "@/common/utils/array-has-elements";
import dynamic from "next/dynamic";
import ScrollButton from "@/features/chat/components/scroll-btn";
import { useDetectKeyboardOpen } from "@/common/hooks/use-detect-keyboard-open";
import { isNativeBridgeAtom } from "@/common/store/nativebridge";
import FlightDetailsSidebar from "../right-sidebar/flight-details/flight-details-sidebar";
import SuggestedCapabilityBanner from "../suggested-capability-banner";

const ChatInput = dynamic(
  () => import("@/features/chat/components/chat-input"),
  { ssr: false }
);

type ChatProps = DivProps & {
  emptyMessage?: ReactNode;
  header?: ReactNode;
  initialMessage?: OutgoingMessage;
  readonly?: boolean;
  sidebarContent?: ReactNode;
  tryingModeEnabled?: boolean;
};

export default function Chat({
  className,
  emptyMessage,
  header,
  initialMessage,
  readonly,
  sidebarContent,
  tryingModeEnabled,
  ...restProps
}: ChatProps) {
  const isOttoReplying = useAtomValue(ottoReplyingAtom);
  const isNativeBridge = useAtomValue(isNativeBridgeAtom);
  const currentTrip = useAtomValue(currentTripAtom);
  const spotnanaSearchFlags = useAtomValue(spotnanaSearchFlagsAtom);
  const enableSpotnanaSearch = spotnanaSearchFlags?.[currentTrip || 0] || false;
  const { send } = useMainWebSocketConnection(
    initialMessage
      ? {
          ...initialMessage,
          extra: { tryingModeEnabled, enableSpotnanaSearch },
        }
      : initialMessage
  );
  const messagesWrapperRef = useSecondaryEffects();

  const foreignUser = useAtomValue(foreignUserAtom);
  const history = useAtomValue(chatHistoryAtom);
  const hideHeader = useAtomValue(hideHeaderAtom);
  const isKeyboardOpen = useDetectKeyboardOpen();

  return (
    <>
      <div
        {...restProps}
        className={clsx(className, "flex-auto h-full w-full lg:w-2/3 z-1", {
          "pb-6.5": isNativeBridge && !isKeyboardOpen,
        })}
      >
        <div className="flex flex-col items-stretch relative h-full">
          <div
            className={clsx(
              "max-lg:absolute max-lg:top-0 max-lg:left-0 max-lg:w-full max-lg:z-20 max-lg:transition-transform",
              { "max-lg:-translate-y-full": hideHeader }
            )}
          >
            {header}
          </div>
          <SuggestedCapabilityBanner />
          <div
            className="overflow-y-auto relative [scrollbar-width:none] h-full flex-1 peer"
            ref={messagesWrapperRef}
          >
            {emptyMessage && !arrayHasElements(history) && !isOttoReplying && (
              <p className="relative text-center top-1/2 px-2">
                {emptyMessage}
              </p>
            )}
            <ChatHistory />
          </div>
          <ScrollButton />
          {!foreignUser && !readonly && <ChatInput send={send} />}
        </div>
        <WSConnectionStatus />
      </div>
      <HotelDetails />
      <FlightDetailsSidebar />
      <HotelRoomDetails />
    </>
  );
}
