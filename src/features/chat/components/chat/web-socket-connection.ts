import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { use<PERSON><PERSON>, useAtomValue, useSet<PERSON><PERSON> } from "jotai";
import * as nativebridge from "@nrk/nativebridge";
import { ReadyState } from "react-use-websocket";
import {
  IncomingMessage,
  IncomingMessageStatus,
  IncomingMessageTypes,
  OutgoingMessage,
} from "@/features/chat/types/messages";
import { WSReconnectState } from "@/features/chat/types/web-socket";
import { OnboardingChecklistItems } from "@/common/types/onboarding-checklist";
import { OutgoingJSBridgeEvents } from "@/common/types/js-bridge-events";
import { ProfileForms } from "@/features/user/types/profile-forms";
import { shouldAttemptReconnectCodes } from "@/features/chat/constants/web-socket";
import { ROUTES } from "@/common/constants";
import { onboardingEndMessage } from "@/features/chat/constants/messages";
import { useCurrentRoute } from "@/common/hooks/use-current-route";
import useSocketConnection from "@/features/chat/hooks/use-web-socket";
import {
  useCreateTrip,
  useGetTripDetails,
  useTripsList,
} from "@/common/hooks/api";
import { useUserProfile } from "@/features/user/hooks/api";
import useToast from "@/common/hooks/use-toast";
import useProfileFormToggle from "@/features/user/hooks/use-profile-form-switch";
import {
  currentTripAtom,
  threadTimestampAtom,
} from "@/features/chat/store/current-trip";
import { travelContextAtom } from "@/features/chat/store/debugging";
import {
  chatHistoryAtom,
  connectWebsocketAtom,
  ottoReplyingAtom,
  reconnectAttemptAtom,
  showSkeletonAtom,
  skeletonTextAtom,
  stopDisabledAtom,
  suggestedCapabilityAtom,
  calendarIntegrationModalAtom,
  chatHistoryReceivedAtom,
  openingMessageAtom,
} from "@/features/chat/store/chat";
import { onboardingChecklistAtom } from "@/common/store/sidebar";
import { nativeWSDebugAtom } from "@/common/store/nativebridge";
import formatErrorMessage from "@/features/chat/utils/format-error-message";
import { useAccess } from "@/common/hooks/use-access";
import arrayHasElements from "@/common/utils/array-has-elements";
import useShouldIgnoreMessage from "../../hooks/use-should-ignore-message";
import {
  unbookedFlightAtom,
  unbookedHotelAtom,
} from "@/features/itineraries/store/unbooked";
import { CapabilityPromptsTypes } from "../../types/capability-prompts";

export default function useMainWebSocketConnection(
  initialMessage?: OutgoingMessage
) {
  const router = useRouter();
  const currentRoute = useCurrentRoute();
  const { showErrorToast } = useToast();
  const { mutateUserProfile } = useUserProfile();
  const { mutate: mutateTripsList } = useTripsList();
  const { getTripDetails } = useGetTripDetails();
  const setIsOttoReplying = useSetAtom(ottoReplyingAtom);
  const setSelectedFlight = useSetAtom(unbookedFlightAtom);
  const setSelectedHotel = useSetAtom(unbookedHotelAtom);
  const setShowSkeleton = useSetAtom(showSkeletonAtom);
  const setSkeletonText = useSetAtom(skeletonTextAtom);
  const setTravelContext = useSetAtom(travelContextAtom);
  const setStopDisabled = useSetAtom(stopDisabledAtom);
  const setThreadTimestamp = useSetAtom(threadTimestampAtom);
  const setSuggestedCapability = useSetAtom(suggestedCapabilityAtom);
  const setCalendarIntegrationModal = useSetAtom(calendarIntegrationModalAtom);
  const nativeWSDebug = useAtomValue(nativeWSDebugAtom);
  const [_, setOpeningMessage] = useAtom(openingMessageAtom);
  const [onboardingChecklist, setOnboardingChecklist] = useAtom(
    onboardingChecklistAtom
  );
  const [messagesList, setMessagesList] = useAtom(chatHistoryAtom);
  const [chatHistoryReceived, setChatHistoryReceived] = useAtom(
    chatHistoryReceivedAtom
  );
  const [reconnectAttempt, setReconnectAttempt] = useAtom(reconnectAttemptAtom);
  const currentTrip = useAtomValue(currentTripAtom);
  const setConnectWebsocket = useSetAtom(connectWebsocketAtom);

  const [hasInitialized, setHasInitialized] = useState(false);

  const { requestGoogleCalendarAccess } = useAccess();

  const { createTrip } = useCreateTrip();
  const { openProfileForm } = useProfileFormToggle();

  const shouldIgnoreMessage = useShouldIgnoreMessage();

  const { send, sendInitMessage, sendSilentInitMessage, readyState } =
    useSocketConnection(process.env.NEXT_PUBLIC_WS_URL as string, {
      onClose: (event) => {
        console.debug("onClose", event, { reconnectAttempt });
        const shouldReconnect = shouldAttemptReconnectCodes.includes(
          event.code
        );
        setShowSkeleton(null);
        if (
          shouldReconnect &&
          reconnectAttempt === WSReconnectState.SHOULD_ATTEMPT
        ) {
          setReconnectAttempt(WSReconnectState.ATTEMPTING);
        } else if (!shouldReconnect && event.code > 1000) {
          showErrorToast({
            detail: "Websocket connection closed.",
            status: event.code,
          });
        }
      },

      onError: (e) => {
        console.debug("onError", e);

        if (
          reconnectAttempt !== WSReconnectState.ATTEMPTING &&
          reconnectAttempt !== WSReconnectState.SHOULD_ATTEMPT
        ) {
          showErrorToast({}, "Websocket connection failed.");
        }
      },

      onMessage: async (event) => {
        let message: IncomingMessage = JSON.parse(event.data);
        const {
          status,
          thread_id,
          selected_accomodation,
          selected_flight_itinerary,
          type,
        } = message ?? {};

        if (shouldIgnoreMessage(thread_id)) {
          return;
        }

        if (status === IncomingMessageStatus.ERROR) {
          message = formatErrorMessage(message);
        }

        if (
          type === IncomingMessageTypes.PROMPT &&
          (arrayHasElements(message.flights) ||
            arrayHasElements(message.accommodations) ||
            Array.isArray(message.flights) ||
            Array.isArray(message.accommodations))
        ) {
          setShowSkeleton(null);
        }

        if (
          type === IncomingMessageTypes.SUGGESTED_CAPABILITY &&
          !!message?.capability_type
        ) {
          const text =
            typeof message.text === "string"
              ? message.text
              : Array.isArray(message.text)
              ? message.text.join(" ")
              : "";
          if (
            message.capability_type ===
            CapabilityPromptsTypes.CALENDAR_INTEGRATION
          ) {
            setCalendarIntegrationModal(true);
          } else {
            setSuggestedCapability({
              capability_type:
                message.capability_type as CapabilityPromptsTypes,
              text,
            });
          }
          return; // Don't add to message history
        }

        // if (type === IncomingMessageTypes.REDIRECT_TO_TRIP && !!message?.newTripId) {
        //   return; // Don't add to message history
        // }

        if (currentRoute === ROUTES.PREFERENCES) {
          mutateUserProfile();
        }

        if (!!selected_flight_itinerary) {
          setSelectedFlight(selected_flight_itinerary);
        }

        if (!!selected_accomodation) {
          setSelectedHotel(selected_accomodation);
        }

        setStopDisabled(false);

        try {
          !!nativeWSDebug &&
            nativebridge.emit(OutgoingJSBridgeEvents.WEB_SOCKET_DEBUG, message);
        } catch (e) {}

        setMessagesList((prevMessages) => {
          const reinitializedHistory =
            chatHistoryReceived && type === IncomingMessageTypes.HISTORY;

          if (arrayHasElements(prevMessages) && !reinitializedHistory) {
            const { isBotMessage, partial } = prevMessages.at(
              -1
            ) as IncomingMessage;
            return [
              ...(isBotMessage && partial
                ? prevMessages.slice(0, -1)
                : prevMessages),
              message,
            ];
          } else {
            return [message];
          }
        });
      },

      onExpectResponse: () => setIsOttoReplying(false),

      onErrorMessage: () => setIsOttoReplying(false),

      onHistoryMessage: async (message) => {
        const {
          messages,
          thread_id,
          lastTimestamp,
          minMessageTimestampSeconds,
        } = message ?? {};

        if (shouldIgnoreMessage(thread_id)) {
          return;
        }

        if (lastTimestamp && minMessageTimestampSeconds) {
          setThreadTimestamp({
            lastTimestamp,
            threshold: minMessageTimestampSeconds,
          });
        }

        const length = messages?.length;
        const lastMessage =
          !!length && length > 0 ? messages[length - 1] : undefined;

        if (!!lastMessage && !lastMessage.expectResponse) {
          setIsOttoReplying(true);
        } else {
          setIsOttoReplying(false);
        }
        setChatHistoryReceived(true);
      },

      onItineraryUpdate: () => !!currentTrip && getTripDetails(currentTrip),

      onOpen: () => {
        console.debug("onOpen");
        if (hasInitialized && initialMessage) {
          sendSilentInitMessage(initialMessage);
        } else if (initialMessage) {
          sendInitMessage(initialMessage);
          setHasInitialized(true);
          setOpeningMessage(null);
        }
        setReconnectAttempt(WSReconnectState.SHOULD_ATTEMPT);
      },

      onPreferencesComplete: () => {
        setOnboardingChecklist({
          ...onboardingChecklist,
          [OnboardingChecklistItems.TRAVEL_PREFERENCES]: true,
        });
      },

      onOnboardingComplete: () => {
        mutateUserProfile();
        setIsOttoReplying(true);
        setMessagesList([...messagesList, onboardingEndMessage]);
        setTimeout(() => createTrip(), 5000);
      },

      onProfileUpdate: () => mutateUserProfile(),

      onReconnectStop: () => {
        if (reconnectAttempt === WSReconnectState.ATTEMPTING) {
          setReconnectAttempt(WSReconnectState.FAILED);
          setConnectWebsocket(false);
        }
      },
      onRefreshMessage: () => location.reload(),

      onSkeleton: (skeleton, text) => {
        setShowSkeleton(skeleton);
        setIsOttoReplying(false);
        if (!!text) {
          setSkeletonText(text);
        }
      },

      onTravelContext: (context) => setTravelContext(context),

      onTripsUpdate: () => mutateTripsList(),

      onOpenPaymentForm: (
        formType,
        { frequentFlyerIATACodes, formMessage }
      ) => {
        setIsOttoReplying(false);
        const { doneMessage, closeMessage } = formMessage ?? {};
        switch (formType) {
          case IncomingMessageTypes.OPEN_FLIGHTS_PAYMENT_FORM:
            openProfileForm(
              ProfileForms.FLIGHTS,
              frequentFlyerIATACodes,
              closeMessage,
              doneMessage
            );
            break;
          case IncomingMessageTypes.OPEN_HOTELS_PAYMENT_FORM:
            openProfileForm(ProfileForms.HOTELS, [], closeMessage, doneMessage);
            break;
          case IncomingMessageTypes.OPEN_PAYMENT_FORM:
            openProfileForm(
              ProfileForms.PROFILE_UPDATE_AGENT,
              [],
              closeMessage,
              doneMessage
            );
            break;
          default:
            openProfileForm(
              ProfileForms.FLIGHTS,
              [],
              closeMessage,
              doneMessage
            );
            break;
        }
      },

      onGoogleCalendarAccess: (accessFlowUrl) =>
        requestGoogleCalendarAccess(accessFlowUrl),

      onMicrosoftCalendarAccess: (accessFlowUrl) => {
        if (accessFlowUrl) {
          router.push(accessFlowUrl);
        }
      },

      reconnectAttempts: 3,

      reconnectInterval(lastAttemptNumber) {
        return lastAttemptNumber * 5000;
      },

      retryOnError: true,

      shouldReconnect(event) {
        console.debug("shouldReconnect", event, { reconnectAttempt });
        return (
          shouldAttemptReconnectCodes.includes(event.code) &&
          reconnectAttempt === WSReconnectState.ATTEMPTING
        );
      },
    });

  useEffect(() => {
    setConnectWebsocket(true);
  }, [initialMessage, setConnectWebsocket]);

  useEffect(() => {
    if (initialMessage && readyState === ReadyState.OPEN && !hasInitialized) {
      sendInitMessage(initialMessage);
      setHasInitialized(true);
    }
  }, [hasInitialized, initialMessage, readyState, sendInitMessage]);

  return { readyState, send };
}
