import { useCallback, useEffect, useRef } from "react";
import { useSet<PERSON>tom } from "jotai";
import {
  hideHeader<PERSON>tom,
  ottoReplyingAtom,
  userScrolledAtom,
} from "@/features/chat/store/chat";
import { chatRef<PERSON>tom } from "@/features/chat/store/components";
import useDebounce from "@/common/hooks/use-debounce";
import useThrottle from "@/common/hooks/use-throttle";
import { useStoreToggle } from "@/common/hooks/toggles";
import { enableUpdatedScrollBehaviourFlagAtom } from "@/features/user/store/feature-flags";

export default function useSecondaryEffects() {
  const { value: enableUpdatedScrollBehaviour } = useStoreToggle(
    enableUpdatedScrollBehaviourFlagAtom
  );

  const messagesWrapperRef = useRef<HTMLDivElement>(null);
  const scrollOffset = useRef<number>(0);
  const scrollTop = useRef<number>(0);
  const isProgramaticScroll = useRef<boolean>(false);
  const setChatRef = useSetAtom(chatRefAtom);
  const setHasUserScrolled = useSetAtom(userScrolledAtom);
  const setIsOttoReplying = useSetAtom(ottoReplyingAtom);
  const setHideHeader = useSetAtom(hideHeaderAtom);

  const handleScrollFlag = (element: HTMLDivElement) => {
    const isFullyScrolled =
      Math.abs(
        element.scrollHeight - element.clientHeight - element.scrollTop
      ) <= 10;

    isFullyScrolled
      ? element.removeAttribute("data-scrolled")
      : element.setAttribute("data-scrolled", "true");
  };

  const throttledScrollCallback = useThrottle(
    useCallback(
      (event) => {
        const element = event.target;

        handleScrollFlag(element);

        const scrollingUp = element.scrollTop < scrollTop.current;
        scrollTop.current = element.scrollTop;

        const shouldHideHeader =
          element.scrollHeight - element.offsetHeight > 10;

        !isProgramaticScroll.current &&
          shouldHideHeader &&
          setHideHeader(scrollingUp);
      },
      [setHideHeader]
    ),
    80
  );

  const debouncedScrollCallback = useDebounce(
    useCallback(
      (event) => {
        const element = event.target;
        handleScrollFlag(element);
        scrollOffset.current =
          element.scrollHeight - element.scrollTop - element.clientHeight;

        element.scrollTop < 10 && setHideHeader(false);
      },
      [setHideHeader]
    )
  );

  const debouncedResetFlag = useDebounce(
    useCallback(() => {
      isProgramaticScroll.current = false;
    }, []),
    100
  );

  const debouncedResizeCallback = useDebounce(
    useCallback(() => {
      const element = messagesWrapperRef?.current;

      if (!element) {
        return;
      }

      !enableUpdatedScrollBehaviour &&
        element.scrollTo(
          0,
          element.scrollHeight - element.clientHeight - scrollOffset.current
        );
      debouncedResetFlag();
    }, [debouncedResetFlag, enableUpdatedScrollBehaviour]),
    50
  );

  // Secondary effect to set a flag when the scroll on the chat element is at the bottom.
  // This is needed to trigger a scroll when the refresh element is being displayed under the cards rich control component.
  useEffect(() => {
    const element = messagesWrapperRef.current;

    if (!!element) {
      element.addEventListener("scroll", debouncedScrollCallback);
      element.addEventListener("scroll", throttledScrollCallback);

      const resizeObserver = new ResizeObserver(() => {
        isProgramaticScroll.current = true;
        debouncedResizeCallback();
      });
      !enableUpdatedScrollBehaviour && resizeObserver.observe(element, {});

      return () => {
        element.removeEventListener("scroll", debouncedScrollCallback);
        element.removeEventListener("scroll", throttledScrollCallback);
        !enableUpdatedScrollBehaviour && resizeObserver.unobserve(element);
      };
    }
  }, [
    debouncedResizeCallback,
    debouncedScrollCallback,
    enableUpdatedScrollBehaviour,
    messagesWrapperRef,
    setHasUserScrolled,
    throttledScrollCallback,
  ]);

  useEffect(() => {
    setChatRef(messagesWrapperRef);
  }, [setChatRef]);

  useEffect(() => setIsOttoReplying(true), [setIsOttoReplying]);

  return messagesWrapperRef;
}
