import { use<PERSON><PERSON>Value, use<PERSON>tom } from "jotai";
import { <PERSON><PERSON> } from "primereact/button";
import { InputSwitch } from "primereact/inputswitch";
import { UserRole } from "@/features/user/types/user";
import RightSidebars from "@/features/chat/types/right-sidebars";
import defaultTripName from "@/features/chat/constants/default-trip-name";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { useUserProfile } from "@/features/user/hooks/api";
import { useTripsList } from "@/common/hooks/api";
import {
  userSelectDebugAtom,
  spotnanaSearchFlagsAtom,
} from "@/features/chat/store/debugging";
import { currentTripAtom } from "@/features/chat/store/current-trip";
import Header from "../../../common/components/header";
import UserSelect from "@/features/admin/components/user-select";
import { useMemo, useState } from "react";
import { useOnboardingRedirect } from "../hooks/redirects";
import dayjs from "dayjs";
import PopupMenu from "@/common/components/popup-menu";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { useClient } from "@/common/hooks/use-client";
import { WithEllipsis } from "@/common/components/with-ellipsis";
import useTripDeleteConfirmation from "../hooks/use-trip-delete-confirmation";
import { OutgoingMessageTypes } from "../types/messages";
import useWebSocket from "@/features/chat/hooks/use-web-socket";

function formatTripDate(start: string, end?: string) {
  const startDate = dayjs.utc(start);
  const endDate = dayjs.utc(end);
  const spansMonths = startDate.month() !== endDate.month();

  const startDateFormatString = `D${spansMonths ? " MMMM" : ""}`;
  const formattedStart = startDate.isValid()
    ? startDate.format(startDateFormatString)
    : "";
  const formattedEnd = endDate.isValid() ? endDate.format("D MMMM") : "";

  if (!formattedStart && !formattedEnd) {
    return "";
  }

  return `${formattedStart}${formattedEnd ? `-${formattedEnd}` : ""}`;
}

export default function TripsHeader() {
  const { role } = useUserProfile();
  const currentTrip = useAtomValue(currentTripAtom);
  const userSelectDebug = useAtomValue(userSelectDebugAtom);
  const { data: trips, isLoading } = useTripsList();
  const { toggleRightSidebar } = useRightSidebarSwitch();
  const { onboardingRedirect } = useOnboardingRedirect();
  const [spotnanaSearchFlags, setSpotnanaSearchFlags] = useAtom(
    spotnanaSearchFlagsAtom
  );
  const { send } = useWebSocket(process.env.NEXT_PUBLIC_WS_URL as string);

  const [isTitleTruncated, setIsTitleTruncated] = useState(false);

  const { isMobile, isDesktop } = useViewportSize();
  const isClient = useClient();

  const {
    title,
    shortTitle,
    hasItinerary,
    longTitle,
    cancelTripAction,
    changeTripAction,
  } = useMemo(() => {
    const currentTripData = [
      ...(trips?.booked ?? []),
      ...(trips?.planned ?? []),
    ].find((trip) => trip.id === currentTrip);

    const shortTitle =
      currentTripData?.title !== defaultTripName
        ? `${currentTripData?.title || ""}${isDesktop ? " trip" : ""}`
        : "New trip";

    const date = !!currentTripData
      ? formatTripDate(currentTripData.date_start, currentTripData.date_end)
      : "";

    const longTitle = `${shortTitle}${date ? `, ${date}` : ""}`;

    return {
      title: currentTripData?.title,
      shortTitle,
      hasItinerary: !!currentTripData?.itinerary,
      longTitle,
      cancelTripAction: currentTripData?.cancel_trip_action,
      changeTripAction: currentTripData?.change_trip_action,
    };
  }, [currentTrip, isDesktop, trips?.booked, trips?.planned]);

  const { openDeleteDialog, DeleteDialog } = useTripDeleteConfirmation(
    currentTrip ?? 0,
    title ?? ""
  );

  const menuItems = useMemo(
    () => [
      {
        label: longTitle,
        visible: isTitleTruncated,
        className: "text-xs text-neutral-500 [&_a]:py-2",
      },
      {
        icon: <FontAwesomeIcon icon={["far", "map"]} />,
        label: "View itinerary",
        command: () => toggleRightSidebar(RightSidebars.ITINERARY),
        visible: isMobile,
      },
      {
        icon: <FontAwesomeIcon icon={["far", "receipt"]} />,
        label: "View receipt",
        visible: hasItinerary,
      },
      {
        icon: <FontAwesomeIcon icon={["far", "bookmark"]} />,
        label: "Save trip",
        visible: !hasItinerary,
      },
      {
        icon: <FontAwesomeIcon icon={["far", "pen"]} />,
        label: "Change trip",
        visible: hasItinerary && !!changeTripAction,
        command: () =>
          send({ text: changeTripAction, type: OutgoingMessageTypes.PROMPT }),
      },
      {
        icon: <FontAwesomeIcon icon={["far", "ban"]} />,
        label: "Cancel trip",
        className: "text-red-550",
        visible: hasItinerary && !!cancelTripAction,
        command: () =>
          send({ text: cancelTripAction, type: OutgoingMessageTypes.PROMPT }),
      },
      {
        icon: <FontAwesomeIcon icon={["far", "trash"]} />,
        label: "Delete trip",
        className: "text-red-550",
        visible: !hasItinerary,
        command: openDeleteDialog,
      },
    ],
    [
      longTitle,
      isTitleTruncated,
      isMobile,
      hasItinerary,
      changeTripAction,
      cancelTripAction,
      openDeleteDialog,
      toggleRightSidebar,
      send,
    ]
  );

  if (!isClient) {
    return null;
  }

  const enableSpotnanaSearchFlag =
    spotnanaSearchFlags?.[currentTrip || 0] || false;
  const toggleEnableSpotnanaSearchFlag = () => {
    if (currentTrip) {
      setSpotnanaSearchFlags((prev) => ({
        ...prev,
        [currentTrip]: !enableSpotnanaSearchFlag,
      }));
    }
  };

  return (
    <Header>
      <div className="flex gap-x-2 items-center w-full">
        <h1 className="flex-grow items-center gap-x-2 py-3 min-w-0 hidden md:flex">
          {isLoading ? "" : shortTitle}
        </h1>
        {role === UserRole.ADMIN && userSelectDebug && (
          <div className="flex items-center gap-x-2 mr-2">
            <span className="text-sm text-nowrap">Force Spotnana</span>
            <InputSwitch
              checked={enableSpotnanaSearchFlag}
              onChange={toggleEnableSpotnanaSearchFlag}
            />
          </div>
        )}

        <PopupMenu
          model={menuItems}
          containerClassName="md:hidden mx-auto max-w-72"
          pt={{
            action: { className: "justify-between" },
            label: { className: "order-first" },
          }}
          className="!left-0 !right-0 mx-auto"
        >
          <Button text className="hover:bg-transparent text-base w-full">
            <WithEllipsis
              id="trip-title"
              onTruncatedChange={setIsTitleTruncated}
              showTooltip={false}
            >
              {longTitle}
            </WithEllipsis>
            <FontAwesomeIcon icon={["far", "chevron-down"]} height={16} />
          </Button>
        </PopupMenu>

        {role === UserRole.ADMIN && userSelectDebug && (
          <UserSelect onClear={onboardingRedirect} />
        )}

        <div className="items-center gap-x-1 hidden md:flex">
          <PopupMenu model={menuItems}>
            <Button text rounded className="size-8">
              <FontAwesomeIcon
                icon={["far", "ellipsis-vertical"]}
                height={16}
              />
            </Button>
          </PopupMenu>
          <Button
            className="text-nowrap"
            onClick={() => toggleRightSidebar(RightSidebars.ITINERARY)}
            outlined
          >
            View itinerary
          </Button>
        </div>
      </div>
      {DeleteDialog}
    </Header>
  );
}
