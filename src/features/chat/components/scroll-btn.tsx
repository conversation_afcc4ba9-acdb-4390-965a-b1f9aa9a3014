import { useAtomValue } from "jotai";
import { But<PERSON> } from "primereact/button";
import { chatRef<PERSON>tom } from "@/features/chat/store/components";
import IconArrowSolid from "@/common/components/icons/arrow-solid";

export default function ScrollButton() {
  const chatElement = useAtomValue(chatRefAtom)?.current;

  const clickHandler = () => {
    if (!chatElement) {
      return;
    }

    chatElement.scrollTo({
      top: chatElement.scrollHeight,
      behavior: "smooth",
    });
  };

  return (
    <Button
      className="absolute bottom-20 hidden left-1/2 p-0 rounded-full shadow-lg -translate-x-1/2 peer-data-[scrolled=true]:block z-10 lg:bottom-24"
      onClick={clickHandler}
      plain
    >
      <IconArrowSolid className="rotate-180 [&_rect]:fill-white [&_path]:fill-neutral-900 dark:[&_rect]:fill-neutral-400  dark:[&_path]:fill-neutral-900" />
    </Button>
  );
}
