import React, { createContext, useContext, ReactNode, FC } from "react";
import { useRouter, useParams } from "next/navigation";
import { ROUTES } from "@/common/constants";
import { OutgoingMessageTypes } from "@/features/chat/types/messages";
import { useCreateQueryString } from "@/common/hooks/use-query-string";
import useSocketConnection from "@/features/chat/hooks/use-web-socket";
import { showSidebarAtom } from "@/common/store/sidebar";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { useStoreToggle } from "@/common/hooks/toggles";
import { useReset } from "@/features/chat/hooks/use-reset";
import { WebSocketReturnType } from "@/features/chat/types/web-socket";

interface WebSocketContextType extends WebSocketReturnType {
  sendMessageToTrip: (tripId: number, message: string) => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(
  undefined
);

interface WebSocketProviderProps {
  children: ReactNode;
}

export const WebSocketProvider: FC<WebSocketProviderProps> = ({ children }) => {
  const router = useRouter();
  const { slug: currentTripId } = useParams();
  const createQueryString = useCreateQueryString();

  const { resetChatHistory } = useReset();

  const { turnOff: closeSidebar } = useStoreToggle(showSidebarAtom);
  const { closeRightSidebar } = useRightSidebarSwitch();

  const webSocketConnection = useSocketConnection(
    process.env.NEXT_PUBLIC_WS_URL as string
  );

  const sendMessageToTrip = (tripId: number, message: string) => {
    closeRightSidebar();
    closeSidebar();
    if (tripId.toString() === currentTripId) {
      webSocketConnection.send({
        text: message,
        type: OutgoingMessageTypes.PROMPT,
      });
      return;
    }

    const queryString = createQueryString({
      message,
    });
    router.push(`${ROUTES.TRIPS}/${tripId}?${queryString}`);
    resetChatHistory();
  };

  const contextValue: WebSocketContextType = {
    ...webSocketConnection,
    sendMessageToTrip,
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocketContext = () => {
  const context = useContext(WebSocketContext);
  if (context == null) {
    throw new Error(
      "useWebSocketContext must be used within a WebSocketProvider"
    );
  }
  return context;
};
