import { atom } from "jotai";
import {
  RenderedMessage,
  SkeletonsMessageTypes,
} from "@/features/chat/types/messages";
import { WSReconnectState } from "@/features/chat/types/web-socket";
import { atomWithStorage } from "jotai/utils";
import { AccommodationSelections } from "../types/cards";
import { ACCOMMODATION_SELECTIONS_KEY, OPENING_MESSAGE_KEY } from "../constants/local-storage-keys";
import { CapabilityPromptAtom } from "@/features/chat/types/capability-prompts";

export const chatHistoryAtom = atom<RenderedMessage[]>([]);
export const chatHistoryReceivedAtom = atom(false);

export const ottoReplyingAtom = atom<boolean>(false);
export const messageRenderingAtom = atom<boolean>(false);
export const userScrolledAtom = atom<boolean>(false);
export const showSkeletonAtom = atom<SkeletonsMessageTypes | null>(null);
export const skeletonTextAtom = atom<string | string[] | null>(null);
export const accommodationSelectionsAtom =
  atomWithStorage<AccommodationSelections>(ACCOMMODATION_SELECTIONS_KEY, {});
export const openingMessageAtom = atomWithStorage<string | null>(OPENING_MESSAGE_KEY, null);
export const hideHeaderAtom = atom<boolean>(false);
export const stopDisabledAtom = atom<boolean>(false);
export const messageSelectionModeAtom = atom<boolean>(false);
export const selectedMessagesAtom = atom<number[]>([]);

export const reconnectAttemptAtom = atom<WSReconnectState>(
  WSReconnectState.NO_ATTEMPT
);
export const connectWebsocketAtom = atom(true);
export const suggestedCapabilityAtom = atom<CapabilityPromptAtom | null>(null);
export const calendarIntegrationModalAtom = atom<boolean>(false);
