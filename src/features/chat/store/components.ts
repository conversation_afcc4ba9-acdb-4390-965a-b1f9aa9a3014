import { RefObject } from "react";
import { atom } from "jotai";

export const chatRefAtom = atom<RefObject<HTMLDivElement> | null>(null);
export const chatHeaderAtom = atom<RefObject<HTMLDivElement> | null>(null);

/**
 * Atom used to read chat history scrollHeight before sending a user message from it.
 */
export const chatHistoryHeightAtom = atom<number>(0);
export const chatHistoryOverflowAtom = atom<number>(0);
export const chatHistoryRefAtom = atom<RefObject<HTMLDivElement> | null>(null);
export const sampleTripsRefAtom = atom<RefObject<HTMLDivElement> | null>(null);
