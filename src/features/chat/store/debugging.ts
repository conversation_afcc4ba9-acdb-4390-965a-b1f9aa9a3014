import {
  AGENTS_DEBUG_KEY,
  ENABLE_DELETE_MESSAGE_KEY,
  FLIGHT_CARDS_DEBUG_KEY,
  RAW_TOOL_OUTPUT_DEBUG_KEY,
  SPOTNANA_SEARCH_FLAGS_KEY,
  TRAVEL_CONTEXT_DEBUG_KEY,
  USER_SELECT_DEBUG_KEY,
} from "@/features/chat/constants/local-storage-keys";
import { RawToolOutput } from "@/features/chat/types/messages";
import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

export const enableDeleteMessageFlagAtom = atomWithStorage(
  ENABLE_DELETE_MESSAGE_KEY,
  false
);
export const agentsDebugAtom = atomWithStorage(AGENTS_DEBUG_KEY, false);
export const flightCardsDebugAtom = atomWithStorage(
  FLIGHT_CARDS_DEBUG_KEY,
  false
);
export const travelContextDebugAtom = atomWithStorage(
  TRAVEL_CONTEXT_DEBUG_KEY,
  false
);
export const rawToolOutputDebugAtom = atomWithStorage(
  RAW_TOOL_OUTPUT_DEBUG_KEY,
  false
);
export const userSelectDebugAtom = atomWithStorage(
  USER_SELECT_DEBUG_KEY,
  false
);
export const spotnanaSearchFlagsAtom = atomWithStorage(
  SPOTNANA_SEARCH_FLAGS_KEY,
  {} as { [key: number]: boolean }
);

export const travelContextAtom = atom<any>(null);
export const rawToolOutputAtom = atom<RawToolOutput | null>(null);
