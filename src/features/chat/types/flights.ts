import { StaticImageData } from "next/image";
import { Card } from "@/features/chat/types/cards";

/**
 * Flight stop data as it comes in a request response.
 */
export type FlightStop = {
  origin_code: string;
  origin_name: string;
  destination_code: string;
  destination_name: string;
  airline_code: string;
  airline_name: string;
  aircraft_iata_code: string;
  aircraft_name: string;
  flight_number: string;
  departure: string;
  departure_timezone: string;
  arrival: string;
  arrival_timezone: string;
};

/**
 * Flight segment data as it comes in a request response.
 */
export type FlightSegment = {
  flight_stops: FlightStop[];
  destination_code: string;
  destination_name: string;
  origin_code: string;
  origin_name: string;
};

export enum FlightCardTypes {
  CHANGE = "CHANGE",
  ONE_WAY = "ONE_WAY",
  ROUND_TRIP = "ROUND_TRIP",
}

/**
 * Flight data as it comes in a request response.
 */
export type FlightData = {
  action: string;
  cabin: string;
  cancellation_policy: string;
  exchange_policy: string;
  flight_segments: FlightSegment[];
  keyword: string;
  id: string;
  price: {
    amount: string;
    currency: string;
  };
  credits: {
    amount: string;
    currency: string;
  };
  net_price: {
    amount: string;
    currency: string;
  };
  recommendationReasons: string[];
  type?: FlightCardTypes;
  within_or_out_policy_reason?: string;
  within_policy?: boolean;
  seat_selection_policy?: string;
  boarding_policy?: string;
  booking_code?: string;
  total_distance_miles?: string;
  operating_airline_code?: string;
  operating_flight_number?: string;
  source?: string;
  is_red_eye?: boolean;
};

export type MappedFlightData = {
  userAction: string;
  airlineImg?: {
    alt: string;
    src: StaticImageData | null;
  };
  airlineName?: string;
  arrival: {
    airport: {
      name: string;
      code: string;
    };
    time: string;
    timezone: string;
  };
  cancellation_policy: string;
  exchange_policy: string;
  departure: {
    airport: {
      name: string;
      code: string;
    };
    time: string;
    timezone: string;
  };
  duration: string;
  flightNumbers: string;
  keyword: string;
  id: string;
  price?: { amount: string; currency: string };
  credits?: { amount: string; currency: string };
  net_price?: { amount: string; currency: string };
  cabin: string;
  recommendationReasons: string[];
  stops: string;
  type: FlightCardTypes | undefined;
  within_or_out_policy_reason?: string;
  within_policy?: boolean;
  is_red_eye?: boolean;
  seat_selection_policy?: string;
  boarding_policy?: string;
  booking_code?: string;
  total_distance_miles?: string;
  operating_airline_code?: string;
  operating_flight_number?: string;
  source?: string;
  arrivalDays?: number;
  middleStops?: FlightStop[];
};

export type FlightCard = Card<{
  data: FlightData;
  id: string;
}>;

export type FlightFareData = {
  action: string;
  id: string;
  option_title: string;
  options?: string[];
  price: string;
};

export type FlightFareCard = Card<FlightFareData>;

export type FlightDetails = MappedFlightData &
  Card<{
    flightLeg: string;
    flightStops: FlightStop[];
  }>;

export type LocalizedTimestamp = {
  timestamp: string;
  timezone: string;
};
