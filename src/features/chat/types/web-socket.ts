import {
  IncomingMessage,
  OutgoingMessage,
  PaymentFormTypes,
  RenderedMessage,
  SkeletonsMessageTypes,
} from "@/features/chat/types/messages";
import { IATACode } from "@/features/user/constants/profile-forms";
import { Options, ReadyState } from "react-use-websocket";

export type WebSocketSender = (
  message: OutgoingMessage | OutgoingMessage[],
  preventTimestamp?: boolean
) => void;

export type WebSocketReturnType = {
  send: WebSocketSender;
  sendInitMessage: (initMessage: OutgoingMessage) => void;
  sendSilentInitMessage: (initMessage: OutgoingMessage) => void;
  messagesList: RenderedMessage[];
  lastReceivedMessage: IncomingMessage;
  lastMessage: MessageEvent | null;
  status: string;
  readyState: ReadyState;
};

export type WebSocketOptions = Options & {
  /** Callback triggered when the incoming message contains an error from the back end.  */
  onErrorMessage?: VoidFunction;

  /** Callback triggered when the agent is waiting for an user input. */
  onExpectResponse?: VoidFunction;

  /** Callback triggered when the incoming message type is history. */
  onHistoryMessage?: (message: IncomingMessage) => void;

  /** Callback triggered when the itinerary data is stale and needs to be updated in the front end. */
  onItineraryUpdate?: VoidFunction;

  /** Callback triggered when the onboarding conversation is over. */
  onOnboardingComplete?: VoidFunction;

  /** Callback triggered when the user preferences are set and the onboarding continues to the future trips thread. */
  onPreferencesComplete?: VoidFunction;

  /** Callback triggered when a user profile update should be triggered. */
  onProfileUpdate?: VoidFunction;

  /** Callback triggered when the back end requests a page refresh. */
  onRefreshMessage?: VoidFunction;

  /** Callback triggered when the incoming message triggers the render of a skeleton component. */
  onSkeleton?: (skeleton: SkeletonsMessageTypes, text?: string | string[]) => void;

  /** Callback triggered when the incoming message has a travel context attached to it. */
  onTravelContext?: (context: object | undefined) => void;

  /** Callback triggered when the trips data is stale and should be updated in the front end. */
  onTripsUpdate?: VoidFunction;

  /** Callback triggered when the backend needs user profile/payment data to continue */
  onOpenPaymentForm?: (
    formType: PaymentFormTypes,
    { frequentFlyerIATACodes, formMessage }: { frequentFlyerIATACodes?: IATACode[], formMessage?: { doneMessage?: string, closeMessage?: string } }
  ) => void;

  /** Callback triggered when the Google calendar access is requested */
  onGoogleCalendarAccess?: (accessFlowUrl?: string) => void;

  /** Callback triggered when the Microsoft calendar access is requested */
  onMicrosoftCalendarAccess?: (accessFlowUrl?: string) => void;
};

/**
  Defines the state of the web socket reconnect attempts.

  NO_ATTEMPT: The connection closed with an error before a successful connection has been established meaning an attempt to reconnect should not be made.

  SHOULD_ATTEMPT: The connection was established successfully and an attempt to reconnect should be made in case the connection closes with specific error codes.

  ATTEMPTING: An attempt to reconnect is ongoing.
  
  FAILED: All the reconnection attempts have failed.
 */
export enum WSReconnectState {
  NO_ATTEMPT = -1,
  SHOULD_ATTEMPT = 0,
  ATTEMPTING = 1,
  FAILED = 2,
}
