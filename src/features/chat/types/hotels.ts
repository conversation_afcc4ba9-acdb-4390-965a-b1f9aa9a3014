import { CardProps } from "primereact/card";
import { Card } from "@/features/chat/types/cards";
import { RecommendationReasons } from "./recommendationReasons";

/**
 * Hotel data coming in api call response.
 */
export type HotelData = {
  amenities: string | string[];
  cancellation?: string;
  check_in_time: string;
  check_out_time: string;
  highlight: string;
  hotel: string;
  hotel_class?: number;
  id: string;
  img: {
    alt: string;
    src: string;
  };
  photos: string[];
  mapMarker: {
    address: string;
    coordinates: {
      lat: string;
      lng: string;
    };
    text: string;
  };
  payment?: string;
  rating: number;
  rating_description: string;
  recommendationReasons: RecommendationReasons;
  rooms: HotelRoomData[];
  within_or_out_policy_reason?: string;
  within_policy?: boolean;
};

export type HotelCard = Omit<HotelData, "action"> &
  CardProps &
  Card<{
    allHotels: HotelData[];
    searchLocation?: google.maps.LatLngLiteral;
  }>;

export type HotelRoomData = {
  action: string;
  id: string;
  image: {
    alt: string;
    src: string;
  };
  no_nights: number;
  option_title: string;
  options?: string[];
  price: number;
  priceExcludingFees: number;
  pricePerNight: number;
  room_photo: string;
  taxAndFees: number;
  within_policy?: boolean;
  within_or_out_policy_reason?: string;
  recommendation_reason: string;
  amenities: string | string[];
  cancellation_policy?: {
    type: CancellationType;
    display_policy: string;
  };
  payment_policy?: {
    display_label: string;
    policy: string;
  };
};

export type HotelRoomCard = CardProps & Card<HotelRoomData>;

export enum CancellationType {
  FREE_CANCELLATION = "free_cancellation",
  SPECIAL_CONDITIONS = "special_conditions",
  NON_REFUNDABLE = "non_refundable",
}
