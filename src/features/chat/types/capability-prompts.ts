export enum CapabilityPromptsTypes {
  COMPANY_TRAVEL_POLICY = "COMPANY_TRAVEL_POLICY",
  CALENDAR_INTEGRATION = "<PERSON><PERSON><PERSON><PERSON>_INTEGRATION",
  ENTRY_REQUIREMENTS = "ENTRY_REQUIREMENTS",
  FLIGHT_BOOKING = "FLIGHT_BOOKING",
  HOTEL_BOOKING = "HOTEL_BOOKING",
  FLIGHT_CHANGE = "FLIGHT_CHANGE",
  FLIGHT_CANCELLATION = "FLIGHT_CANCELLATION",
  HOTEL_CANCELLATION = "HOTEL_CANCELLATION",
  LOYALTY_PROGRAMS = "LOYALTY_PROGRAMS",
  OTHERS = "OTHERS",
}

export type CapabilityPromptAtom = {
  capability_type: CapabilityPromptsTypes;
  text: string;
};
