import Itinerary from "../../itineraries/types/itinerary";

export type Trip = {
  created_date: string;
  date_end: string;
  date_start: string;
  id: number;
  showWarn: boolean;
  title: string;
  itinerary?: Itinerary;
  is_deleted?: boolean; // Deprecated, use extra.isDeleted
  extra?: {
    isSampleTrip?: boolean;
    isDeleted?: boolean;
    [key: string]: any;
  };
  cancel_trip_action?: string;
  change_trip_action?: string;
};

export type TravelContext = {
  thread_id: number;
  addition_preferences: string;
  airline_brands_preferences: string;
  budget_preferences: string;
  home_airport: string;
  hotel_brands_preferences: string;
  is_flight_booked: boolean;
  is_hotel_booked: boolean;
  open_minded_flights_preferences: string;
  open_minded_hotels_preferences: string;
  trip_destination: string;
};

export type TripDetails = {
  itinerary: Itinerary;
  lastTimestamp: string;
  minMessageTimestampSeconds: number;
  travelContext: TravelContext;
  change_trip_action?: string;
  cancel_trip_action?: string;
};

export type TripsList = {
  booked: Trip[];
  onboarding_thread_id: number;
  past: Trip[];
  planned: Trip[];
  preferences_thread_id: number;
  saved: Trip[];
  travel_policy_thread_id: number;
};

export type ForeignTripsList = {
  planned: Trip[];
  booked: Trip[];
  saved: Trip[];
  past: Trip[];
};

export type TripSample = {
  title: string;
  description: string;
  prompt: string;
};

export type TripSamples = {
  trips: TripSample[];
};
