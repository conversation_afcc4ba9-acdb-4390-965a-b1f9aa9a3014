import { HotelData, HotelRoomData } from "./hotels";
import { FlightData, FlightFareData } from "./flights";
import { EventData } from "@/features/chat/types/events";
import { IATACode } from "@/features/user/constants/profile-forms";
import { ReactNode, RefObject } from "react";
import { User } from "@/features/user/types/user";
import { SuggestedPreferences } from "./suggested-preferences";
import {
  UnbookedFlightType,
  UnbookedHotelType,
} from "@/features/itineraries/types/unbooked";

export enum IncomingMessageTypes {
  FARES_SKELETON = "fares_skeleton",
  FLIGHTS_SKELETON = "flights_skeleton",
  ERROR = "error",
  GOOGLE_CALENDAR_ACCESS = "google_login",
  HISTORY = "history",
  HOTELS_SKELETON = "hotels_skeleton",
  ITINERARY_UPDATE = "itinerary_update",
  MICROSOFT_CALENDAR_ACCESS = "microsoft_login",
  PREFERENCES_COMPLETE = "preferences_complete",
  ONBOARDING_COMPLETE = "onboarding_chat_complete",
  PROFILE_UPDATE = "profile_update",
  PROMPT = "prompt",
  SEARCH_UPDATE = "search_update",
  AI_HUMAN = "ai_human",
  TRIPS_UPDATE = "trip_update",
  OPEN_FLIGHTS_PAYMENT_FORM = "open_flights_payment_form",
  OPEN_HOTELS_PAYMENT_FORM = "open_hotels_payment_form",
  OPEN_PAYMENT_FORM = "open_payment_form",
  SAMPLE_TRIPS_SKELETON = "sample_trips_skeleton",
  SUGGESTED_CAPABILITY = "suggested_capability",
  FORK_NEW_TRIP = "fork_new_trip",
  REDIRECT_TO_TRIP = "redirect_to_trip",

  // Async skeletons, should not block the chat input.
  FLIGHTS_SKELETON_ASYNC = "flights_skeleton_async",
  HOTELS_SKELETON_ASYNC = "hotels_skeleton_async",
}

export type PaymentFormTypes =
  | IncomingMessageTypes.OPEN_PAYMENT_FORM
  | IncomingMessageTypes.OPEN_FLIGHTS_PAYMENT_FORM
  | IncomingMessageTypes.OPEN_HOTELS_PAYMENT_FORM;

export enum OutgoingMessageTypes {
  AGENT_RESUME = "agent_resume",
  FUTURE_TRIPS_INIT = "future_trips_init",
  ONBOARDING_INIT = "onboarding_init",
  PREFERENCES_INIT = "preferences_init",
  PROMPT = "prompt",
  SILENT_PROMPT = "silent_prompt",
  TRIP_INIT = "trip_init",
  UPDATE = "update",
  TRAVEL_POLICY_INIT = "travel_policy_init",
  STOP = "stop",
}

export type SkeletonsMessageTypes =
  | IncomingMessageTypes.FARES_SKELETON
  | IncomingMessageTypes.FLIGHTS_SKELETON
  | IncomingMessageTypes.HOTELS_SKELETON
  | IncomingMessageTypes.FLIGHTS_SKELETON_ASYNC
  | IncomingMessageTypes.HOTELS_SKELETON_ASYNC
  | IncomingMessageTypes.SAMPLE_TRIPS_SKELETON;

export enum IncomingMessageStatus {
  ERROR = "error",
  SUCCESS = "success",
}

type HotelChoice = {
  name: string;
  rate_per_night: string;
  lat: number;
  long: number;
};

export type RawToolOutput = {
  flight_choices: string[];
  hotel_choices: HotelChoice[];
};

export type SampleTrips = {
  trips: {
    title: string;
    description: string;
    prompt: string;
  }[];
};

export type IncomingMessage = {
  messageId?: number;
  accommodations?: HotelData[];
  eventAttended?: {
    coordinates: {
      lat: string;
      lng: string;
    };
  };
  events?: EventData[];
  expectResponse?: boolean;
  expireTimestamp?: string;
  fares?: {
    cards: FlightFareData[];
    flights: string[];
  };
  flights?: FlightData[];
  isBotMessage?: boolean;
  isEnabled?: boolean;
  isLastSearch?: boolean;
  is_history?: boolean;
  isStopped?: boolean;
  location_lat_long?: {
    latitude?: number;
    longitude?: number;
    radius?: number;
  };
  messages?: Omit<IncomingMessage, "messages">[];
  partial?: boolean;
  rawToolOutput?: RawToolOutput;
  reason?: string[];
  refreshPage?: boolean;
  roomTypes?: HotelRoomData[];
  selected_accomodation?: UnbookedHotelType;
  selected_flight_itinerary?: UnbookedFlightType;
  status?: IncomingMessageStatus;
  text?: string | string[];
  textColor?: string;
  thread_id?: number;
  timestamp?: number | string;
  travel_context?: object;
  type: IncomingMessageTypes;
  missingFreqFlyerAirlines?: IATACode[] | null;
  loginInitUrl?: string;
  suggested_preferences?: SuggestedPreferences;
  lastTimestamp?: string;
  minMessageTimestampSeconds?: number;
  sampleTrips?: SampleTrips;
  capability_type?: string; // For suggested capabilities
  newTripId?: number; // For create_new_trip and redirect_to_trip messages
  lastHumanMessage?: string; // For redirect_to_trip messages
  formMessage?: {
    doneMessage?: string;
    closeMessage?: string;
  };
};

export type OutgoingMessage = {
  cardIds?: string[];
  clientTimezone?: string;
  isOnboarding?: boolean;
  messageId?: string;
  openingMessage?: string;
  replace?: boolean;
  text?: string | string[];
  timestamp?: string | undefined;
  tripId?: number;
  isMobile?: boolean;
  type: OutgoingMessageTypes;
  extra?: Record<string, any>; // Add extra field for additional data like flight IDs
};

export type PromptProps = IncomingMessage & {
  avatar?: ReactNode;
  currentOttoUser: User;
  forwardRef?: RefObject<HTMLDivElement>;
  messageId?: number;
  sendMessageToTrip?: (tripId: number, message: string) => void;
};

export type RenderedMessage = IncomingMessage | OutgoingMessage;
