export type FlightStop = {
  aircraft_name: string;
  airline_code: string;
  airline_name: string;
  arrival: string;
  arrival_timezone: string;
  departure: string;
  departure_timezone: string;
  destination_code: string;
  destination_name: string;
  flight_number: string;
  origin_code: string;
  origin_name: string;

  // Mock data from here
  seat?: string;
  confirmation?: string;
  cabin?: string;
};

export type FlightSegment = {
  destination_code: string;
  destination_name: string;
  flight_stops: FlightStop[];
  origin_code: string;
  origin_name: string;
};

export type Flight = {
  action: string;
  cancelled?: boolean;
  flight_segments: FlightSegment[];
  id: string;
  img: {
    alt: string;
    src: string;
  };
  price: {
    amount: string;
    currency: string;
  };
  recommendationReasons: string[];
  cancel_flight_leg_action?: string;
  change_flight_leg_action?: string;
  // Represents the order in the users choices as a 1-based index.
  selected?: number;
};
