import { HotelData, HotelRoomData } from "./hotels";

export type Card<T> = {
  className?: string;
  isActive?: boolean;
  isDisabled?: boolean;
  isExpired?: boolean;
  isSubmitted?: boolean;
  onSubmit: VoidFunction;
} & T;

export type AccommodationSelections = {
  [searchKey: string]: {
    selectedHotelId?: HotelData["id"];
    rooms?: (HotelRoomData &
      Pick<HotelData, "amenities" | "recommendationReasons">)[];
    selectedRoomId?: HotelRoomData["id"];
  };
};
