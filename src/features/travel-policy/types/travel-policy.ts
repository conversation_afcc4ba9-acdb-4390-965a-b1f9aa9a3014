export type TPHotelRateException = {
  city: string;
  rate: number;
};

export type TPFlightClassException = {
  title: string;
  duration_in_hours: number;
  flight_class: string;
};

export type TravelPolicyData = {
  hotel_policy?: {
    standard_rate: number;
    exceptions?: TPHotelRateException[];
  };
  flight_policy?: {
    default_class: string;
    exceptions?: TPFlightClassException[];
  };
};
