import WithSidebar from "@/common/components/sidebar/with-right-sidebar";
import { TravelPolicyDisplay } from "./travel-policy-display";
import { TravelPolicyData } from "../types/travel-policy";

type TravelPolicySidebarProps = {
  data?: TravelPolicyData;
  isOpen?: boolean;
  onClose: VoidFunction;
};

export default function TravelPolicySidebar({
  data,
  ...props
}: TravelPolicySidebarProps) {
  return (
    <WithSidebar header="Travel policy" {...props}>
      <div className="px-4 py-2">
        {data && <TravelPolicyDisplay travelPolicy={data} />}
      </div>
    </WithSidebar>
  );
}
