import { TravelPolicyData } from "../types/travel-policy";
import arrayHasElements from "@/common/utils/array-has-elements";
import clsx from "clsx";

type TravelPolicyDisplayProps = {
  travelPolicy: TravelPolicyData;
  className?: string;
};

export function TravelPolicyDisplay({
  travelPolicy,
  className,
}: TravelPolicyDisplayProps) {
  const { flight_policy, hotel_policy } = travelPolicy ?? {};
  const { default_class, exceptions: flightsExceptions } = flight_policy ?? {};
  const { exceptions: hotelsExceptions, standard_rate } = hotel_policy ?? {};

  return (
    <div className={className}>
      {default_class && (
        <>
          <h2 className="font-semibold mb-4 text-lg">Flights</h2>
          <p>Default class: {default_class}</p>
          {arrayHasElements(flightsExceptions) && (
            <>
              <p className="mt-2">Exceptions:</p>
              {flightsExceptions?.map((exception, index) => (
                <ul
                  className={clsx(
                    "border-l-2 border-primary-500 mt-2 pl-3 ml-4",
                    {
                      "mt-6": index > 0,
                    }
                  )}
                  key={index}
                >
                  {!!exception.title && <li>Title: {exception.title}</li>}
                  {!!exception.duration_in_hours && (
                    <li>{`Duration: ${exception.duration_in_hours} hour${
                      exception.duration_in_hours > 1 && "s"
                    }`}</li>
                  )}
                  {!!exception.flight_class && (
                    <li>Class: {exception.flight_class}</li>
                  )}
                </ul>
              ))}
            </>
          )}
        </>
      )}

      {standard_rate && (
        <>
          <h2 className="font-semibold mb-4 mt-12 text-lg">Hotels</h2>
          <p>Standard rate: ${standard_rate}</p>
          {arrayHasElements(hotelsExceptions) && (
            <>
              <p className="mt-2">Exceptions:</p>
              {hotelsExceptions?.map((exception, index) => (
                <ul
                  className={clsx(
                    "border-l-2 border-primary-500 mt-2 pl-3 ml-4",
                    {
                      "mt-6": index > 0,
                    }
                  )}
                  key={index}
                >
                  {!!exception.city && <li>City: {exception.city}</li>}
                  {!!exception.rate && <li>Rate: ${exception.rate}</li>}
                </ul>
              ))}
            </>
          )}
        </>
      )}
    </div>
  );
}
