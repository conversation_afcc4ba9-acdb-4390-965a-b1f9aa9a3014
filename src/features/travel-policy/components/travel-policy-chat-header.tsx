import clsx from "clsx";
import { <PERSON><PERSON> } from "primereact/button";
import RightSidebars from "@/features/chat/types/right-sidebars";
import { UserRole } from "@/features/user/types/user";
import { travelPolicyInitMessage } from "@/features/chat/constants/messages";
import { useUserProfile } from "@/features/user/hooks/api";
import useWebSocket from "@/features/chat/hooks/use-web-socket";
import useRightSidebarSwitch from "@/features/chat/hooks/use-right-sidebar-switch";
import { userSelectDebugAtom } from "@/features/chat/store/debugging";
import Header from "@/common/components/header";
import UserSelect from "@/features/admin/components/user-select";
import { useAtomValue } from "jotai";
import { useViewportSize } from "@/common/hooks/use-viewport-size";
import { SettingsHeader } from "@/common/components/settings-header";
import { useRedirectToLastTrip } from "@/features/chat/hooks/redirects";

export default function TravelPolicyChatHeader() {
  const { sendInitMessage } = useWebSocket(
    process.env.NEXT_PUBLIC_WS_URL as string
  );
  const userSelectDebug = useAtomValue(userSelectDebugAtom);
  const { isMobile } = useViewportSize();
  const { rightSidebar, switchTo } = useRightSidebarSwitch();
  const isSidebarOpen = rightSidebar === RightSidebars.TRAVEL_POLICY;
  const { role } = useUserProfile();
  const redirectToLastTrip = useRedirectToLastTrip();

  const onBack = () => {
    switchTo(null);
    redirectToLastTrip();
  };

  const ActionItems = (
    <>
      {role === UserRole.ADMIN && userSelectDebug && (
        <UserSelect
          onClear={() =>
            sendInitMessage({ ...travelPolicyInitMessage, isOnboarding: false })
          }
        />
      )}
      <Button
        className={clsx("gap-x-1 text-nowrap", {
          hidden: isSidebarOpen,
        })}
        onClick={() =>
          switchTo(isSidebarOpen ? null : RightSidebars.TRAVEL_POLICY)
        }
        outlined
      >
        {isMobile ? "View" : "Travel policy"}
      </Button>
    </>
  );

  if (isMobile) {
    return (
      <SettingsHeader title="Travel policy" onBack={onBack}>
        {ActionItems}
      </SettingsHeader>
    );
  }

  return (
    <Header>
      <h1 className="flex gap-x-1 items-center w-full overflow-hidden text-nowrap text-ellipsis">
        Travel policy
      </h1>
      {ActionItems}
    </Header>
  );
}
