export default function List({ items }: { items: string[] }) {
  return (
    <ul
      className="mt-4 text-lg lg:text-xl [&>li]:pl-5 [&>li]:relative [&>li]:mt-2 /
    [&>li]:before:content-[''] [&>li]:before:absolute [&>li]:before:top-3 /
    [&>li]:before:left-0 [&>li]:before:w-2 [&>li]:before:h-px [&>li]:before:bg-gray-900"
    >
      {items.map((item, index) => (
        <li key={index}>
          <p>{item}</p>
        </li>
      ))}
    </ul>
  );
}
