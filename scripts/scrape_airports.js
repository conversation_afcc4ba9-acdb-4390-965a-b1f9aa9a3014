// Save this as scrape_airports.js and run with: node scrape_airports.js
import fs from 'fs';
import fetch from 'node-fetch';
import * as cheerio from 'cheerio';
import { stringify } from 'csv-stringify/sync';

(async () => {
  const all = [];
  for (let pg = 1; pg <= 78; pg++) {
    const url = `https://www.aviationfanatic.com/ent_list.php?ent=7&pg=${pg}&NN_AP_Passengers=1&so=22`;
    console.log(`Fetching page ${pg}…`);
    const res = await fetch(url);
    const html = await res.text();
    // console.log(`Parsing page ${pg}…`);
    // console.log(`html: \n----\n${html}\n----`);
    const $ = cheerio.load(html);
    $('#ent_list_table tr').slice(1).each((_, row) => {
      const rank = $(row).find('th.right').text().trim()
      const cells = $(row).find('td').map((i, td) => $(td).text().trim()).get();
      // console.log(`cells: \n----\n${[rank, ...cells].join(',')}\n----`);
      // [Rank, ICAO, IATA, Location, Country, Airport, …]
      if (cells.length >= 6) {
        all.push({
          rank,
          icao: cells[0],
          airport_name: cells[1],
          iata: cells[3],
          country: cells[4],
          city_name: cells[6],
          airport_type: cells[7],
        });
      }
    });
    // console.log(`Found ${all.length} airports so far…`);
    // console.log(`All airports so far: \n----\n${JSON.stringify(all, null, 2)}\n----`);
    // be polite
    await new Promise(r => setTimeout(r, 1000));
  }
  // dedupe by IATA, keep lowest rank
  const byIata = {};
  all.forEach(a => {
    if (!byIata[a.iata] || a.rank < byIata[a.iata].rank) {
      byIata[a.iata] = a;
    }
  });
  const top1000_full = Object.values(byIata)
    .sort((a, b) => a.rank - b.rank)
    .slice(0, 1000)
    .map(({ rank, iata, city_name, country, airport_name, airport_type, icao }) => [rank, iata, city_name, country, airport_name, airport_type, icao]);
  // include header
  const csv_full = stringify([['Rank', 'Code (IATA)', 'City Name', 'Country', 'Airport', 'Type', 'Code (ICAO)'], ...top1000_full]);
  fs.writeFileSync('busiest_airports_top1000_full.csv', csv_full);
})();
