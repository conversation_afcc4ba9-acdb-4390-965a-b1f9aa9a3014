import plugin from "tailwindcss/plugin";
import type { Config } from "tailwindcss";
import defaultTheme from "tailwindcss/defaultTheme";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: "class",
  plugins: [
    plugin(({ matchUtilities, theme }) => {
      matchUtilities(
        {
          "text-shadow": (value) => ({ textShadow: value }),
        },
        {
          values: theme("textShadow"),
        }
      );
    }),
    plugin(({ matchUtilities, theme }) => {
      matchUtilities(
        {
          "animation-delay": (value) => {
            return {
              "animation-delay": value,
            };
          },
        },
        {
          values: theme("transitionDelay"),
        }
      );
    }),
  ],
  safelist: [
    "text-blue-500",
    "text-blue-600",
    "text-green-300",
    "text-green-500",
    "text-green-600",
    "text-orange-500",
    "text-purple-300",
    "text-purple-500",
    "text-purple-700",
    "text-purple-800",
    "text-red-500",
    "text-primary-300",
    "text-primary-400",
    "text-primary-500",
    "text-primary-600",
    "text-primary-900",
  ],
  theme: {
    container: {
      center: true,
    },
    extend: {
      animation: {
        backdrop: "backdrop-fade-in .3s ease-in-out forwards",
        "top-fade-in": "top-fade-in .5s ease-in both",
        "top-fade-in-md": "top-fade-in-md .45s ease-in both",
        "top-fade-in-lg": "top-fade-in-lg .4s ease-in both",
        "top-fade-in-xl": "top-fade-in-xl .35s ease-in both",
        "top-fade-in-2xl": "top-fade-in-2xl .3s ease-in both",
        "skeleton-bg": "skeleton-bg-keys 1.25s ease-in-out forwards infinite",
        "slide-up": "slide-up .3s ease-in-out forwards",
        "slide-down": "slide-down .3s ease-in-out forwards",
      },
      backgroundColor: {
        none: "transparent",
      },
      backgroundImage: {
        "gradient-login":
          "radial-gradient(circle farthest-side at 50% -10%, var(--tw-gradient-stops))",
        "gradient-radial":
          "radial-gradient(at -50% 25%, var(--tw-gradient-stops))",
        "skeleton-bg":
          "linear-gradient(to right, #EFEFEF 35%, #E1E1E1 50%, #EFEFEF 65%)",
        "skeleton-bg-dark":
          "linear-gradient(to right, #1D1C1D 35%, #011017 50%, #1D1C1D 65%)",
        "gradient-r": "linear-gradient(to right, var(--tw-gradient-stops))",
      },
      boxShadow: {
        md: "0 0 8px 0 rgba(0, 0, 0, .08)",
        "md-12%": "0 0 8px 0 rgba(0, 0, 0, .12)",
        lg: "0 0 16px 0 rgba(0, 0, 0, .16)",
        "md-light": "0 0 8px 0 rgba(255, 255, 255, .08)",
        "md-12%-light": "0 0 8px 0 rgba(255, 255, 255, .12)",
        "lg-light": "0 0 16px 0 rgba(255, 255, 255, .16)",
      },
      colors: {
        amber: {
          250: "#FEE59A",
          500: "#FFA602",
        },
        black: "#000",
        blue: {
          100: "#D3E7FF",
          400: "#5996FF",
          500: "#007FDB",
          600: "#013B61",
        },
        cyan: {
          950: "#013B61",
        },
        gray: {
          100: "#F3F5F5",
          300: "#D7D7CF",
          350: "#BCC0C2",
          400: "#8E918F",
          450: "#B3B3B3",
          475: "#DDDDDD",
          500: "#3A3D3C",
          800: "#2D2C2D",
          900: "#1D1C1D",
          950: "#011017",
          999: "#01080c",
        },
        neutral: {
          100: "#F3F3F3",
          150: "#EFEFEF",
          200: "#E3E3E3",
          250: "#E1E1E1",
          300: "#DADADA",
          350: "#D9D9D9",
          375: "#A9A8A8",
          400: "#CCC",
          450: "#AAA",
          460: "#999",
          475: "#888",
          490: "#787878",
          500: "#777",
          600: "#555",
          700: "#373737",
          750: "#8491A1",
          900: "#1E1E1E",
        },
        orange: {
          500: "#FC9B34",
        },
        primary: {
          300: "#D0F2F4",
          400: "#1384BA",
          450: "#28C8D0",
          500: "#08C0CC",
          550: "#06BECA",
          600: "#99F9E7",
          900: "#025279",
        },
        red: {
          500: "#D85040",
          550: "#FF303E",
          600: "#EC221F",
          650: "#DB2A1D",
        },
        secondary: {
          600: "#99F9E7",
        },
        teal: {
          100: "#C5FEF1",
        },
        green: {
          500: "#26BE31",
          550: "#2AA526",
        },
        lime: {
          100: "#E5FAD4",
        },
        sky: {
          850: "#0A4A73",
        },
      },
      transitionDelay: {
        "350": "350ms",
      },
      flexBasis: {
        "4.5": "1.125rem",
      },
      fontSize: {
        "2xs": "0.625rem",
        "2.5xl": "1.75rem",
        "3xl": ["2rem", "3.375rem"],
        "4xl": ["2.5rem", "3rem"],
        "6xl": ["4rem", "4.5rem"],
        "7xl": "5rem",
      },
      keyframes: {
        "backdrop-fade-in": {
          "0%": { opacity: "0" },
          "100%": { opacity: ".32" },
        },
        "top-fade-in": {
          "0%": { opacity: "0", transform: "translateY(-20px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        "top-fade-in-md": {
          "0%": { opacity: "0", transform: "translateY(-30px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        "top-fade-in-lg": {
          "0%": { opacity: "0", transform: "translateY(-40px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        "top-fade-in-xl": {
          "0%": { opacity: "0", transform: "translateY(-50px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        "top-fade-in-2xl": {
          "0%": { opacity: "0", transform: "translateY(-60px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        "skeleton-bg-keys": {
          "0%": { backgroundPosition: "0% 50%" },
          "100%": { backgroundPosition: "100% 50%" },
        },
        "slide-up": {
          "0%": { transform: "translateY(100%)" },
          "100%": { transform: "translateY(0)" },
        },
        "slide-down": {
          "0%": { transform: "translateY(0)" },
          "100%": { transform: "translateY(100%)" },
        },
      },
      lineHeight: {
        relaxed: "1.6875",
        3.5: "0.875rem",
        4.5: "1.125rem",
        5.5: "1.375rem",
      },
      rotate: {
        "30": "30deg",
      },
      screens: {
        xs: "480px",
        lg: "992px",
        xl: "1256px",
        "3xl": "1860px",

        // Workaround fix to have custom media queries for tailwind v3
        // https://github.com/tailwindlabs/tailwindcss/issues/13022
        "max-sm": {
          raw: `not all and (min-width: ${defaultTheme.screens.sm})`,
        },
        "max-md": {
          raw: `not all and (min-width: ${defaultTheme.screens.md})`,
        },
        "max-lg": {
          raw: `not all and (min-width: ${defaultTheme.screens.lg})`,
        },
        "max-xl": {
          raw: `not all and (min-width: ${defaultTheme.screens.xl})`,
        },
        "max-2xl": {
          raw: `not all and (min-width: ${defaultTheme.screens["2xl"]})`,
        },

        tall: { raw: "(min-height: 800px)" },
      },
      spacing: {
        4.5: "1.125rem",
        4.75: "1.1875rem",
        5.5: "1.375rem",
        6.5: "1.625rem",
        10.5: "2.625rem",
        13: "3.25rem",
        13.5: "3.375rem",
        15: "3.75rem",
        17: "4.25rem",
        18: "4.5rem",
        21: "5.25rem",
        22: "5.5rem",
        25: "6.25rem",
        30: "7.5rem",
        67.5: "16.875rem",
        86: "21.5rem",
        100: "25rem",
        120: "30rem",
        142.5: "35.625rem",
        150: "37.5rem",
        160: "40rem",
        164: "41rem",
        218: "54.5rem",
        240: "60rem",
        350: "87.5rem",
      },
      textShadow: {
        DEFAULT: "0 0 12px var(--tw-shadow-color)",
      },
      transitionProperty: {
        "width-transform": "width, transform",
      },
      zIndex: {
        "1": "1",
      },
    },
  },
};
export default config;
