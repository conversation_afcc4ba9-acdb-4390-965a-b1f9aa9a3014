name: Production Rollback
run-name: Rollback production ${{ inputs.job-name }} to version ${{ github.ref_name }}

on:
  workflow_dispatch:

permissions:
  id-token: write
  packages: write
  contents: read

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  frontend:
    name: Rollback Frontend
    runs-on: ubuntu-latest

    steps:
      - name: Checking tag...
        run: |
          if ! [[ ${{ github.ref_name }} =~ ^v([0-9]+)\.([0-9]+)\.([0-9]+)-rc([0-9]+)$ ]]; then
            echo "Incorrect tag, exiting."
            exit 1
          else
            echo "Correct tag, continuing."
          fi

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::891377138838:role/GithubActionsOttoFrontend-live
          aws-region: us-east-2

      - name: Login to Amazon ECR
        id: ecr-login
        uses: aws-actions/amazon-ecr-login@v1

      - name: Get Tags for Frontend
        id: metadata
        uses: docker/metadata-action@v3
        with:
          images: ${{ steps.ecr-login.outputs.registry }}/otto-frontend-live
          tags: type=raw,value=${{ github.ref_name }}
  
      - name: Get task definition
        id: get-task-definition
        run: |
          aws ecs describe-task-definition --task-definition otto-frontend-live --query taskDefinition > task-definition.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: otto-frontend-live
          image: ${{ steps.metadata.outputs.tags }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: otto-frontend-live
          cluster: otto-apps-live
          wait-for-service-stability: true
