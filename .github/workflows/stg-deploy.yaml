name: Staging Deploy Frontend
run-name: Frontend - Staging Build & Deploy - Version ${{ github.ref_name }}

on:
  push:
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+-rc[0-9]+-stg"
      - "stg-manual-release-*"

permissions:
  id-token: write
  packages: write
  contents: read

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  build-frontend:
    name: Build and Push Image
    runs-on: ubuntu-latest
    environment: stg

    steps:
      - uses: actions/checkout@v4

      - name: Cache node modules
        uses: actions/cache@v3
        env:
          cache-name: cache-node-modules-stg
        with:
          path: |
            **/node_modules
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::891377138838:role/GithubActionsOttoFrontend-stg
          aws-region: us-east-2

      - name: Login to Amazon ECR
        id: ecr-login
        uses: aws-actions/amazon-ecr-login@v1

      - name: Get Tags for Image
        id: metadata
        uses: docker/metadata-action@v3
        with:
          images: ${{ steps.ecr-login.outputs.registry }}/otto-frontend-stg
          tags: |
            type=raw,value=${{ github.sha }}
            type=raw,value=latest

      - name: Build app
        env:
          GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
          GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
          NEXT_PUBLIC_VGS_VAULT_ID: ${{ secrets.NEXT_PUBLIC_VGS_VAULT_ID }}
          NEXT_PUBLIC_VGS_ENVIRONMENT: ${{ secrets.NEXT_PUBLIC_VGS_ENVIRONMENT }}
          NEXT_PUBLIC_VGS_CNAME: ${{ secrets.NEXT_PUBLIC_VGS_CNAME }}
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          NEXT_PUBLIC_SENTRY_DSN: ${{ secrets.NEXT_PUBLIC_SENTRY_DSN }}
          NEXT_PUBLIC_API_URL: ${{ vars.NEXT_PUBLIC_API_URL }}
          NEXT_PUBLIC_WS_URL: ${{ vars.NEXT_PUBLIC_WS_URL }}
          NEXT_PUBLIC_MAP_ID: ${{ vars.NEXT_PUBLIC_MAP_STYLE_ID }}
          NEXT_PUBLIC_APP_ENV: ${{ vars.NEXT_PUBLIC_APP_ENV }}
          NEXT_PUBLIC_GTM_ID: ${{ vars.NEXT_PUBLIC_GTM_ID }}
          NEXT_PUBLIC_SENTRY_ENVIRONMENT: ${{ vars.NEXT_PUBLIC_SENTRY_ENVIRONMENT }}
          NEXT_PUBLIC_PARENT_DOMAIN: ${{ vars.PARENT_DOMAIN }}
          FONTAWESOME_PRO_AUTHTOKEN: ${{ secrets.FONTAWESOME_PRO_AUTHTOKEN }}
        run: |
          npm config set "//npm.fontawesome.com/:_authToken" ${{ secrets.FONTAWESOME_PRO_AUTHTOKEN }}
          npm install
          npm run build

      - name: Push image
        id: build-image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          file: "Dockerfile"
          tags: ${{ steps.metadata.outputs.tags }}
          cache-to: type=gha,mode=max
          cache-from: type=gha

  deploy-apps:
    name: Deploy App
    runs-on: ubuntu-latest
    needs: [build-frontend]

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::891377138838:role/GithubActionsOttoFrontend-stg
          aws-region: us-east-2

      - name: Login to Amazon ECR
        id: ecr-login
        uses: aws-actions/amazon-ecr-login@v1

      - name: Get Tags for Frontend
        id: metadata
        uses: docker/metadata-action@v3
        with:
          images: ${{ steps.ecr-login.outputs.registry }}/otto-frontend-stg
          tags: type=raw,value=${{ github.sha }}

      - name: Get task definition
        id: get-task-definition
        run: |
          aws ecs describe-task-definition --task-definition otto-frontend-stg --query taskDefinition > task-definition.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: otto-frontend-stg
          image: ${{ steps.metadata.outputs.tags }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: otto-frontend-stg
          cluster: otto-apps-stg
          wait-for-service-stability: true
