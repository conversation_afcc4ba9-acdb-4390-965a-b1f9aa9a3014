name: Staging Trigger
run-name: Staging Deployment - Commit ${{ github.sha }}

on:
  schedule:
    # Runs every day at 12:00 UTC (4 AM PST / 5 AM PDT, 14:00 EET / 15:00 EEST)
    - cron: '0 12 * * *'
  workflow_dispatch:
    inputs:
      commit:
        description: "Commit to deploy to staging"
        required: false
        default: "HEAD"

permissions:
  packages: write
  contents: write

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  increment-version:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository with full history
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.WORKFLOW_PAT }}

      - name: Determine commit and branch
        id: determine-ref
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" && "${{ github.event.inputs.commit }}" != "" ]]; then
            TARGET_COMMIT="${{ github.event.inputs.commit }}"
            TARGET_BRANCH="${{ github.ref_name }}"
            MANUAL_TRIGGER=true
          else
            TARGET_COMMIT="${{ github.sha }}"
            TARGET_BRANCH="development"
            MANUAL_TRIGGER=false
          fi

          echo "TARGET_COMMIT=$TARGET_COMMIT" >> $GITHUB_ENV
          echo "TARGET_BRANCH=$TARGET_BRANCH" >> $GITHUB_ENV
          echo "MANUAL_TRIGGER=$MANUAL_TRIGGER" >> $GITHUB_ENV
          echo "Using commit: $TARGET_COMMIT from branch: $TARGET_BRANCH"

      - name: Switch to target branch
        uses: actions/checkout@v3
        with:
          ref: ${{ env.TARGET_BRANCH }}
          fetch-depth: 0
          tags: true
          token: ${{ secrets.WORKFLOW_PAT }}

      - name: Set up Git
        run: |
          git config --global user.name '${{ github.actor }}'
          git config --global user.email '${{ github.actor }}@users.noreply.github.com'

      - name: Determine staging tag
        id: determine-tag
        run: |
          if [[ "$MANUAL_TRIGGER" == "true" ]]; then
            # Construct a tag based on branch and commit SHA
            SANITIZED_BRANCH=$(echo "$TARGET_BRANCH" | tr '/' '-')
            NEW_TAG="stg-manual-release-${SANITIZED_BRANCH}-${TARGET_COMMIT:0:7}"
          else
            # Find latest `-rcX` tag for cron jobs
            DEV_TAGGING_PATTERN='v[0-9]+\.[0-9]+\.[0-9]+-rc[0-9]+$'
            LATEST_DEV_TAG=$(git tag --sort=-v:refname | grep -E "$DEV_TAGGING_PATTERN" | head -n1)
            if [[ -z "$LATEST_DEV_TAG" ]]; then
              echo "No matching development tag found. Available tags:"
              git tag --list
              exit 1
            fi
            NEW_TAG="${LATEST_DEV_TAG}-stg"
          fi

          echo "NEW_TAG=$NEW_TAG" >> $GITHUB_ENV
          echo "Generated staging tag: $NEW_TAG"

      - name: Push staging tag
        run: |
          echo "Pushing new tag: $NEW_TAG"
          # Check if the tag already exists
          if git rev-parse "$NEW_TAG" >/dev/null 2>&1; then
            echo "Tag $NEW_TAG already exists. Deleting it..."
            git push --delete origin "$NEW_TAG" || echo "Tag deletion failed, possibly because it doesn't exist remotely."
            git tag -d "$NEW_TAG"
          fi

          # Create and push the new tag
          git tag "$NEW_TAG"
          git push origin "$NEW_TAG"
