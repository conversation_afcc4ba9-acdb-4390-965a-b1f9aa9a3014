This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

### 1. Clone the project

### 2. Run `npm config`

Run `npm config set "//npm.fontawesome.com/:_authToken" <fontawesome_token>`. Get the token from aws secret.

### 3. Run `npm install`

Run `npm install` from the root of the project (same level as the package.json).

### 4. Create `.env.local`

Create a file named `.env.local` in the root of the project.

Add values for the following keys:

```bash
GOOGLE_MAPS_API_KEY=google_maps_api_key
NEXT_PUBLIC_API_URL=https://api_url
NEXT_PUBLIC_WS_URL=wss://websocket_url
NEXT_PUBLIC_MAP_ID=map_id_from_google_console
GOOGLE_CLIENT_ID=google_client_id
NEXT_PUBLIC_VGS_VAULT_ID=vault_id
NEXT_PUBLIC_VGS_ENVIRONMENT=environment
NEXT_PUBLIC_VGS_CNAME=domain
NEXT_PUBLIC_PARENT_DOMAIN=dev.otto-demo.com
```

### 5. Edit hosts file

In order to have the authentication/authorization work correctly you need to access the site from the same domain the server is setting the cookies for.  
To do that on your local machine you need to add a line of text in your hosts file.

#### Linux-based systems (Mac, Ubuntu, Debian)

The hosts file is located at `/etc/hosts`.  
You can edit this file using a terminal text editor such as nano using the following command:  
`sudo nano /etc/hosts`  
Alternatively you can use a graphical text editor such as gedit using the following command:  
`gksu gedit /etc/hosts`

#### Windows

The hosts file is located at `C:\Windows\System32\drivers\etc\hosts`

In order to edit this file you need to open a text editor, such as Notepad, with administrator privileges.

To do this you can follow these steps:

1. Press the start button
2. Type Notepad
3. Right click to open context menu and click on Run as administrator (Ctrl + Shift + Enter)

Now you need to add at the end of the file the following line:

`127.0.0.1   local.dev.otto-demo.com`

### 6. Start the dev server

To start the dev server run `npm run dev` in the root of the project.  
The application will be served through port 3000 at the address added in the hosts file after the previous step.  
Now you can access the local solution at the following address: [`https://local.dev.otto-demo.com:3000`](https://local.dev.otto-demo.com:3000)

_Note: you must access the solution using https but you don't need to install an SSL certificate as NextJS supports generating self-signed certificates for use with local development out-of-the-box_

## FAQ

If you see `Error downloading mkcert: TypeError: fetch failed`
can try the answer [here](https://stackoverflow.com/questions/74165121/next-js-fetch-request-gives-error-typeerror-fetch-failed), basically run
`NODE_TLS_REJECT_UNAUTHORIZED=0 npm run dev`
